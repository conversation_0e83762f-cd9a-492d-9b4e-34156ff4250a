{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// Types for API responses\nexport interface Patient {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  dateOfBirth?: string;\n  gender?: string;\n  address?: string;\n  assessments: Assessment[];\n  invitations: PatientInvitation[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Assessment {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  age: string;\n  bmi: string;\n  waistCircumference: string;\n  physicalActivity: string;\n  vegetableConsumption: string;\n  bloodPressureMedication: string;\n  highGlucoseHistory: string;\n  familyDiabetesHistory: string;\n  riskScore: number;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  notes?: string;\n  assessedBy?: string;\n  assessmentDate: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PatientInvitation {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  email: string;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  riskScore: number;\n  invitationSent: boolean;\n  sentAt?: string;\n  accountCreated: boolean;\n  accountCreatedAt?: string;\n  emailOpened: boolean;\n  emailOpenedAt?: string;\n  appDownloaded: boolean;\n  appDownloadedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AdminSettings {\n  id: string;\n  smtpHost?: string;\n  smtpPort?: string;\n  smtpUser?: string;\n  fromEmail?: string;\n  fromName?: string;\n  appName?: string;\n  clinicName?: string;\n  clinicAddress?: string;\n  clinicPhone?: string;\n  iosAppUrl?: string;\n  androidAppUrl?: string;\n  webAppUrl?: string;\n  emailNotifications: boolean;\n  assessmentAlerts: boolean;\n  highRiskAlerts: boolean;\n  dailyReports: boolean;\n  weeklyReports: boolean;\n  monthlyReports: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RiskGroupStats {\n  totalAssessments: number;\n  averageScore: number;\n  riskDistribution: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    percentage: number;\n  }>;\n  monthlyTrends: Array<{\n    date: string;\n    assessments: number;\n    averageScore: number;\n  }>;\n}\n\nexport interface SummaryReport {\n  summary: {\n    totalAssessments: number;\n    highRiskPatients: number;\n    invitationsSent: number;\n    accountsCreated: number;\n  };\n  riskGroupBreakdown: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    averageScore: number;\n  }>;\n  recentAssessments: Assessment[];\n  dateRange: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\ninterface PaginatedResponse<T> {\n  success: boolean;\n  data: {\n    [key: string]: T[];\n    pagination: {\n      page: number;\n      limit: number;\n      total: number;\n      pages: number;\n    };\n  };\n}\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`;\n  \n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n    \n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    \n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n}\n\n// Assessment API functions\nexport const assessmentApi = {\n  // Create new assessment\n  create: async (assessmentData: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone?: string;\n    age: string;\n    bmi: string;\n    waistCircumference: string;\n    physicalActivity: string;\n    vegetableConsumption: string;\n    bloodPressureMedication: string;\n    highGlucoseHistory: string;\n    familyDiabetesHistory: string;\n    notes?: string;\n    assessedBy?: string;\n  }): Promise<ApiResponse<{ assessment: Assessment; riskScore: number; riskGroup: string }>> => {\n    return apiRequest('/admin/assessments', {\n      method: 'POST',\n      body: JSON.stringify(assessmentData),\n    });\n  },\n\n  // Get all assessments\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    riskGroup?: 'A' | 'B' | 'C' | 'D';\n  }): Promise<PaginatedResponse<Assessment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.riskGroup) searchParams.append('riskGroup', params.riskGroup);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/admin/assessments${query ? `?${query}` : ''}`);\n  },\n};\n\n// Patient API functions\nexport const patientApi = {\n  // Get all patients\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n  }): Promise<PaginatedResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/admin/patients${query ? `?${query}` : ''}`);\n  },\n};\n\n// Invitation API functions\nexport const invitationApi = {\n  // Send invitation\n  send: async (invitationData: {\n    patientId: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    riskGroup: 'A' | 'B' | 'C' | 'D';\n    riskScore: number;\n  }): Promise<ApiResponse<{ message: string }>> => {\n    return apiRequest('/admin/send-invitation', {\n      method: 'POST',\n      body: JSON.stringify(invitationData),\n    });\n  },\n};\n\n// Risk Groups API functions\nexport const riskGroupApi = {\n  // Get risk group statistics\n  getStats: async (): Promise<ApiResponse<RiskGroupStats>> => {\n    return apiRequest('/admin/risk-groups/stats');\n  },\n};\n\n// Reports API functions\nexport const reportsApi = {\n  // Get summary report\n  getSummary: async (params?: {\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<SummaryReport>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/admin/reports/summary${query ? `?${query}` : ''}`);\n  },\n};\n\n// Settings API functions\nexport const settingsApi = {\n  // Get settings\n  get: async (): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings');\n  },\n\n  // Update settings\n  update: async (settings: Partial<AdminSettings>): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings', {\n      method: 'PUT',\n      body: JSON.stringify(settings),\n    });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,eAAe,iEAAmC;AAyIxD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,eAAe,UAAU;IAExC,MAAM,SAAsB;QAC1B,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,QAAQ,OAAO;QAgBb,OAAO,WAAW,sBAAsB;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,sBAAsB;IACtB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnE;AACF;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAChE;AACF;AAGO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,MAAM,OAAO;QAQX,OAAO,WAAW,0BAA0B;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,UAAU;QACR,OAAO,WAAW;IACpB;AACF;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,YAAY,OAAO;QAIjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACvE;AACF;AAGO,MAAM,cAAc;IACzB,eAAe;IACf,KAAK;QACH,OAAO,WAAW;IACpB;IAEA,kBAAkB;IAClB,QAAQ,OAAO;QACb,OAAO,WAAW,mBAAmB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/app/dashboard/patients/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Input } from '@/components/ui/input';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { ArrowLeft, Search, Mail, Eye, Filter } from 'lucide-react';\nimport Link from 'next/link';\nimport { patientApi, invitationApi, type Patient } from '@/lib/api';\nimport { toast } from 'sonner';\n\n\n\nconst getRiskGroupColor = (group: string) => {\n  switch (group) {\n    case 'A': return 'default';\n    case 'B': return 'secondary';\n    case 'C': return 'destructive';\n    case 'D': return 'destructive';\n    default: return 'default';\n  }\n};\n\nconst getRiskGroupLabel = (group: string) => {\n  switch (group) {\n    case 'A': return 'Low Risk';\n    case 'B': return 'Slightly Elevated';\n    case 'C': return 'Moderate Risk';\n    case 'D': return 'High Risk';\n    default: return 'Unknown';\n  }\n};\n\nexport default function PatientsPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);\n  const [filterGroup, setFilterGroup] = useState('all');\n  const [patients, setPatients] = useState<Patient[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchPatients = async () => {\n      try {\n        setLoading(true);\n        const response = await patientApi.getAll();\n        if (response.success) {\n          setPatients(response.data.patients || []);\n        }\n      } catch (error) {\n        console.error('Error fetching patients:', error);\n        toast.error('Failed to load patients');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPatients();\n  }, []);\n\n  const filteredPatients = patients.filter(patient => {\n    const matchesSearch =\n      patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      patient.email.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const latestAssessment = patient.assessments?.[0];\n    const matchesFilter = filterGroup === 'all' || latestAssessment?.riskGroup === filterGroup;\n\n    return matchesSearch && matchesFilter;\n  });\n\n  const sendInvitation = async (patient: Patient) => {\n    try {\n      const latestAssessment = patient.assessments?.[0];\n      if (!latestAssessment) {\n        toast.error('No assessment found for this patient');\n        return;\n      }\n\n      const response = await invitationApi.send({\n        patientId: patient.id,\n        email: patient.email,\n        firstName: patient.firstName,\n        lastName: patient.lastName,\n        riskGroup: latestAssessment.riskGroup,\n        riskScore: latestAssessment.riskScore,\n      });\n\n      if (response.success) {\n        toast.success(`Invitation sent to ${patient.email}!`);\n        // Refresh patients data\n        const refreshResponse = await patientApi.getAll();\n        if (refreshResponse.success) {\n          setPatients(refreshResponse.data.patients || []);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to send invitation:', error);\n      toast.error('Failed to send invitation');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/dashboard\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to Dashboard\n            </Link>\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Patient Management</h1>\n            <p className=\"text-gray-600 mt-2\">\n              View and manage all assessed patients\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filter */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Search & Filter</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex space-x-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search by name or email...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button\n                variant={filterGroup === 'all' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterGroup('all')}\n              >\n                All\n              </Button>\n              <Button\n                variant={filterGroup === 'A' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterGroup('A')}\n              >\n                Group A\n              </Button>\n              <Button\n                variant={filterGroup === 'B' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterGroup('B')}\n              >\n                Group B\n              </Button>\n              <Button\n                variant={filterGroup === 'C' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterGroup('C')}\n              >\n                Group C\n              </Button>\n              <Button\n                variant={filterGroup === 'D' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterGroup('D')}\n              >\n                Group D\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Patients Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Patients ({filteredPatients.length})</CardTitle>\n          <CardDescription>\n            All patients who have completed the diabetes risk assessment\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Patient</TableHead>\n                <TableHead>Contact</TableHead>\n                <TableHead>Risk Score</TableHead>\n                <TableHead>Risk Group</TableHead>\n                <TableHead>Assessment Date</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {loading ? (\n                <TableRow>\n                  <TableCell colSpan={7} className=\"text-center py-8\">\n                    <div className=\"animate-pulse\">Loading patients...</div>\n                  </TableCell>\n                </TableRow>\n              ) : filteredPatients.length === 0 ? (\n                <TableRow>\n                  <TableCell colSpan={7} className=\"text-center py-8\">\n                    No patients found\n                  </TableCell>\n                </TableRow>\n              ) : (\n                filteredPatients.map((patient) => {\n                  const latestAssessment = patient.assessments?.[0];\n                  const latestInvitation = patient.invitations?.[0];\n\n                  return (\n                    <TableRow key={patient.id}>\n                      <TableCell>\n                        <div>\n                          <p className=\"font-medium\">{patient.firstName} {patient.lastName}</p>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          <p>{patient.email}</p>\n                          <p className=\"text-gray-500\">{patient.phone || 'No phone'}</p>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <span className=\"font-mono text-lg\">\n                          {latestAssessment?.riskScore || 'N/A'}\n                        </span>\n                      </TableCell>\n                      <TableCell>\n                        {latestAssessment ? (\n                          <Badge variant={getRiskGroupColor(latestAssessment.riskGroup) as any}>\n                            Group {latestAssessment.riskGroup} - {getRiskGroupLabel(latestAssessment.riskGroup)}\n                          </Badge>\n                        ) : (\n                          <span className=\"text-gray-500\">No assessment</span>\n                        )}\n                      </TableCell>\n                      <TableCell>\n                        {latestAssessment ?\n                          new Date(latestAssessment.assessmentDate).toLocaleDateString() :\n                          'N/A'\n                        }\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"space-y-1\">\n                          <div className=\"flex items-center space-x-2\">\n                            <div className={`w-2 h-2 rounded-full ${latestInvitation?.invitationSent ? 'bg-green-500' : 'bg-red-500'}`} />\n                            <span className=\"text-sm\">{latestInvitation?.invitationSent ? 'Invited' : 'Not Invited'}</span>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <div className={`w-2 h-2 rounded-full ${latestInvitation?.accountCreated ? 'bg-green-500' : 'bg-gray-300'}`} />\n                            <span className=\"text-sm\">{latestInvitation?.accountCreated ? 'Account Created' : 'No Account'}</span>\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex space-x-2\">\n                          <Dialog>\n                            <DialogTrigger asChild>\n                              <Button variant=\"outline\" size=\"sm\" onClick={() => setSelectedPatient(patient)}>\n                                <Eye className=\"h-4 w-4\" />\n                              </Button>\n                            </DialogTrigger>\n                            <DialogContent>\n                              <DialogHeader>\n                                <DialogTitle>Patient Details</DialogTitle>\n                                <DialogDescription>\n                                  Complete assessment information for {patient.firstName} {patient.lastName}\n                                </DialogDescription>\n                              </DialogHeader>\n                              {selectedPatient && (\n                                <div className=\"space-y-4\">\n                                  <div className=\"grid grid-cols-2 gap-4\">\n                                    <div>\n                                      <label className=\"text-sm font-medium\">Name</label>\n                                      <p>{selectedPatient.firstName} {selectedPatient.lastName}</p>\n                                    </div>\n                                    <div>\n                                      <label className=\"text-sm font-medium\">Email</label>\n                                      <p>{selectedPatient.email}</p>\n                                    </div>\n                                    <div>\n                                      <label className=\"text-sm font-medium\">Phone</label>\n                                      <p>{selectedPatient.phone || 'Not provided'}</p>\n                                    </div>\n                                    <div>\n                                      <label className=\"text-sm font-medium\">Address</label>\n                                      <p>{selectedPatient.address || 'Not provided'}</p>\n                                    </div>\n                                    {selectedPatient.assessments?.[0] && (\n                                      <>\n                                        <div>\n                                          <label className=\"text-sm font-medium\">Assessment Date</label>\n                                          <p>{new Date(selectedPatient.assessments[0].assessmentDate).toLocaleDateString()}</p>\n                                        </div>\n                                        <div>\n                                          <label className=\"text-sm font-medium\">Risk Score</label>\n                                          <p className=\"font-mono text-lg\">{selectedPatient.assessments[0].riskScore} points</p>\n                                        </div>\n                                        <div>\n                                          <label className=\"text-sm font-medium\">Risk Group</label>\n                                          <Badge variant={getRiskGroupColor(selectedPatient.assessments[0].riskGroup) as any}>\n                                            Group {selectedPatient.assessments[0].riskGroup} - {getRiskGroupLabel(selectedPatient.assessments[0].riskGroup)}\n                                          </Badge>\n                                        </div>\n                                        <div>\n                                          <label className=\"text-sm font-medium\">Assessed By</label>\n                                          <p>{selectedPatient.assessments[0].assessedBy || 'Unknown'}</p>\n                                        </div>\n                                      </>\n                                    )}\n                                  </div>\n                                  {selectedPatient.assessments?.[0]?.notes && (\n                                    <div>\n                                      <label className=\"text-sm font-medium\">Notes</label>\n                                      <p className=\"text-sm text-gray-600 mt-1\">{selectedPatient.assessments[0].notes}</p>\n                                    </div>\n                                  )}\n                                </div>\n                              )}\n                            </DialogContent>\n                          </Dialog>\n\n                          {!latestInvitation?.invitationSent && latestAssessment && (\n                            <Button\n                              variant=\"default\"\n                              size=\"sm\"\n                              onClick={() => sendInvitation(patient)}\n                            >\n                              <Mail className=\"h-4 w-4\" />\n                            </Button>\n                          )}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAgBA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB,KAAK;YAAK,OAAO;QACjB;YAAS,OAAO;IAClB;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;gBACxC,IAAI,SAAS,OAAO,EAAE;oBACpB,YAAY,SAAS,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC1C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBACJ,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE7D,MAAM,mBAAmB,QAAQ,WAAW,EAAE,CAAC,EAAE;QACjD,MAAM,gBAAgB,gBAAgB,SAAS,kBAAkB,cAAc;QAE/E,OAAO,iBAAiB;IAC1B;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,mBAAmB,QAAQ,WAAW,EAAE,CAAC,EAAE;YACjD,IAAI,CAAC,kBAAkB;gBACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;gBACxC,WAAW,QAAQ,EAAE;gBACrB,OAAO,QAAQ,KAAK;gBACpB,WAAW,QAAQ,SAAS;gBAC5B,UAAU,QAAQ,QAAQ;gBAC1B,WAAW,iBAAiB,SAAS;gBACrC,WAAW,iBAAiB,SAAS;YACvC;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;gBACpD,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;gBAC/C,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,YAAY,gBAAgB,IAAI,CAAC,QAAQ,IAAI,EAAE;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,OAAO;sCACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,gBAAgB,QAAQ,YAAY;4CAC7C,MAAK;4CACL,SAAS,IAAM,eAAe;sDAC/B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,gBAAgB,MAAM,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,eAAe;sDAC/B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,gBAAgB,MAAM,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,eAAe;sDAC/B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,gBAAgB,MAAM,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,eAAe;sDAC/B;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,gBAAgB,MAAM,YAAY;4CAC3C,MAAK;4CACL,SAAS,IAAM,eAAe;sDAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;;oCAAC;oCAAW,iBAAiB,MAAM;oCAAC;;;;;;;0CAC9C,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,wBACC,8OAAC,iIAAA,CAAA,WAAQ;kDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAC/B,cAAA,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;+CAGjC,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC,iIAAA,CAAA,WAAQ;kDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAAmB;;;;;;;;;;+CAKtD,iBAAiB,GAAG,CAAC,CAAC;wCACpB,MAAM,mBAAmB,QAAQ,WAAW,EAAE,CAAC,EAAE;wCACjD,MAAM,mBAAmB,QAAQ,WAAW,EAAE,CAAC,EAAE;wCAEjD,qBACE,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;kEACC,cAAA,8OAAC;4DAAE,WAAU;;gEAAe,QAAQ,SAAS;gEAAC;gEAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;8DAGpE,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAG,QAAQ,KAAK;;;;;;0EACjB,8OAAC;gEAAE,WAAU;0EAAiB,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;8DAGnD,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAK,WAAU;kEACb,kBAAkB,aAAa;;;;;;;;;;;8DAGpC,8OAAC,iIAAA,CAAA,YAAS;8DACP,iCACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,kBAAkB,iBAAiB,SAAS;;4DAAU;4DAC7D,iBAAiB,SAAS;4DAAC;4DAAI,kBAAkB,iBAAiB,SAAS;;;;;;6EAGpF,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;8DAGpC,8OAAC,iIAAA,CAAA,YAAS;8DACP,mBACC,IAAI,KAAK,iBAAiB,cAAc,EAAE,kBAAkB,KAC5D;;;;;;8DAGJ,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,kBAAkB,iBAAiB,iBAAiB,cAAc;;;;;;kFAC1G,8OAAC;wEAAK,WAAU;kFAAW,kBAAkB,iBAAiB,YAAY;;;;;;;;;;;;0EAE5E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,kBAAkB,iBAAiB,iBAAiB,eAAe;;;;;;kFAC3G,8OAAC;wEAAK,WAAU;kFAAW,kBAAkB,iBAAiB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;8DAIxF,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;;kFACL,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,OAAO;kFACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;4EAAK,SAAS,IAAM,mBAAmB;sFACpE,cAAA,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAGnB,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,eAAY;;kGACX,8OAAC,kIAAA,CAAA,cAAW;kGAAC;;;;;;kGACb,8OAAC,kIAAA,CAAA,oBAAiB;;4FAAC;4FACoB,QAAQ,SAAS;4FAAC;4FAAE,QAAQ,QAAQ;;;;;;;;;;;;;4EAG5E,iCACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;;kHACC,8OAAC;wGAAM,WAAU;kHAAsB;;;;;;kHACvC,8OAAC;;4GAAG,gBAAgB,SAAS;4GAAC;4GAAE,gBAAgB,QAAQ;;;;;;;;;;;;;0GAE1D,8OAAC;;kHACC,8OAAC;wGAAM,WAAU;kHAAsB;;;;;;kHACvC,8OAAC;kHAAG,gBAAgB,KAAK;;;;;;;;;;;;0GAE3B,8OAAC;;kHACC,8OAAC;wGAAM,WAAU;kHAAsB;;;;;;kHACvC,8OAAC;kHAAG,gBAAgB,KAAK,IAAI;;;;;;;;;;;;0GAE/B,8OAAC;;kHACC,8OAAC;wGAAM,WAAU;kHAAsB;;;;;;kHACvC,8OAAC;kHAAG,gBAAgB,OAAO,IAAI;;;;;;;;;;;;4FAEhC,gBAAgB,WAAW,EAAE,CAAC,EAAE,kBAC/B;;kHACE,8OAAC;;0HACC,8OAAC;gHAAM,WAAU;0HAAsB;;;;;;0HACvC,8OAAC;0HAAG,IAAI,KAAK,gBAAgB,WAAW,CAAC,EAAE,CAAC,cAAc,EAAE,kBAAkB;;;;;;;;;;;;kHAEhF,8OAAC;;0HACC,8OAAC;gHAAM,WAAU;0HAAsB;;;;;;0HACvC,8OAAC;gHAAE,WAAU;;oHAAqB,gBAAgB,WAAW,CAAC,EAAE,CAAC,SAAS;oHAAC;;;;;;;;;;;;;kHAE7E,8OAAC;;0HACC,8OAAC;gHAAM,WAAU;0HAAsB;;;;;;0HACvC,8OAAC,iIAAA,CAAA,QAAK;gHAAC,SAAS,kBAAkB,gBAAgB,WAAW,CAAC,EAAE,CAAC,SAAS;;oHAAU;oHAC3E,gBAAgB,WAAW,CAAC,EAAE,CAAC,SAAS;oHAAC;oHAAI,kBAAkB,gBAAgB,WAAW,CAAC,EAAE,CAAC,SAAS;;;;;;;;;;;;;kHAGlH,8OAAC;;0HACC,8OAAC;gHAAM,WAAU;0HAAsB;;;;;;0HACvC,8OAAC;0HAAG,gBAAgB,WAAW,CAAC,EAAE,CAAC,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;oFAKxD,gBAAgB,WAAW,EAAE,CAAC,EAAE,EAAE,uBACjC,8OAAC;;0GACC,8OAAC;gGAAM,WAAU;0GAAsB;;;;;;0GACvC,8OAAC;gGAAE,WAAU;0GAA8B,gBAAgB,WAAW,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4DAQ1F,CAAC,kBAAkB,kBAAkB,kCACpC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,eAAe;0EAE9B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAtHX,QAAQ,EAAE;;;;;oCA6H7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}