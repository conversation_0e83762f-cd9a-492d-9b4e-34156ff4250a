{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction RadioGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Root>) {\n  return (\n    <RadioGroupPrimitive.Root\n      data-slot=\"radio-group\"\n      className={cn(\"grid gap-3\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction RadioGroupItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Item>) {\n  return (\n    <RadioGroupPrimitive.Item\n      data-slot=\"radio-group-item\"\n      className={cn(\n        \"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator\n        data-slot=\"radio-group-indicator\"\n        className=\"relative flex items-center justify-center\"\n      >\n        <CircleIcon className=\"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n}\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0XACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAC5B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// Types for API responses\nexport interface Patient {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  dateOfBirth?: string;\n  gender?: string;\n  address?: string;\n  assessments: Assessment[];\n  invitations: PatientInvitation[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Assessment {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  age: string;\n  bmi: string;\n  waistCircumference: string;\n  physicalActivity: string;\n  vegetableConsumption: string;\n  bloodPressureMedication: string;\n  highGlucoseHistory: string;\n  familyDiabetesHistory: string;\n  riskScore: number;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  notes?: string;\n  assessedBy?: string;\n  assessmentDate: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PatientInvitation {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  email: string;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  riskScore: number;\n  invitationSent: boolean;\n  sentAt?: string;\n  accountCreated: boolean;\n  accountCreatedAt?: string;\n  emailOpened: boolean;\n  emailOpenedAt?: string;\n  appDownloaded: boolean;\n  appDownloadedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AdminSettings {\n  id: string;\n  smtpHost?: string;\n  smtpPort?: string;\n  smtpUser?: string;\n  fromEmail?: string;\n  fromName?: string;\n  appName?: string;\n  clinicName?: string;\n  clinicAddress?: string;\n  clinicPhone?: string;\n  iosAppUrl?: string;\n  androidAppUrl?: string;\n  webAppUrl?: string;\n  emailNotifications: boolean;\n  assessmentAlerts: boolean;\n  highRiskAlerts: boolean;\n  dailyReports: boolean;\n  weeklyReports: boolean;\n  monthlyReports: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RiskGroupStats {\n  totalAssessments: number;\n  averageScore: number;\n  riskDistribution: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    percentage: number;\n  }>;\n  monthlyTrends: Array<{\n    date: string;\n    assessments: number;\n    averageScore: number;\n  }>;\n}\n\nexport interface SummaryReport {\n  summary: {\n    totalAssessments: number;\n    highRiskPatients: number;\n    invitationsSent: number;\n    accountsCreated: number;\n  };\n  riskGroupBreakdown: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    averageScore: number;\n  }>;\n  recentAssessments: Assessment[];\n  dateRange: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\nexport interface GlucoseReading {\n  id: string;\n  value: number;\n  timestamp: string;\n  trend?: string;\n  trendRate?: number;\n  source?: string;\n  notes?: string;\n}\n\nexport interface CGMStatistics {\n  totalReadings: number;\n  averageGlucose: number;\n  timeInRange: number;\n  timeAboveRange: number;\n  timeBelowRange: number;\n  targetRange: {\n    min: number;\n    max: number;\n  };\n}\n\nexport interface PatientCGMData {\n  patient: Patient;\n  cgmData: {\n    readings: GlucoseReading[];\n    statistics: CGMStatistics;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n    };\n  };\n}\n\nexport interface CGMOverview {\n  overview: Array<{\n    patient: {\n      id: string;\n      firstName: string;\n      lastName: string;\n      email: string;\n      riskGroup: 'A' | 'B' | 'C' | 'D' | null;\n      riskScore: number | null;\n    };\n    cgmSummary: {\n      totalReadings: number;\n      averageGlucose: number;\n      timeInRange: number;\n      latestReading: {\n        value: number;\n        timestamp: string;\n        trend?: string;\n      };\n      targetRange: {\n        min: number;\n        max: number;\n      };\n    };\n  }>;\n  summary: {\n    totalPatientsWithCGM: number;\n    totalPatients: number;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n      days: number;\n    };\n  };\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\ninterface PaginatedResponse<T> {\n  success: boolean;\n  data: {\n    [key: string]: T[];\n    pagination: {\n      page: number;\n      limit: number;\n      total: number;\n      pages: number;\n    };\n  };\n}\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n}\n\n// Assessment API functions\nexport const assessmentApi = {\n  // Create new assessment\n  create: async (assessmentData: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone?: string;\n    age: string;\n    bmi: string;\n    waistCircumference: string;\n    physicalActivity: string;\n    vegetableConsumption: string;\n    bloodPressureMedication: string;\n    highGlucoseHistory: string;\n    familyDiabetesHistory: string;\n    notes?: string;\n    assessedBy?: string;\n  }): Promise<ApiResponse<{ assessment: Assessment; riskScore: number; riskGroup: string }>> => {\n    return apiRequest('/admin/assessments', {\n      method: 'POST',\n      body: JSON.stringify(assessmentData),\n    });\n  },\n\n  // Get all assessments\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    riskGroup?: 'A' | 'B' | 'C' | 'D';\n  }): Promise<PaginatedResponse<Assessment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.riskGroup) searchParams.append('riskGroup', params.riskGroup);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/assessments${query ? `?${query}` : ''}`);\n  },\n};\n\n// Patient API functions\nexport const patientApi = {\n  // Get all patients\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n  }): Promise<PaginatedResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/patients${query ? `?${query}` : ''}`);\n  },\n};\n\n// Invitation API functions\nexport const invitationApi = {\n  // Send invitation\n  send: async (invitationData: {\n    patientId: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    riskGroup: 'A' | 'B' | 'C' | 'D';\n    riskScore: number;\n  }): Promise<ApiResponse<{ message: string }>> => {\n    return apiRequest('/admin/send-invitation', {\n      method: 'POST',\n      body: JSON.stringify(invitationData),\n    });\n  },\n};\n\n// Risk Groups API functions\nexport const riskGroupApi = {\n  // Get risk group statistics\n  getStats: async (): Promise<ApiResponse<RiskGroupStats>> => {\n    return apiRequest('/admin/risk-groups/stats');\n  },\n};\n\n// Reports API functions\nexport const reportsApi = {\n  // Get summary report\n  getSummary: async (params?: {\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<SummaryReport>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/reports/summary${query ? `?${query}` : ''}`);\n  },\n};\n\n// Settings API functions\nexport const settingsApi = {\n  // Get settings\n  get: async (): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings');\n  },\n\n  // Update settings\n  update: async (settings: Partial<AdminSettings>): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings', {\n      method: 'PUT',\n      body: JSON.stringify(settings),\n    });\n  },\n};\n\n// CGM API functions\nexport const cgmApi = {\n  // Get CGM data for a specific patient\n  getPatientData: async (\n    patientId: string,\n    params?: {\n      startDate?: string;\n      endDate?: string;\n      limit?: number;\n    }\n  ): Promise<ApiResponse<PatientCGMData>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/patients/${patientId}/cgm-data${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n\n  // Get CGM overview for all patients\n  getOverview: async (days?: number): Promise<ApiResponse<CGMOverview>> => {\n    const searchParams = new URLSearchParams();\n    if (days) searchParams.append('days', days.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/cgm-overview${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA,MAAM,eAAe,iEAAmC;AA+MxD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,eAAe,UAAU;IAExC,MAAM,SAAsB;QAC1B,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,QAAQ,OAAO;QAgBb,OAAO,WAAW,sBAAsB;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,sBAAsB;IACtB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnE;AACF;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAChE;AACF;AAGO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,MAAM,OAAO;QAQX,OAAO,WAAW,0BAA0B;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,UAAU;QACR,OAAO,WAAW;IACpB;AACF;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,YAAY,OAAO;QAIjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACvE;AACF;AAGO,MAAM,cAAc;IACzB,eAAe;IACf,KAAK;QACH,OAAO,WAAW;IACpB;IAEA,kBAAkB;IAClB,QAAQ,OAAO;QACb,OAAO,WAAW,mBAAmB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,SAAS;IACpB,sCAAsC;IACtC,gBAAgB,OACd,WACA;QAMA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,gBAAgB,EAAE,UAAU,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE1F,OAAO,WAAW;IACpB;IAEA,oCAAoC;IACpC,aAAa,OAAO;QAClB,MAAM,eAAe,IAAI;QACzB,IAAI,MAAM,aAAa,MAAM,CAAC,QAAQ,KAAK,QAAQ;QAEnD,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAExE,OAAO,WAAW;IACpB;AACF", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/app/dashboard/assessment/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\nimport { Badge } from '@/components/ui/badge';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Textarea } from '@/components/ui/textarea';\nimport { toast } from 'sonner';\nimport { ArrowLeft, Calculator, Mail, User } from 'lucide-react';\nimport Link from 'next/link';\nimport { assessmentApi, invitationApi } from '@/lib/api';\n\n// Finnish Diabetes Risk Score Schema\nconst assessmentSchema = z.object({\n  // Patient Information\n  firstName: z.string().min(2, 'First name must be at least 2 characters'),\n  lastName: z.string().min(2, 'Last name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phone: z.string().min(10, 'Please enter a valid phone number'),\n\n  // Finnish Diabetes Risk Score Questions\n  age: z.string(),\n  bmi: z.string(),\n  waistCircumference: z.string(),\n  physicalActivity: z.string(),\n  vegetableConsumption: z.string(),\n  bloodPressureMedication: z.string(),\n  highGlucoseHistory: z.string(),\n  familyDiabetesHistory: z.string(),\n\n  // Additional notes\n  notes: z.string().optional(),\n});\n\ntype AssessmentForm = z.infer<typeof assessmentSchema>;\n\n// Finnish Diabetes Risk Score calculation\nconst calculateRiskScore = (data: AssessmentForm) => {\n  let score = 0;\n\n  // Age scoring\n  if (data.age === '45-54') score += 2;\n  else if (data.age === '55-64') score += 3;\n  else if (data.age === '65+') score += 4;\n\n  // BMI scoring\n  if (data.bmi === '25-30') score += 1;\n  else if (data.bmi === '30+') score += 3;\n\n  // Waist circumference scoring (gender-specific)\n  if (data.waistCircumference === 'male-94-102' || data.waistCircumference === 'female-80-88') score += 3;\n  else if (data.waistCircumference === 'male-102+' || data.waistCircumference === 'female-88+') score += 4;\n\n  // Physical activity\n  if (data.physicalActivity === 'no') score += 2;\n\n  // Vegetable consumption\n  if (data.vegetableConsumption === 'no') score += 1;\n\n  // Blood pressure medication\n  if (data.bloodPressureMedication === 'yes') score += 2;\n\n  // High glucose history\n  if (data.highGlucoseHistory === 'yes') score += 5;\n\n  // Family diabetes history\n  if (data.familyDiabetesHistory === 'yes-grandparent-aunt-uncle') score += 3;\n  else if (data.familyDiabetesHistory === 'yes-parent-sibling-child') score += 5;\n\n  return score;\n};\n\n// Risk group assignment\nconst getRiskGroup = (score: number) => {\n  if (score < 7) return { group: 'A', risk: 'Low Risk', color: 'default' };\n  if (score < 12) return { group: 'B', risk: 'Slightly Elevated Risk', color: 'secondary' };\n  if (score < 15) return { group: 'C', risk: 'Moderate Risk', color: 'destructive' };\n  return { group: 'D', risk: 'High Risk', color: 'destructive' };\n};\n\nexport default function AssessmentPage() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [result, setResult] = useState<{ score: number; riskGroup: any } | null>(null);\n\n  const form = useForm<AssessmentForm>({\n    resolver: zodResolver(assessmentSchema),\n    defaultValues: {\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      age: '',\n      bmi: '',\n      waistCircumference: '',\n      physicalActivity: '',\n      vegetableConsumption: '',\n      bloodPressureMedication: '',\n      highGlucoseHistory: '',\n      familyDiabetesHistory: '',\n      notes: '',\n    },\n  });\n\n  const onSubmit = async (data: AssessmentForm) => {\n    setIsSubmitting(true);\n\n    try {\n      // Submit assessment to backend\n      const response = await assessmentApi.create({\n        firstName: data.firstName,\n        lastName: data.lastName,\n        email: data.email,\n        phone: data.phone,\n        age: data.age,\n        bmi: data.bmi,\n        waistCircumference: data.waistCircumference,\n        physicalActivity: data.physicalActivity,\n        vegetableConsumption: data.vegetableConsumption,\n        bloodPressureMedication: data.bloodPressureMedication,\n        highGlucoseHistory: data.highGlucoseHistory,\n        familyDiabetesHistory: data.familyDiabetesHistory,\n        notes: data.notes,\n        assessedBy: 'Dr. Admin', // You can make this dynamic\n      });\n\n      if (response.success) {\n        const { riskScore, riskGroup } = response.data;\n        const riskGroupInfo = getRiskGroup(riskScore);\n\n        setResult({\n          score: riskScore,\n          riskGroup: riskGroupInfo,\n          patientId: response.data.assessment.patientId,\n          email: data.email,\n          firstName: data.firstName,\n          lastName: data.lastName,\n          apiRiskGroup: riskGroup\n        });\n\n        toast.success('Assessment completed and saved successfully!');\n      } else {\n        throw new Error('Failed to save assessment');\n      }\n    } catch (error) {\n      console.error('Assessment error:', error);\n      toast.error('Failed to complete assessment. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const sendInvitation = async () => {\n    if (!result) return;\n\n    try {\n      const response = await invitationApi.send({\n        patientId: (result as any).patientId,\n        email: (result as any).email,\n        firstName: (result as any).firstName,\n        lastName: (result as any).lastName,\n        riskGroup: (result as any).apiRiskGroup,\n        riskScore: result.score,\n      });\n\n      if (response.success) {\n        toast.success(`Invitation sent to ${(result as any).email}!`);\n      } else {\n        throw new Error('Failed to send invitation');\n      }\n    } catch (error) {\n      console.error('Invitation error:', error);\n      toast.error('Failed to send invitation. Please try again.');\n    }\n  };\n\n  if (result) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/dashboard\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to Dashboard\n            </Link>\n          </Button>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Calculator className=\"mr-2 h-5 w-5\" />\n              Assessment Results\n            </CardTitle>\n            <CardDescription>\n              Finnish Diabetes Risk Score calculation completed\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"text-center space-y-4\">\n              <div>\n                <h3 className=\"text-2xl font-bold\">Risk Score: {result.score}</h3>\n                <Badge variant={result.riskGroup.color as any} className=\"mt-2\">\n                  Group {result.riskGroup.group} - {result.riskGroup.risk}\n                </Badge>\n              </div>\n\n              <Alert>\n                <AlertDescription>\n                  {result.riskGroup.group === 'A' &&\n                    'Low risk of developing type 2 diabetes. Continue healthy lifestyle habits.'\n                  }\n                  {result.riskGroup.group === 'B' &&\n                    'Slightly elevated risk. Consider lifestyle modifications and regular monitoring.'\n                  }\n                  {result.riskGroup.group === 'C' &&\n                    'Moderate risk. Lifestyle intervention recommended. Consider glucose tolerance test.'\n                  }\n                  {result.riskGroup.group === 'D' &&\n                    'High risk. Immediate lifestyle intervention and glucose tolerance test recommended.'\n                  }\n                </AlertDescription>\n              </Alert>\n            </div>\n\n            <div className=\"flex space-x-4 justify-center\">\n              <Button onClick={sendInvitation} className=\"flex items-center\">\n                <Mail className=\"mr-2 h-4 w-4\" />\n                Send App Invitation\n              </Button>\n              <Button variant=\"outline\" onClick={() => setResult(null)}>\n                New Assessment\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center space-x-4\">\n        <Button variant=\"ghost\" size=\"sm\" asChild>\n          <Link href=\"/dashboard\">\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            Back to Dashboard\n          </Link>\n        </Button>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <User className=\"mr-2 h-5 w-5\" />\n            New Patient Assessment\n          </CardTitle>\n          <CardDescription>\n            Finnish Diabetes Risk Score Assessment - Complete the questionnaire to determine prediabetes risk\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Form {...form}>\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\n              {/* Patient Information */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Patient Information</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <FormField\n                    control={form.control}\n                    name=\"firstName\"\n                    render={({ field }) => (\n                      <FormItem>\n                        <FormLabel>First Name</FormLabel>\n                        <FormControl>\n                          <Input placeholder=\"Enter first name\" {...field} />\n                        </FormControl>\n                        <FormMessage />\n                      </FormItem>\n                    )}\n                  />\n                  <FormField\n                    control={form.control}\n                    name=\"lastName\"\n                    render={({ field }) => (\n                      <FormItem>\n                        <FormLabel>Last Name</FormLabel>\n                        <FormControl>\n                          <Input placeholder=\"Enter last name\" {...field} />\n                        </FormControl>\n                        <FormMessage />\n                      </FormItem>\n                    )}\n                  />\n                  <FormField\n                    control={form.control}\n                    name=\"email\"\n                    render={({ field }) => (\n                      <FormItem>\n                        <FormLabel>Email Address</FormLabel>\n                        <FormControl>\n                          <Input type=\"email\" placeholder=\"Enter email address\" {...field} />\n                        </FormControl>\n                        <FormMessage />\n                      </FormItem>\n                    )}\n                  />\n                  <FormField\n                    control={form.control}\n                    name=\"phone\"\n                    render={({ field }) => (\n                      <FormItem>\n                        <FormLabel>Phone Number</FormLabel>\n                        <FormControl>\n                          <Input placeholder=\"Enter phone number\" {...field} />\n                        </FormControl>\n                        <FormMessage />\n                      </FormItem>\n                    )}\n                  />\n                </div>\n              </div>\n\n              {/* Finnish Diabetes Risk Score Questions */}\n              <div className=\"space-y-6\">\n                <h3 className=\"text-lg font-semibold\">Risk Assessment Questions</h3>\n\n                {/* Age */}\n                <FormField\n                  control={form.control}\n                  name=\"age\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>1. Age</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"under-45\" id=\"age-under-45\" />\n                            <label htmlFor=\"age-under-45\">Under 45 years (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"45-54\" id=\"age-45-54\" />\n                            <label htmlFor=\"age-45-54\">45-54 years (2 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"55-64\" id=\"age-55-64\" />\n                            <label htmlFor=\"age-55-64\">55-64 years (3 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"65+\" id=\"age-65-plus\" />\n                            <label htmlFor=\"age-65-plus\">Over 64 years (4 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* BMI */}\n                <FormField\n                  control={form.control}\n                  name=\"bmi\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>2. Body Mass Index (BMI)</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"under-25\" id=\"bmi-under-25\" />\n                            <label htmlFor=\"bmi-under-25\">Lower than 25 kg/m² (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"25-30\" id=\"bmi-25-30\" />\n                            <label htmlFor=\"bmi-25-30\">25-30 kg/m² (1 point)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"30+\" id=\"bmi-30-plus\" />\n                            <label htmlFor=\"bmi-30-plus\">Higher than 30 kg/m² (3 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Waist Circumference */}\n                <FormField\n                  control={form.control}\n                  name=\"waistCircumference\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>3. Waist Circumference</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"male-under-94\" id=\"waist-male-under-94\" />\n                            <label htmlFor=\"waist-male-under-94\">Men: Less than 94 cm (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"female-under-80\" id=\"waist-female-under-80\" />\n                            <label htmlFor=\"waist-female-under-80\">Women: Less than 80 cm (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"male-94-102\" id=\"waist-male-94-102\" />\n                            <label htmlFor=\"waist-male-94-102\">Men: 94-102 cm (3 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"female-80-88\" id=\"waist-female-80-88\" />\n                            <label htmlFor=\"waist-female-80-88\">Women: 80-88 cm (3 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"male-102+\" id=\"waist-male-102-plus\" />\n                            <label htmlFor=\"waist-male-102-plus\">Men: More than 102 cm (4 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"female-88+\" id=\"waist-female-88-plus\" />\n                            <label htmlFor=\"waist-female-88-plus\">Women: More than 88 cm (4 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Physical Activity */}\n                <FormField\n                  control={form.control}\n                  name=\"physicalActivity\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>4. Do you usually have at least 30 minutes of physical activity at work and/or during leisure time?</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes\" id=\"activity-yes\" />\n                            <label htmlFor=\"activity-yes\">Yes (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"no\" id=\"activity-no\" />\n                            <label htmlFor=\"activity-no\">No (2 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Vegetable Consumption */}\n                <FormField\n                  control={form.control}\n                  name=\"vegetableConsumption\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>5. How often do you eat vegetables, fruit or berries?</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes\" id=\"vegetables-yes\" />\n                            <label htmlFor=\"vegetables-yes\">Every day (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"no\" id=\"vegetables-no\" />\n                            <label htmlFor=\"vegetables-no\">Not every day (1 point)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Blood Pressure Medication */}\n                <FormField\n                  control={form.control}\n                  name=\"bloodPressureMedication\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>6. Have you ever taken medication for high blood pressure?</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"no\" id=\"bp-med-no\" />\n                            <label htmlFor=\"bp-med-no\">No (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes\" id=\"bp-med-yes\" />\n                            <label htmlFor=\"bp-med-yes\">Yes (2 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* High Glucose History */}\n                <FormField\n                  control={form.control}\n                  name=\"highGlucoseHistory\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>7. Have you ever been found to have high blood glucose?</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"no\" id=\"glucose-no\" />\n                            <label htmlFor=\"glucose-no\">No (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes\" id=\"glucose-yes\" />\n                            <label htmlFor=\"glucose-yes\">Yes (5 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Family Diabetes History */}\n                <FormField\n                  control={form.control}\n                  name=\"familyDiabetesHistory\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>8. Have any of your family members been diagnosed with diabetes?</FormLabel>\n                      <FormControl>\n                        <RadioGroup onValueChange={field.onChange} value={field.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"no\" id=\"family-diabetes-no\" />\n                            <label htmlFor=\"family-diabetes-no\">No (0 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes-grandparent-aunt-uncle\" id=\"family-diabetes-distant\" />\n                            <label htmlFor=\"family-diabetes-distant\">Yes: grandparent, aunt, uncle, or first cousin (3 points)</label>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value=\"yes-parent-sibling-child\" id=\"family-diabetes-close\" />\n                            <label htmlFor=\"family-diabetes-close\">Yes: parent, brother, sister, or own child (5 points)</label>\n                          </div>\n                        </RadioGroup>\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                {/* Additional Notes */}\n                <FormField\n                  control={form.control}\n                  name=\"notes\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Additional Notes (Optional)</FormLabel>\n                      <FormControl>\n                        <Textarea\n                          placeholder=\"Any additional observations or notes about the patient...\"\n                          {...field}\n                        />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n\n                <div className=\"flex justify-end\">\n                  <Button type=\"submit\" disabled={isSubmitting}>\n                    {isSubmitting ? 'Calculating...' : 'Calculate Risk Score'}\n                  </Button>\n                </div>\n              </div>\n            </form>\n          </Form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;;AAmBA,qCAAqC;AACrC,MAAM,mBAAmB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,EAAE;IAChC,sBAAsB;IACtB,WAAW,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC7B,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC5B,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;IACxB,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;IAE1B,wCAAwC;IACxC,KAAK,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IACZ,KAAK,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IACZ,oBAAoB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IAC3B,kBAAkB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IACzB,sBAAsB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IAC7B,yBAAyB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IAChC,oBAAoB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IAC3B,uBAAuB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD;IAE9B,mBAAmB;IACnB,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;AAC5B;AAIA,0CAA0C;AAC1C,MAAM,qBAAqB,CAAC;IAC1B,IAAI,QAAQ;IAEZ,cAAc;IACd,IAAI,KAAK,GAAG,KAAK,SAAS,SAAS;SAC9B,IAAI,KAAK,GAAG,KAAK,SAAS,SAAS;SACnC,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS;IAEtC,cAAc;IACd,IAAI,KAAK,GAAG,KAAK,SAAS,SAAS;SAC9B,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS;IAEtC,gDAAgD;IAChD,IAAI,KAAK,kBAAkB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,gBAAgB,SAAS;SACjG,IAAI,KAAK,kBAAkB,KAAK,eAAe,KAAK,kBAAkB,KAAK,cAAc,SAAS;IAEvG,oBAAoB;IACpB,IAAI,KAAK,gBAAgB,KAAK,MAAM,SAAS;IAE7C,wBAAwB;IACxB,IAAI,KAAK,oBAAoB,KAAK,MAAM,SAAS;IAEjD,4BAA4B;IAC5B,IAAI,KAAK,uBAAuB,KAAK,OAAO,SAAS;IAErD,uBAAuB;IACvB,IAAI,KAAK,kBAAkB,KAAK,OAAO,SAAS;IAEhD,0BAA0B;IAC1B,IAAI,KAAK,qBAAqB,KAAK,8BAA8B,SAAS;SACrE,IAAI,KAAK,qBAAqB,KAAK,4BAA4B,SAAS;IAE7E,OAAO;AACT;AAEA,wBAAwB;AACxB,MAAM,eAAe,CAAC;IACpB,IAAI,QAAQ,GAAG,OAAO;QAAE,OAAO;QAAK,MAAM;QAAY,OAAO;IAAU;IACvE,IAAI,QAAQ,IAAI,OAAO;QAAE,OAAO;QAAK,MAAM;QAA0B,OAAO;IAAY;IACxF,IAAI,QAAQ,IAAI,OAAO;QAAE,OAAO;QAAK,MAAM;QAAiB,OAAO;IAAc;IACjF,OAAO;QAAE,OAAO;QAAK,MAAM;QAAa,OAAO;IAAc;AAC/D;AAEe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAE/E,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkB;QACnC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,KAAK;YACL,KAAK;YACL,oBAAoB;YACpB,kBAAkB;YAClB,sBAAsB;YACtB,yBAAyB;YACzB,oBAAoB;YACpB,uBAAuB;YACvB,OAAO;QACT;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,+BAA+B;YAC/B,MAAM,WAAW,MAAM,iHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAC1C,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG;gBACb,KAAK,KAAK,GAAG;gBACb,oBAAoB,KAAK,kBAAkB;gBAC3C,kBAAkB,KAAK,gBAAgB;gBACvC,sBAAsB,KAAK,oBAAoB;gBAC/C,yBAAyB,KAAK,uBAAuB;gBACrD,oBAAoB,KAAK,kBAAkB;gBAC3C,uBAAuB,KAAK,qBAAqB;gBACjD,OAAO,KAAK,KAAK;gBACjB,YAAY;YACd;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC9C,MAAM,gBAAgB,aAAa;gBAEnC,UAAU;oBACR,OAAO;oBACP,WAAW;oBACX,WAAW,SAAS,IAAI,CAAC,UAAU,CAAC,SAAS;oBAC7C,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,cAAc;gBAChB;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;gBACxC,WAAW,AAAC,OAAe,SAAS;gBACpC,OAAO,AAAC,OAAe,KAAK;gBAC5B,WAAW,AAAC,OAAe,SAAS;gBACpC,UAAU,AAAC,OAAe,QAAQ;gBAClC,WAAW,AAAC,OAAe,YAAY;gBACvC,WAAW,OAAO,KAAK;YACzB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,AAAC,OAAe,KAAK,CAAC,CAAC,CAAC;YAC9D,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,QAAQ;QACV,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,OAAO;kCACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAM5C,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGzC,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAAqB;wDAAa,OAAO,KAAK;;;;;;;8DAC5D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,OAAO,SAAS,CAAC,KAAK;oDAAS,WAAU;;wDAAO;wDACvD,OAAO,SAAS,CAAC,KAAK;wDAAC;wDAAI,OAAO,SAAS,CAAC,IAAI;;;;;;;;;;;;;sDAI3D,8OAAC,iIAAA,CAAA,QAAK;sDACJ,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;oDACd,OAAO,SAAS,CAAC,KAAK,KAAK,OAC1B;oDAED,OAAO,SAAS,CAAC,KAAK,KAAK,OAC1B;oDAED,OAAO,SAAS,CAAC,KAAK,KAAK,OAC1B;oDAED,OAAO,SAAS,CAAC,KAAK,KAAK,OAC1B;;;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAgB,WAAU;;8DACzC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,UAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQtE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,OAAO;8BACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;;0CACT,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;0BAM5C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,8OAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDAErD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAAoB,GAAG,KAAK;;;;;;;;;;;kFAEjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kEAIlB,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAAmB,GAAG,KAAK;;;;;;;;;;;kFAEhD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kEAIlB,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,MAAK;4EAAQ,aAAY;4EAAuB,GAAG,KAAK;;;;;;;;;;;kFAEjE,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kEAIlB,8OAAC,gIAAA,CAAA,YAAS;wDACR,SAAS,KAAK,OAAO;wDACrB,MAAK;wDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kFACP,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4EAAC,aAAY;4EAAsB,GAAG,KAAK;;;;;;;;;;;kFAEnD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DAGtC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAW,IAAG;;;;;;8FACpC,8OAAC;oFAAM,SAAQ;8FAAe;;;;;;;;;;;;sFAEhC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAQ,IAAG;;;;;;8FACjC,8OAAC;oFAAM,SAAQ;8FAAY;;;;;;;;;;;;sFAE7B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAQ,IAAG;;;;;;8FACjC,8OAAC;oFAAM,SAAQ;8FAAY;;;;;;;;;;;;sFAE7B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAc;;;;;;;;;;;;;;;;;;;;;;;0EAInC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAW,IAAG;;;;;;8FACpC,8OAAC;oFAAM,SAAQ;8FAAe;;;;;;;;;;;;sFAEhC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAQ,IAAG;;;;;;8FACjC,8OAAC;oFAAM,SAAQ;8FAAY;;;;;;;;;;;;sFAE7B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAc;;;;;;;;;;;;;;;;;;;;;;;0EAInC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAgB,IAAG;;;;;;8FACzC,8OAAC;oFAAM,SAAQ;8FAAsB;;;;;;;;;;;;sFAEvC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAkB,IAAG;;;;;;8FAC3C,8OAAC;oFAAM,SAAQ;8FAAwB;;;;;;;;;;;;sFAEzC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAc,IAAG;;;;;;8FACvC,8OAAC;oFAAM,SAAQ;8FAAoB;;;;;;;;;;;;sFAErC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAe,IAAG;;;;;;8FACxC,8OAAC;oFAAM,SAAQ;8FAAqB;;;;;;;;;;;;sFAEtC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAY,IAAG;;;;;;8FACrC,8OAAC;oFAAM,SAAQ;8FAAsB;;;;;;;;;;;;sFAEvC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAa,IAAG;;;;;;8FACtC,8OAAC;oFAAM,SAAQ;8FAAuB;;;;;;;;;;;;;;;;;;;;;;;0EAI5C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAe;;;;;;;;;;;;sFAEhC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAK,IAAG;;;;;;8FAC9B,8OAAC;oFAAM,SAAQ;8FAAc;;;;;;;;;;;;;;;;;;;;;;;0EAInC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAiB;;;;;;;;;;;;sFAElC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAK,IAAG;;;;;;8FAC9B,8OAAC;oFAAM,SAAQ;8FAAgB;;;;;;;;;;;;;;;;;;;;;;;0EAIrC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAK,IAAG;;;;;;8FAC9B,8OAAC;oFAAM,SAAQ;8FAAY;;;;;;;;;;;;sFAE7B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAa;;;;;;;;;;;;;;;;;;;;;;;0EAIlC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAK,IAAG;;;;;;8FAC9B,8OAAC;oFAAM,SAAQ;8FAAa;;;;;;;;;;;;sFAE9B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAM,IAAG;;;;;;8FAC/B,8OAAC;oFAAM,SAAQ;8FAAc;;;;;;;;;;;;;;;;;;;;;;;0EAInC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEAAC,eAAe,MAAM,QAAQ;oEAAE,OAAO,MAAM,KAAK;;sFAC3D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAAK,IAAG;;;;;;8FAC9B,8OAAC;oFAAM,SAAQ;8FAAqB;;;;;;;;;;;;sFAEtC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAA6B,IAAG;;;;;;8FACtD,8OAAC;oFAAM,SAAQ;8FAA0B;;;;;;;;;;;;sFAE3C,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFAAC,OAAM;oFAA2B,IAAG;;;;;;8FACpD,8OAAC;oFAAM,SAAQ;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;0EAI7C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAMlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,aAAY;oEACX,GAAG,KAAK;;;;;;;;;;;0EAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAS,UAAU;8DAC7B,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD", "debugId": null}}]}