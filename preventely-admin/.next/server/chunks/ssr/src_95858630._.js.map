{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// Types for API responses\nexport interface Patient {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  dateOfBirth?: string;\n  gender?: string;\n  address?: string;\n  assessments: Assessment[];\n  invitations: PatientInvitation[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Assessment {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  age: string;\n  bmi: string;\n  waistCircumference: string;\n  physicalActivity: string;\n  vegetableConsumption: string;\n  bloodPressureMedication: string;\n  highGlucoseHistory: string;\n  familyDiabetesHistory: string;\n  riskScore: number;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  notes?: string;\n  assessedBy?: string;\n  assessmentDate: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PatientInvitation {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  email: string;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  riskScore: number;\n  invitationSent: boolean;\n  sentAt?: string;\n  accountCreated: boolean;\n  accountCreatedAt?: string;\n  emailOpened: boolean;\n  emailOpenedAt?: string;\n  appDownloaded: boolean;\n  appDownloadedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AdminSettings {\n  id: string;\n  smtpHost?: string;\n  smtpPort?: string;\n  smtpUser?: string;\n  fromEmail?: string;\n  fromName?: string;\n  appName?: string;\n  clinicName?: string;\n  clinicAddress?: string;\n  clinicPhone?: string;\n  iosAppUrl?: string;\n  androidAppUrl?: string;\n  webAppUrl?: string;\n  emailNotifications: boolean;\n  assessmentAlerts: boolean;\n  highRiskAlerts: boolean;\n  dailyReports: boolean;\n  weeklyReports: boolean;\n  monthlyReports: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RiskGroupStats {\n  totalAssessments: number;\n  averageScore: number;\n  riskDistribution: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    percentage: number;\n  }>;\n  monthlyTrends: Array<{\n    date: string;\n    assessments: number;\n    averageScore: number;\n  }>;\n}\n\nexport interface SummaryReport {\n  summary: {\n    totalAssessments: number;\n    highRiskPatients: number;\n    invitationsSent: number;\n    accountsCreated: number;\n  };\n  riskGroupBreakdown: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    averageScore: number;\n  }>;\n  recentAssessments: Assessment[];\n  dateRange: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\nexport interface GlucoseReading {\n  id: string;\n  value: number;\n  timestamp: string;\n  trend?: string;\n  trendRate?: number;\n  source?: string;\n  notes?: string;\n}\n\nexport interface CGMStatistics {\n  totalReadings: number;\n  averageGlucose: number;\n  timeInRange: number;\n  timeAboveRange: number;\n  timeBelowRange: number;\n  targetRange: {\n    min: number;\n    max: number;\n  };\n}\n\nexport interface PatientCGMData {\n  patient: Patient;\n  cgmData: {\n    readings: GlucoseReading[];\n    statistics: CGMStatistics;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n    };\n  };\n}\n\nexport interface CGMOverview {\n  overview: Array<{\n    patient: {\n      id: string;\n      firstName: string;\n      lastName: string;\n      email: string;\n      riskGroup: 'A' | 'B' | 'C' | 'D' | null;\n      riskScore: number | null;\n    };\n    cgmSummary: {\n      totalReadings: number;\n      averageGlucose: number;\n      timeInRange: number;\n      latestReading: {\n        value: number;\n        timestamp: string;\n        trend?: string;\n      };\n      targetRange: {\n        min: number;\n        max: number;\n      };\n    };\n  }>;\n  summary: {\n    totalPatientsWithCGM: number;\n    totalPatients: number;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n      days: number;\n    };\n  };\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\ninterface PaginatedResponse<T> {\n  success: boolean;\n  data: {\n    [key: string]: T[];\n    pagination: {\n      page: number;\n      limit: number;\n      total: number;\n      pages: number;\n    };\n  };\n}\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n}\n\n// Assessment API functions\nexport const assessmentApi = {\n  // Create new assessment\n  create: async (assessmentData: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone?: string;\n    age: string;\n    bmi: string;\n    waistCircumference: string;\n    physicalActivity: string;\n    vegetableConsumption: string;\n    bloodPressureMedication: string;\n    highGlucoseHistory: string;\n    familyDiabetesHistory: string;\n    notes?: string;\n    assessedBy?: string;\n  }): Promise<ApiResponse<{ assessment: Assessment; riskScore: number; riskGroup: string }>> => {\n    return apiRequest('/admin/assessments', {\n      method: 'POST',\n      body: JSON.stringify(assessmentData),\n    });\n  },\n\n  // Get all assessments\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    riskGroup?: 'A' | 'B' | 'C' | 'D';\n  }): Promise<PaginatedResponse<Assessment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.riskGroup) searchParams.append('riskGroup', params.riskGroup);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/assessments${query ? `?${query}` : ''}`);\n  },\n};\n\n// Patient API functions\nexport const patientApi = {\n  // Get all patients\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n  }): Promise<PaginatedResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/patients${query ? `?${query}` : ''}`);\n  },\n};\n\n// Invitation API functions\nexport const invitationApi = {\n  // Send invitation\n  send: async (invitationData: {\n    patientId: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    riskGroup: 'A' | 'B' | 'C' | 'D';\n    riskScore: number;\n  }): Promise<ApiResponse<{ message: string }>> => {\n    return apiRequest('/admin/send-invitation', {\n      method: 'POST',\n      body: JSON.stringify(invitationData),\n    });\n  },\n};\n\n// Risk Groups API functions\nexport const riskGroupApi = {\n  // Get risk group statistics\n  getStats: async (): Promise<ApiResponse<RiskGroupStats>> => {\n    return apiRequest('/admin/risk-groups/stats');\n  },\n};\n\n// Reports API functions\nexport const reportsApi = {\n  // Get summary report\n  getSummary: async (params?: {\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<SummaryReport>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/reports/summary${query ? `?${query}` : ''}`);\n  },\n};\n\n// Settings API functions\nexport const settingsApi = {\n  // Get settings\n  get: async (): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings');\n  },\n\n  // Update settings\n  update: async (settings: Partial<AdminSettings>): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings', {\n      method: 'PUT',\n      body: JSON.stringify(settings),\n    });\n  },\n};\n\n// CGM API functions\nexport const cgmApi = {\n  // Get CGM data for a specific patient\n  getPatientData: async (\n    patientId: string,\n    params?: {\n      startDate?: string;\n      endDate?: string;\n      limit?: number;\n    }\n  ): Promise<ApiResponse<PatientCGMData>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/patients/${patientId}/cgm-data${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n\n  // Get CGM overview for all patients\n  getOverview: async (days?: number): Promise<ApiResponse<CGMOverview>> => {\n    const searchParams = new URLSearchParams();\n    if (days) searchParams.append('days', days.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/cgm-overview${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA,MAAM,eAAe,iEAAmC;AA+MxD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,eAAe,UAAU;IAExC,MAAM,SAAsB;QAC1B,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,QAAQ,OAAO;QAgBb,OAAO,WAAW,sBAAsB;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,sBAAsB;IACtB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnE;AACF;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAChE;AACF;AAGO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,MAAM,OAAO;QAQX,OAAO,WAAW,0BAA0B;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,UAAU;QACR,OAAO,WAAW;IACpB;AACF;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,YAAY,OAAO;QAIjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACvE;AACF;AAGO,MAAM,cAAc;IACzB,eAAe;IACf,KAAK;QACH,OAAO,WAAW;IACpB;IAEA,kBAAkB;IAClB,QAAQ,OAAO;QACb,OAAO,WAAW,mBAAmB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,SAAS;IACpB,sCAAsC;IACtC,gBAAgB,OACd,WACA;QAMA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,gBAAgB,EAAE,UAAU,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE1F,OAAO,WAAW;IACpB;IAEA,oCAAoC;IACpC,aAAa,OAAO;QAClB,MAAM,eAAe,IAAI;QACzB,IAAI,MAAM,aAAa,MAAM,CAAC,QAAQ,KAAK,QAAQ;QAEnD,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAExE,OAAO,WAAW;IACpB;AACF", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/cgm/glucose-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler,\n  TimeScale,\n} from 'chart.js';\nimport { Line } from 'react-chartjs-2';\nimport 'chartjs-adapter-date-fns';\nimport { format } from 'date-fns';\nimport { type GlucoseReading, type CGMStatistics } from '@/lib/api';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler,\n  TimeScale\n);\n\ninterface GlucoseChartProps {\n  readings: GlucoseReading[];\n  statistics: CGMStatistics;\n  height?: number;\n  showTargetRange?: boolean;\n}\n\nexport function GlucoseChart({ \n  readings, \n  statistics, \n  height = 400, \n  showTargetRange = true \n}: GlucoseChartProps) {\n  const chartData = useMemo(() => {\n    // Sort readings by timestamp\n    const sortedReadings = [...readings].sort(\n      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n    );\n\n    const glucoseData = sortedReadings.map(reading => ({\n      x: new Date(reading.timestamp),\n      y: reading.value,\n      trend: reading.trend,\n      trendRate: reading.trendRate,\n    }));\n\n    const datasets = [\n      {\n        label: 'Glucose Level',\n        data: glucoseData,\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 2,\n        pointRadius: 3,\n        pointHoverRadius: 6,\n        pointBackgroundColor: glucoseData.map(point => {\n          if (point.y < statistics.targetRange.min) return 'rgb(37, 99, 235)'; // Blue for low\n          if (point.y > statistics.targetRange.max) return 'rgb(239, 68, 68)'; // Red for high\n          return 'rgb(34, 197, 94)'; // Green for in range\n        }),\n        pointBorderColor: '#ffffff',\n        pointBorderWidth: 2,\n        fill: false,\n        tension: 0.1,\n      },\n    ];\n\n    // Add target range bands if enabled\n    if (showTargetRange && sortedReadings.length > 0) {\n      const timeRange = {\n        start: new Date(sortedReadings[0].timestamp),\n        end: new Date(sortedReadings[sortedReadings.length - 1].timestamp),\n      };\n\n      // Target range band\n      datasets.push({\n        label: 'Target Range',\n        data: [\n          { x: timeRange.start, y: statistics.targetRange.max },\n          { x: timeRange.end, y: statistics.targetRange.max },\n        ],\n        borderColor: 'rgba(34, 197, 94, 0.3)',\n        backgroundColor: 'rgba(34, 197, 94, 0.1)',\n        borderWidth: 1,\n        pointRadius: 0,\n        fill: '+1',\n        tension: 0,\n      } as any);\n\n      datasets.push({\n        label: 'Target Range Lower',\n        data: [\n          { x: timeRange.start, y: statistics.targetRange.min },\n          { x: timeRange.end, y: statistics.targetRange.min },\n        ],\n        borderColor: 'rgba(34, 197, 94, 0.3)',\n        backgroundColor: 'rgba(34, 197, 94, 0.1)',\n        borderWidth: 1,\n        pointRadius: 0,\n        fill: false,\n        tension: 0,\n      } as any);\n    }\n\n    return { datasets };\n  }, [readings, statistics, showTargetRange]);\n\n  const options = useMemo(() => ({\n    responsive: true,\n    maintainAspectRatio: false,\n    interaction: {\n      mode: 'index' as const,\n      intersect: false,\n    },\n    plugins: {\n      title: {\n        display: true,\n        text: 'Continuous Glucose Monitoring',\n        font: {\n          size: 16,\n          weight: 'bold' as const,\n        },\n      },\n      legend: {\n        display: true,\n        position: 'top' as const,\n        labels: {\n          filter: (legendItem: any) => {\n            // Hide the target range lower line from legend\n            return legendItem.text !== 'Target Range Lower';\n          },\n        },\n      },\n      tooltip: {\n        callbacks: {\n          title: (context: any) => {\n            const date = new Date(context[0].parsed.x);\n            return format(date, 'MMM dd, yyyy HH:mm');\n          },\n          label: (context: any) => {\n            if (context.datasetIndex === 0) {\n              const reading = readings.find(r => \n                new Date(r.timestamp).getTime() === context.parsed.x\n              );\n              \n              let label = `Glucose: ${context.parsed.y} mg/dL`;\n              \n              if (reading?.trend && reading.trend !== 'stable') {\n                const trendSymbol = reading.trend === 'rising' ? '↗️' : '↘️';\n                label += ` ${trendSymbol}`;\n                \n                if (reading.trendRate) {\n                  label += ` (${reading.trendRate > 0 ? '+' : ''}${reading.trendRate.toFixed(1)} mg/dL/min)`;\n                }\n              }\n              \n              // Add status\n              if (context.parsed.y < statistics.targetRange.min) {\n                label += ' - LOW';\n              } else if (context.parsed.y > statistics.targetRange.max) {\n                label += ' - HIGH';\n              } else {\n                label += ' - IN RANGE';\n              }\n              \n              return label;\n            }\n            return context.dataset.label;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        type: 'time' as const,\n        time: {\n          displayFormats: {\n            hour: 'HH:mm',\n            day: 'MMM dd',\n          },\n        },\n        title: {\n          display: true,\n          text: 'Time',\n        },\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n      },\n      y: {\n        title: {\n          display: true,\n          text: 'Glucose (mg/dL)',\n        },\n        min: Math.max(50, Math.min(...readings.map(r => r.value)) - 20),\n        max: Math.min(400, Math.max(...readings.map(r => r.value)) + 20),\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)',\n        },\n        ticks: {\n          callback: function(value: any) {\n            return value + ' mg/dL';\n          },\n        },\n      },\n    },\n    elements: {\n      point: {\n        hoverRadius: 8,\n      },\n    },\n  }), [readings, statistics]);\n\n  if (readings.length === 0) {\n    return (\n      <div \n        className=\"flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300\"\n        style={{ height }}\n      >\n        <div className=\"text-center\">\n          <div className=\"text-gray-400 text-lg mb-2\">📊</div>\n          <p className=\"text-gray-500 font-medium\">No glucose data available</p>\n          <p className=\"text-gray-400 text-sm\">Connect a CGM device to see glucose trends</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ height }}>\n      <Line data={chartData} options={options} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAYA;;;;;;AAEA;AAjBA;;;;;;;AAoBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,YAAS;AAUJ,SAAS,aAAa,EAC3B,QAAQ,EACR,UAAU,EACV,SAAS,GAAG,EACZ,kBAAkB,IAAI,EACJ;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,6BAA6B;QAC7B,MAAM,iBAAiB;eAAI;SAAS,CAAC,IAAI,CACvC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAG3E,MAAM,cAAc,eAAe,GAAG,CAAC,CAAA,UAAW,CAAC;gBACjD,GAAG,IAAI,KAAK,QAAQ,SAAS;gBAC7B,GAAG,QAAQ,KAAK;gBAChB,OAAO,QAAQ,KAAK;gBACpB,WAAW,QAAQ,SAAS;YAC9B,CAAC;QAED,MAAM,WAAW;YACf;gBACE,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,aAAa;gBACb,kBAAkB;gBAClB,sBAAsB,YAAY,GAAG,CAAC,CAAA;oBACpC,IAAI,MAAM,CAAC,GAAG,WAAW,WAAW,CAAC,GAAG,EAAE,OAAO,oBAAoB,eAAe;oBACpF,IAAI,MAAM,CAAC,GAAG,WAAW,WAAW,CAAC,GAAG,EAAE,OAAO,oBAAoB,eAAe;oBACpF,OAAO,oBAAoB,qBAAqB;gBAClD;gBACA,kBAAkB;gBAClB,kBAAkB;gBAClB,MAAM;gBACN,SAAS;YACX;SACD;QAED,oCAAoC;QACpC,IAAI,mBAAmB,eAAe,MAAM,GAAG,GAAG;YAChD,MAAM,YAAY;gBAChB,OAAO,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC,SAAS;gBAC3C,KAAK,IAAI,KAAK,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,SAAS;YACnE;YAEA,oBAAoB;YACpB,SAAS,IAAI,CAAC;gBACZ,OAAO;gBACP,MAAM;oBACJ;wBAAE,GAAG,UAAU,KAAK;wBAAE,GAAG,WAAW,WAAW,CAAC,GAAG;oBAAC;oBACpD;wBAAE,GAAG,UAAU,GAAG;wBAAE,GAAG,WAAW,WAAW,CAAC,GAAG;oBAAC;iBACnD;gBACD,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,SAAS;YACX;YAEA,SAAS,IAAI,CAAC;gBACZ,OAAO;gBACP,MAAM;oBACJ;wBAAE,GAAG,UAAU,KAAK;wBAAE,GAAG,WAAW,WAAW,CAAC,GAAG;oBAAC;oBACpD;wBAAE,GAAG,UAAU,GAAG;wBAAE,GAAG,WAAW,WAAW,CAAC,GAAG;oBAAC;iBACnD;gBACD,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,aAAa;gBACb,MAAM;gBACN,SAAS;YACX;QACF;QAEA,OAAO;YAAE;QAAS;IACpB,GAAG;QAAC;QAAU;QAAY;KAAgB;IAE1C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC7B,YAAY;YACZ,qBAAqB;YACrB,aAAa;gBACX,MAAM;gBACN,WAAW;YACb;YACA,SAAS;gBACP,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,UAAU;oBACV,QAAQ;wBACN,QAAQ,CAAC;4BACP,+CAA+C;4BAC/C,OAAO,WAAW,IAAI,KAAK;wBAC7B;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;wBACT,OAAO,CAAC;4BACN,MAAM,OAAO,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;4BACzC,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;wBACtB;wBACA,OAAO,CAAC;4BACN,IAAI,QAAQ,YAAY,KAAK,GAAG;gCAC9B,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAC5B,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,OAAO,QAAQ,MAAM,CAAC,CAAC;gCAGtD,IAAI,QAAQ,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;gCAEhD,IAAI,SAAS,SAAS,QAAQ,KAAK,KAAK,UAAU;oCAChD,MAAM,cAAc,QAAQ,KAAK,KAAK,WAAW,OAAO;oCACxD,SAAS,CAAC,CAAC,EAAE,aAAa;oCAE1B,IAAI,QAAQ,SAAS,EAAE;wCACrB,SAAS,CAAC,EAAE,EAAE,QAAQ,SAAS,GAAG,IAAI,MAAM,KAAK,QAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,WAAW,CAAC;oCAC5F;gCACF;gCAEA,aAAa;gCACb,IAAI,QAAQ,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW,CAAC,GAAG,EAAE;oCACjD,SAAS;gCACX,OAAO,IAAI,QAAQ,MAAM,CAAC,CAAC,GAAG,WAAW,WAAW,CAAC,GAAG,EAAE;oCACxD,SAAS;gCACX,OAAO;oCACL,SAAS;gCACX;gCAEA,OAAO;4BACT;4BACA,OAAO,QAAQ,OAAO,CAAC,KAAK;wBAC9B;oBACF;gBACF;YACF;YACA,QAAQ;gBACN,GAAG;oBACD,MAAM;oBACN,MAAM;wBACJ,gBAAgB;4BACd,MAAM;4BACN,KAAK;wBACP;oBACF;oBACA,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;oBACA,MAAM;wBACJ,OAAO;oBACT;gBACF;gBACA,GAAG;oBACD,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;oBACA,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oBAC5D,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oBAC7D,MAAM;wBACJ,OAAO;oBACT;oBACA,OAAO;wBACL,UAAU,SAAS,KAAU;4BAC3B,OAAO,QAAQ;wBACjB;oBACF;gBACF;YACF;YACA,UAAU;gBACR,OAAO;oBACL,aAAa;gBACf;YACF;QACF,CAAC,GAAG;QAAC;QAAU;KAAW;IAE1B,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAO;sBAEhB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE;QAAO;kBACnB,cAAA,8OAAC,sJAAA,CAAA,OAAI;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGtC", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/cgm/glucose-stats-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n} from 'chart.js';\nimport { Doughnut, Bar } from 'react-chartjs-2';\nimport { type CGMStatistics } from '@/lib/api';\n\nChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);\n\ninterface GlucoseStatsChartProps {\n  statistics: CGMStatistics;\n  type?: 'doughnut' | 'bar';\n  height?: number;\n}\n\nexport function GlucoseStatsChart({ \n  statistics, \n  type = 'doughnut', \n  height = 300 \n}: GlucoseStatsChartProps) {\n  const timeInRangeData = useMemo(() => {\n    const data = {\n      labels: ['Time in Range', 'Time Above Range', 'Time Below Range'],\n      datasets: [\n        {\n          data: [\n            statistics.timeInRange,\n            statistics.timeAboveRange,\n            statistics.timeBelowRange,\n          ],\n          backgroundColor: [\n            'rgba(34, 197, 94, 0.8)', // Green for in range\n            'rgba(239, 68, 68, 0.8)',  // Red for above range\n            'rgba(59, 130, 246, 0.8)', // Blue for below range\n          ],\n          borderColor: [\n            'rgb(34, 197, 94)',\n            'rgb(239, 68, 68)',\n            'rgb(59, 130, 246)',\n          ],\n          borderWidth: 2,\n        },\n      ],\n    };\n\n    return data;\n  }, [statistics]);\n\n  const doughnutOptions = useMemo(() => ({\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      title: {\n        display: true,\n        text: 'Time in Range Analysis',\n        font: {\n          size: 14,\n          weight: 'bold' as const,\n        },\n      },\n      legend: {\n        position: 'bottom' as const,\n        labels: {\n          padding: 20,\n          usePointStyle: true,\n        },\n      },\n      tooltip: {\n        callbacks: {\n          label: (context: any) => {\n            const label = context.label;\n            const value = context.parsed;\n            const target = getTargetForRange(label);\n            \n            let tooltipText = `${label}: ${value.toFixed(1)}%`;\n            if (target) {\n              tooltipText += ` (Target: ${target})`;\n            }\n            \n            return tooltipText;\n          },\n        },\n      },\n    },\n    cutout: '60%',\n  }), []);\n\n  const barOptions = useMemo(() => ({\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      title: {\n        display: true,\n        text: 'Glucose Range Distribution',\n        font: {\n          size: 14,\n          weight: 'bold' as const,\n        },\n      },\n      legend: {\n        display: false,\n      },\n      tooltip: {\n        callbacks: {\n          label: (context: any) => {\n            const label = context.label;\n            const value = context.parsed.y;\n            const target = getTargetForRange(label);\n            \n            let tooltipText = `${label}: ${value.toFixed(1)}%`;\n            if (target) {\n              tooltipText += ` (Target: ${target})`;\n            }\n            \n            return tooltipText;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Percentage (%)',\n        },\n        ticks: {\n          callback: function(value: any) {\n            return value + '%';\n          },\n        },\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Glucose Ranges',\n        },\n      },\n    },\n  }), []);\n\n  const getTargetForRange = (label: string): string | null => {\n    switch (label) {\n      case 'Time in Range':\n        return '>70%';\n      case 'Time Above Range':\n        return '<25%';\n      case 'Time Below Range':\n        return '<4%';\n      default:\n        return null;\n    }\n  };\n\n  const getRangeStatus = (label: string, value: number): 'good' | 'warning' | 'poor' => {\n    switch (label) {\n      case 'Time in Range':\n        if (value >= 70) return 'good';\n        if (value >= 50) return 'warning';\n        return 'poor';\n      case 'Time Above Range':\n        if (value <= 25) return 'good';\n        if (value <= 50) return 'warning';\n        return 'poor';\n      case 'Time Below Range':\n        if (value <= 4) return 'good';\n        if (value <= 10) return 'warning';\n        return 'poor';\n      default:\n        return 'good';\n    }\n  };\n\n  if (type === 'doughnut') {\n    return (\n      <div style={{ height }}>\n        <Doughnut data={timeInRangeData} options={doughnutOptions} />\n        \n        {/* Center text showing average glucose */}\n        <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {statistics.averageGlucose}\n            </div>\n            <div className=\"text-sm text-gray-500\">mg/dL avg</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ height }}>\n      <Bar data={timeInRangeData} options={barOptions} />\n      \n      {/* Status indicators below the chart */}\n      <div className=\"mt-4 grid grid-cols-3 gap-2 text-xs\">\n        {timeInRangeData.labels.map((label, index) => {\n          const value = timeInRangeData.datasets[0].data[index];\n          const status = getRangeStatus(label, value);\n          const statusColor = {\n            good: 'text-green-600 bg-green-50',\n            warning: 'text-yellow-600 bg-yellow-50',\n            poor: 'text-red-600 bg-red-50',\n          }[status];\n          \n          return (\n            <div key={label} className={`p-2 rounded text-center ${statusColor}`}>\n              <div className=\"font-medium\">{value.toFixed(1)}%</div>\n              <div className=\"text-xs opacity-75\">{label.replace('Time ', '')}</div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n\n// Component for displaying glucose trends over time\ninterface GlucoseTrendChartProps {\n  statistics: CGMStatistics;\n  height?: number;\n}\n\nexport function GlucoseTrendChart({ statistics, height = 200 }: GlucoseTrendChartProps) {\n  const trendData = useMemo(() => {\n    return {\n      labels: ['Below Range', 'In Range', 'Above Range'],\n      datasets: [\n        {\n          label: 'Current Period',\n          data: [\n            statistics.timeBelowRange,\n            statistics.timeInRange,\n            statistics.timeAboveRange,\n          ],\n          backgroundColor: [\n            'rgba(59, 130, 246, 0.6)',\n            'rgba(34, 197, 94, 0.6)',\n            'rgba(239, 68, 68, 0.6)',\n          ],\n          borderColor: [\n            'rgb(59, 130, 246)',\n            'rgb(34, 197, 94)',\n            'rgb(239, 68, 68)',\n          ],\n          borderWidth: 1,\n        },\n        {\n          label: 'Target',\n          data: [4, 70, 25], // Standard diabetes targets\n          backgroundColor: [\n            'rgba(156, 163, 175, 0.3)',\n            'rgba(156, 163, 175, 0.3)',\n            'rgba(156, 163, 175, 0.3)',\n          ],\n          borderColor: 'rgb(156, 163, 175)',\n          borderWidth: 1,\n          borderDash: [5, 5],\n        },\n      ],\n    };\n  }, [statistics]);\n\n  const trendOptions = useMemo(() => ({\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      title: {\n        display: true,\n        text: 'Current vs Target Ranges',\n        font: {\n          size: 14,\n          weight: 'bold' as const,\n        },\n      },\n      legend: {\n        position: 'top' as const,\n      },\n      tooltip: {\n        callbacks: {\n          label: (context: any) => {\n            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;\n          },\n        },\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Percentage (%)',\n        },\n        ticks: {\n          callback: function(value: any) {\n            return value + '%';\n          },\n        },\n      },\n    },\n  }), []);\n\n  return (\n    <div style={{ height }}>\n      <Bar data={trendData} options={trendOptions} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AASA;AAZA;;;;;AAeA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CAAC,4JAAA,CAAA,aAAU,EAAE,4JAAA,CAAA,UAAO,EAAE,4JAAA,CAAA,SAAM,EAAE,4JAAA,CAAA,gBAAa,EAAE,4JAAA,CAAA,cAAW,EAAE,4JAAA,CAAA,aAAU;AAQ7E,SAAS,kBAAkB,EAChC,UAAU,EACV,OAAO,UAAU,EACjB,SAAS,GAAG,EACW;IACvB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,OAAO;YACX,QAAQ;gBAAC;gBAAiB;gBAAoB;aAAmB;YACjE,UAAU;gBACR;oBACE,MAAM;wBACJ,WAAW,WAAW;wBACtB,WAAW,cAAc;wBACzB,WAAW,cAAc;qBAC1B;oBACD,iBAAiB;wBACf;wBACA;wBACA;qBACD;oBACD,aAAa;wBACX;wBACA;wBACA;qBACD;oBACD,aAAa;gBACf;aACD;QACH;QAEA,OAAO;IACT,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACrC,YAAY;YACZ,qBAAqB;YACrB,SAAS;gBACP,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,UAAU;oBACV,QAAQ;wBACN,SAAS;wBACT,eAAe;oBACjB;gBACF;gBACA,SAAS;oBACP,WAAW;wBACT,OAAO,CAAC;4BACN,MAAM,QAAQ,QAAQ,KAAK;4BAC3B,MAAM,QAAQ,QAAQ,MAAM;4BAC5B,MAAM,SAAS,kBAAkB;4BAEjC,IAAI,cAAc,GAAG,MAAM,EAAE,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;4BAClD,IAAI,QAAQ;gCACV,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;4BACvC;4BAEA,OAAO;wBACT;oBACF;gBACF;YACF;YACA,QAAQ;QACV,CAAC,GAAG,EAAE;IAEN,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAChC,YAAY;YACZ,qBAAqB;YACrB,SAAS;gBACP,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,SAAS;oBACP,WAAW;wBACT,OAAO,CAAC;4BACN,MAAM,QAAQ,QAAQ,KAAK;4BAC3B,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC;4BAC9B,MAAM,SAAS,kBAAkB;4BAEjC,IAAI,cAAc,GAAG,MAAM,EAAE,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;4BAClD,IAAI,QAAQ;gCACV,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;4BACvC;4BAEA,OAAO;wBACT;oBACF;gBACF;YACF;YACA,QAAQ;gBACN,GAAG;oBACD,aAAa;oBACb,KAAK;oBACL,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;oBACA,OAAO;wBACL,UAAU,SAAS,KAAU;4BAC3B,OAAO,QAAQ;wBACjB;oBACF;gBACF;gBACA,GAAG;oBACD,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF,CAAC,GAAG,EAAE;IAEN,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,SAAS,IAAI,OAAO;gBACxB,IAAI,SAAS,IAAI,OAAO;gBACxB,OAAO;YACT,KAAK;gBACH,IAAI,SAAS,IAAI,OAAO;gBACxB,IAAI,SAAS,IAAI,OAAO;gBACxB,OAAO;YACT,KAAK;gBACH,IAAI,SAAS,GAAG,OAAO;gBACvB,IAAI,SAAS,IAAI,OAAO;gBACxB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS,YAAY;QACvB,qBACE,8OAAC;YAAI,OAAO;gBAAE;YAAO;;8BACnB,8OAAC,sJAAA,CAAA,WAAQ;oBAAC,MAAM;oBAAiB,SAAS;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,WAAW,cAAc;;;;;;0CAE5B,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAKjD;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE;QAAO;;0BACnB,8OAAC,sJAAA,CAAA,MAAG;gBAAC,MAAM;gBAAiB,SAAS;;;;;;0BAGrC,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;oBAClC,MAAM,QAAQ,gBAAgB,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;oBACrD,MAAM,SAAS,eAAe,OAAO;oBACrC,MAAM,cAAc;wBAClB,MAAM;wBACN,SAAS;wBACT,MAAM;oBACR,CAAC,CAAC,OAAO;oBAET,qBACE,8OAAC;wBAAgB,WAAW,CAAC,wBAAwB,EAAE,aAAa;;0CAClE,8OAAC;gCAAI,WAAU;;oCAAe,MAAM,OAAO,CAAC;oCAAG;;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CAAsB,MAAM,OAAO,CAAC,SAAS;;;;;;;uBAFpD;;;;;gBAKd;;;;;;;;;;;;AAIR;AAQO,SAAS,kBAAkB,EAAE,UAAU,EAAE,SAAS,GAAG,EAA0B;IACpF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO;YACL,QAAQ;gBAAC;gBAAe;gBAAY;aAAc;YAClD,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;wBACJ,WAAW,cAAc;wBACzB,WAAW,WAAW;wBACtB,WAAW,cAAc;qBAC1B;oBACD,iBAAiB;wBACf;wBACA;wBACA;qBACD;oBACD,aAAa;wBACX;wBACA;wBACA;qBACD;oBACD,aAAa;gBACf;gBACA;oBACE,OAAO;oBACP,MAAM;wBAAC;wBAAG;wBAAI;qBAAG;oBACjB,iBAAiB;wBACf;wBACA;wBACA;qBACD;oBACD,aAAa;oBACb,aAAa;oBACb,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;aACD;QACH;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAClC,YAAY;YACZ,qBAAqB;YACrB,SAAS;gBACP,OAAO;oBACL,SAAS;oBACT,MAAM;oBACN,MAAM;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,UAAU;gBACZ;gBACA,SAAS;oBACP,WAAW;wBACT,OAAO,CAAC;4BACN,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;wBACpE;oBACF;gBACF;YACF;YACA,QAAQ;gBACN,GAAG;oBACD,aAAa;oBACb,KAAK;oBACL,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;oBACA,OAAO;wBACL,UAAU,SAAS,KAAU;4BAC3B,OAAO,QAAQ;wBACjB;oBACF;gBACF;YACF;QACF,CAAC,GAAG,EAAE;IAEN,qBACE,8OAAC;QAAI,OAAO;YAAE;QAAO;kBACnB,cAAA,8OAAC,sJAAA,CAAA,MAAG;YAAC,MAAM;YAAW,SAAS;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/app/dashboard/cgm/%5BpatientId%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ArrowLeft, Activity, TrendingUp, TrendingDown, Minus, Calendar, Clock } from 'lucide-react';\nimport Link from 'next/link';\nimport { cgmApi, type PatientCGMData, type GlucoseReading } from '@/lib/api';\nimport { toast } from 'sonner';\nimport { GlucoseChart } from '@/components/cgm/glucose-chart';\nimport { GlucoseStatsChart, GlucoseTrendChart } from '@/components/cgm/glucose-stats-chart';\n\nconst getTrendIcon = (trend?: string) => {\n  switch (trend) {\n    case 'rising':\n      return <TrendingUp className=\"h-4 w-4 text-red-500\" />;\n    case 'falling':\n      return <TrendingDown className=\"h-4 w-4 text-blue-500\" />;\n    default:\n      return <Minus className=\"h-4 w-4 text-gray-500\" />;\n  }\n};\n\nconst getGlucoseColor = (value: number, targetMin: number, targetMax: number) => {\n  if (value < targetMin) {\n    return 'text-blue-600 bg-blue-100';\n  } else if (value > targetMax) {\n    return 'text-red-600 bg-red-100';\n  } else {\n    return 'text-green-600 bg-green-100';\n  }\n};\n\nexport default function PatientCGMDetailPage() {\n  const params = useParams();\n  const patientId = params.patientId as string;\n\n  const [cgmData, setCgmData] = useState<PatientCGMData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [selectedDays, setSelectedDays] = useState(7);\n\n  useEffect(() => {\n    const fetchPatientCGMData = async () => {\n      try {\n        setLoading(true);\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - selectedDays);\n\n        const response = await cgmApi.getPatientData(patientId, {\n          startDate: startDate.toISOString().split('T')[0],\n          endDate: new Date().toISOString().split('T')[0],\n          limit: selectedDays * 96, // 96 readings per day (every 15 minutes)\n        });\n\n        if (response.success) {\n          setCgmData(response.data);\n        } else {\n          toast.error('Failed to load patient CGM data');\n        }\n      } catch (error) {\n        console.error('Error fetching patient CGM data:', error);\n        toast.error('Failed to load patient CGM data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (patientId) {\n      fetchPatientCGMData();\n    }\n  }, [patientId, selectedDays]);\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/dashboard/cgm\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to CGM Overview\n            </Link>\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Patient CGM Data</h1>\n            <p className=\"text-gray-600 mt-2\">Loading glucose monitoring data...</p>\n          </div>\n        </div>\n        <div className=\"animate-pulse space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"h-24 bg-gray-200 rounded\"></div>\n            <div className=\"h-24 bg-gray-200 rounded\"></div>\n            <div className=\"h-24 bg-gray-200 rounded\"></div>\n            <div className=\"h-24 bg-gray-200 rounded\"></div>\n          </div>\n          <div className=\"h-96 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!cgmData) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/dashboard/cgm\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to CGM Overview\n            </Link>\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Patient Not Found</h1>\n            <p className=\"text-gray-600 mt-2\">The requested patient could not be found.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const { patient, cgmData: data } = cgmData;\n  const { statistics, readings } = data;\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/dashboard/cgm\">\n              <ArrowLeft className=\"mr-2 h-4 w-4\" />\n              Back to CGM Overview\n            </Link>\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              {patient.firstName} {patient.lastName}\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Continuous glucose monitoring data • {patient.email}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <Select value={selectedDays.toString()} onValueChange={(value) => setSelectedDays(parseInt(value))}>\n            <SelectTrigger className=\"w-32\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"1\">1 Day</SelectItem>\n              <SelectItem value=\"3\">3 Days</SelectItem>\n              <SelectItem value=\"7\">7 Days</SelectItem>\n              <SelectItem value=\"14\">14 Days</SelectItem>\n              <SelectItem value=\"30\">30 Days</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Average Glucose</CardTitle>\n            <Activity className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{statistics.averageGlucose} mg/dL</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Target: {statistics.targetRange.min}-{statistics.targetRange.max} mg/dL\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Time in Range</CardTitle>\n            <div className={`h-4 w-4 rounded-full ${statistics.timeInRange >= 70 ? 'bg-green-500' : statistics.timeInRange >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`} />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{statistics.timeInRange}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              {statistics.targetRange.min}-{statistics.targetRange.max} mg/dL\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Time Above Range</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-red-500\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">{statistics.timeAboveRange}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Above {statistics.targetRange.max} mg/dL\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Time Below Range</CardTitle>\n            <TrendingDown className=\"h-4 w-4 text-blue-500\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-blue-600\">{statistics.timeBelowRange}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Below {statistics.targetRange.min} mg/dL\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Glucose Chart */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Glucose Trend Chart</CardTitle>\n          <CardDescription>\n            Continuous glucose monitoring over the last {selectedDays} days\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <GlucoseChart\n            readings={readings}\n            statistics={statistics}\n            height={400}\n            showTargetRange={true}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Analytics Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Time in Range Analysis</CardTitle>\n            <CardDescription>\n              Glucose control distribution\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"relative\">\n              <GlucoseStatsChart\n                statistics={statistics}\n                type=\"doughnut\"\n                height={300}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Performance vs Targets</CardTitle>\n            <CardDescription>\n              Current glucose control vs clinical targets\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <GlucoseTrendChart\n              statistics={statistics}\n              height={300}\n            />\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Recent Readings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Glucose Readings</CardTitle>\n          <CardDescription>\n            Latest {Math.min(20, readings.length)} glucose readings from the last {selectedDays} days\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {readings.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Activity className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No CGM Data Available</h3>\n              <p className=\"text-gray-500\">\n                No glucose readings found for this patient in the selected time period.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {readings.slice(0, 20).map((reading) => (\n                <div key={reading.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${getGlucoseColor(reading.value, statistics.targetRange.min, statistics.targetRange.max)}`}>\n                      {reading.value} mg/dL\n                    </div>\n                    {getTrendIcon(reading.trend)}\n                    {reading.trend && reading.trendRate && (\n                      <span className=\"text-sm text-gray-500\">\n                        {reading.trendRate > 0 ? '+' : ''}{reading.trendRate.toFixed(1)} mg/dL/min\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Calendar className=\"h-4 w-4\" />\n                      <span>{new Date(reading.timestamp).toLocaleDateString()}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>{new Date(reading.timestamp).toLocaleTimeString()}</span>\n                    </div>\n                    {reading.source && (\n                      <Badge variant=\"outline\">{reading.source}</Badge>\n                    )}\n                  </div>\n                </div>\n              ))}\n\n              {readings.length > 20 && (\n                <div className=\"text-center pt-4\">\n                  <p className=\"text-sm text-gray-500\">\n                    Showing 20 of {readings.length} total readings\n                  </p>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Summary Info */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Data Summary</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div>\n              <span className=\"font-medium\">Total Readings:</span>\n              <span className=\"ml-2\">{statistics.totalReadings}</span>\n            </div>\n            <div>\n              <span className=\"font-medium\">Date Range:</span>\n              <span className=\"ml-2\">\n                {new Date(data.dateRange.startDate).toLocaleDateString()} - {new Date(data.dateRange.endDate).toLocaleDateString()}\n              </span>\n            </div>\n            <div>\n              <span className=\"font-medium\">Data Source:</span>\n              <span className=\"ml-2\">{readings[0]?.source || 'Unknown'}</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeA,MAAM,eAAe,CAAC;IACpB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,KAAK;YACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;QACjC;YACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;IAC5B;AACF;AAEA,MAAM,kBAAkB,CAAC,OAAe,WAAmB;IACzD,IAAI,QAAQ,WAAW;QACrB,OAAO;IACT,OAAO,IAAI,QAAQ,WAAW;QAC5B,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI;gBACF,WAAW;gBACX,MAAM,YAAY,IAAI;gBACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBAExC,MAAM,WAAW,MAAM,iHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,WAAW;oBACtD,WAAW,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChD,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC/C,OAAO,eAAe;gBACxB;gBAEA,IAAI,SAAS,OAAO,EAAE;oBACpB,WAAW,SAAS,IAAI;gBAC1B,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAW;KAAa;IAE5B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,OAAO;sCACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;8BAGtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,OAAO;kCACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,MAAM,EAAE,OAAO,EAAE,SAAS,IAAI,EAAE,GAAG;IACnC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,OAAO;0CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CACX,QAAQ,SAAS;4CAAC;4CAAE,QAAQ,QAAQ;;;;;;;kDAEvC,8OAAC;wCAAE,WAAU;;4CAAqB;4CACM,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO,aAAa,QAAQ;4BAAI,eAAe,CAAC,QAAU,gBAAgB,SAAS;;8CACzF,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8CAEd,8OAAC,kIAAA,CAAA,gBAAa;;sDACZ,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAI;;;;;;sDACtB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAI;;;;;;sDACtB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAI;;;;;;sDACtB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAK;;;;;;sDACvB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAsB,WAAW,cAAc;4CAAC;;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;;4CAAgC;4CAClC,WAAW,WAAW,CAAC,GAAG;4CAAC;4CAAE,WAAW,WAAW,CAAC,GAAG;4CAAC;;;;;;;;;;;;;;;;;;;kCAKvE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,WAAW,IAAI,KAAK,iBAAiB,WAAW,WAAW,IAAI,KAAK,kBAAkB,cAAc;;;;;;;;;;;;0CAEzJ,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAsB,WAAW,WAAW;4CAAC;;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;;4CACV,WAAW,WAAW,CAAC,GAAG;4CAAC;4CAAE,WAAW,WAAW,CAAC,GAAG;4CAAC;;;;;;;;;;;;;;;;;;;kCAK/D,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAmC,WAAW,cAAc;4CAAC;;;;;;;kDAC5E,8OAAC;wCAAE,WAAU;;4CAAgC;4CACpC,WAAW,WAAW,CAAC,GAAG;4CAAC;;;;;;;;;;;;;;;;;;;kCAKxC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;4CAAoC,WAAW,cAAc;4CAAC;;;;;;;kDAC7E,8OAAC;wCAAE,WAAU;;4CAAgC;4CACpC,WAAW,WAAW,CAAC,GAAG;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCAC8B;oCAAa;;;;;;;;;;;;;kCAG9D,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,6IAAA,CAAA,eAAY;4BACX,UAAU;4BACV,YAAY;4BACZ,QAAQ;4BACR,iBAAiB;;;;;;;;;;;;;;;;;0BAMvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sJAAA,CAAA,oBAAiB;wCAChB,YAAY;wCACZ,MAAK;wCACL,QAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,sJAAA,CAAA,oBAAiB;oCAChB,YAAY;oCACZ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCACP,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;oCAAE;oCAAiC;oCAAa;;;;;;;;;;;;;kCAGxF,8OAAC,gIAAA,CAAA,cAAW;kCACT,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAK/B,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBAC1B,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,2CAA2C,EAAE,gBAAgB,QAAQ,KAAK,EAAE,WAAW,WAAW,CAAC,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,GAAG;;4DACnJ,QAAQ,KAAK;4DAAC;;;;;;;oDAEhB,aAAa,QAAQ,KAAK;oDAC1B,QAAQ,KAAK,IAAI,QAAQ,SAAS,kBACjC,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,SAAS,GAAG,IAAI,MAAM;4DAAI,QAAQ,SAAS,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAItE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;oDAEtD,QAAQ,MAAM,kBACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,QAAQ,MAAM;;;;;;;;;;;;;uCAtBpC,QAAQ,EAAE;;;;;gCA4BrB,SAAS,MAAM,GAAG,oBACjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAwB;4CACpB,SAAS,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAQ,WAAW,aAAa;;;;;;;;;;;;8CAElD,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;;gDACb,IAAI,KAAK,KAAK,SAAS,CAAC,SAAS,EAAE,kBAAkB;gDAAG;gDAAI,IAAI,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;8CAGpH,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAQ,QAAQ,CAAC,EAAE,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}]}