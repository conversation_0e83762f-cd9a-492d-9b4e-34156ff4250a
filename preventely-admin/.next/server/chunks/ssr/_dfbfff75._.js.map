{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation';\n\nexport default function Home() {\n  // Redirect to the dashboard\n  redirect('/dashboard');\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS;IACtB,4BAA4B;IAC5B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}