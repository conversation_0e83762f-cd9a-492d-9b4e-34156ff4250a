{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\n// Types for API responses\nexport interface Patient {\n  id: string;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  dateOfBirth?: string;\n  gender?: string;\n  address?: string;\n  assessments: Assessment[];\n  invitations: PatientInvitation[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Assessment {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  age: string;\n  bmi: string;\n  waistCircumference: string;\n  physicalActivity: string;\n  vegetableConsumption: string;\n  bloodPressureMedication: string;\n  highGlucoseHistory: string;\n  familyDiabetesHistory: string;\n  riskScore: number;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  notes?: string;\n  assessedBy?: string;\n  assessmentDate: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PatientInvitation {\n  id: string;\n  patientId: string;\n  patient?: Patient;\n  email: string;\n  riskGroup: 'A' | 'B' | 'C' | 'D';\n  riskScore: number;\n  invitationSent: boolean;\n  sentAt?: string;\n  accountCreated: boolean;\n  accountCreatedAt?: string;\n  emailOpened: boolean;\n  emailOpenedAt?: string;\n  appDownloaded: boolean;\n  appDownloadedAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface AdminSettings {\n  id: string;\n  smtpHost?: string;\n  smtpPort?: string;\n  smtpUser?: string;\n  fromEmail?: string;\n  fromName?: string;\n  appName?: string;\n  clinicName?: string;\n  clinicAddress?: string;\n  clinicPhone?: string;\n  iosAppUrl?: string;\n  androidAppUrl?: string;\n  webAppUrl?: string;\n  emailNotifications: boolean;\n  assessmentAlerts: boolean;\n  highRiskAlerts: boolean;\n  dailyReports: boolean;\n  weeklyReports: boolean;\n  monthlyReports: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RiskGroupStats {\n  totalAssessments: number;\n  averageScore: number;\n  riskDistribution: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    percentage: number;\n  }>;\n  monthlyTrends: Array<{\n    date: string;\n    assessments: number;\n    averageScore: number;\n  }>;\n}\n\nexport interface SummaryReport {\n  summary: {\n    totalAssessments: number;\n    highRiskPatients: number;\n    invitationsSent: number;\n    accountsCreated: number;\n  };\n  riskGroupBreakdown: Array<{\n    group: 'A' | 'B' | 'C' | 'D';\n    count: number;\n    averageScore: number;\n  }>;\n  recentAssessments: Assessment[];\n  dateRange: {\n    startDate: string;\n    endDate: string;\n  };\n}\n\nexport interface GlucoseReading {\n  id: string;\n  value: number;\n  timestamp: string;\n  trend?: string;\n  trendRate?: number;\n  source?: string;\n  notes?: string;\n}\n\nexport interface CGMStatistics {\n  totalReadings: number;\n  averageGlucose: number;\n  timeInRange: number;\n  timeAboveRange: number;\n  timeBelowRange: number;\n  targetRange: {\n    min: number;\n    max: number;\n  };\n}\n\nexport interface PatientCGMData {\n  patient: Patient;\n  cgmData: {\n    readings: GlucoseReading[];\n    statistics: CGMStatistics;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n    };\n  };\n}\n\nexport interface CGMOverview {\n  overview: Array<{\n    patient: {\n      id: string;\n      firstName: string;\n      lastName: string;\n      email: string;\n      riskGroup: 'A' | 'B' | 'C' | 'D' | null;\n      riskScore: number | null;\n    };\n    cgmSummary: {\n      totalReadings: number;\n      averageGlucose: number;\n      timeInRange: number;\n      latestReading: {\n        value: number;\n        timestamp: string;\n        trend?: string;\n      };\n      targetRange: {\n        min: number;\n        max: number;\n      };\n    };\n  }>;\n  summary: {\n    totalPatientsWithCGM: number;\n    totalPatients: number;\n    dateRange: {\n      startDate: string;\n      endDate: string;\n      days: number;\n    };\n  };\n}\n\n// API response wrapper\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\ninterface PaginatedResponse<T> {\n  success: boolean;\n  data: {\n    [key: string]: T[];\n    pagination: {\n      page: number;\n      limit: number;\n      total: number;\n      pages: number;\n    };\n  };\n}\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  const config: RequestInit = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    },\n    ...options,\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(`API request failed for ${endpoint}:`, error);\n    throw error;\n  }\n}\n\n// Assessment API functions\nexport const assessmentApi = {\n  // Create new assessment\n  create: async (assessmentData: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone?: string;\n    age: string;\n    bmi: string;\n    waistCircumference: string;\n    physicalActivity: string;\n    vegetableConsumption: string;\n    bloodPressureMedication: string;\n    highGlucoseHistory: string;\n    familyDiabetesHistory: string;\n    notes?: string;\n    assessedBy?: string;\n  }): Promise<ApiResponse<{ assessment: Assessment; riskScore: number; riskGroup: string }>> => {\n    return apiRequest('/admin/assessments', {\n      method: 'POST',\n      body: JSON.stringify(assessmentData),\n    });\n  },\n\n  // Get all assessments\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    riskGroup?: 'A' | 'B' | 'C' | 'D';\n  }): Promise<PaginatedResponse<Assessment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.riskGroup) searchParams.append('riskGroup', params.riskGroup);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/assessments${query ? `?${query}` : ''}`);\n  },\n};\n\n// Patient API functions\nexport const patientApi = {\n  // Get all patients\n  getAll: async (params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n  }): Promise<PaginatedResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/patients${query ? `?${query}` : ''}`);\n  },\n};\n\n// Invitation API functions\nexport const invitationApi = {\n  // Send invitation\n  send: async (invitationData: {\n    patientId: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    riskGroup: 'A' | 'B' | 'C' | 'D';\n    riskScore: number;\n  }): Promise<ApiResponse<{ message: string }>> => {\n    return apiRequest('/admin/send-invitation', {\n      method: 'POST',\n      body: JSON.stringify(invitationData),\n    });\n  },\n};\n\n// Risk Groups API functions\nexport const riskGroupApi = {\n  // Get risk group statistics\n  getStats: async (): Promise<ApiResponse<RiskGroupStats>> => {\n    return apiRequest('/admin/risk-groups/stats');\n  },\n};\n\n// Reports API functions\nexport const reportsApi = {\n  // Get summary report\n  getSummary: async (params?: {\n    startDate?: string;\n    endDate?: string;\n  }): Promise<ApiResponse<SummaryReport>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n\n    const query = searchParams.toString();\n    return apiRequest(`/admin/reports/summary${query ? `?${query}` : ''}`);\n  },\n};\n\n// Settings API functions\nexport const settingsApi = {\n  // Get settings\n  get: async (): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings');\n  },\n\n  // Update settings\n  update: async (settings: Partial<AdminSettings>): Promise<ApiResponse<AdminSettings>> => {\n    return apiRequest('/admin/settings', {\n      method: 'PUT',\n      body: JSON.stringify(settings),\n    });\n  },\n};\n\n// CGM API functions\nexport const cgmApi = {\n  // Get CGM data for a specific patient\n  getPatientData: async (\n    patientId: string,\n    params?: {\n      startDate?: string;\n      endDate?: string;\n      limit?: number;\n    }\n  ): Promise<ApiResponse<PatientCGMData>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.append('startDate', params.startDate);\n    if (params?.endDate) searchParams.append('endDate', params.endDate);\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/patients/${patientId}/cgm-data${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n\n  // Get CGM overview for all patients\n  getOverview: async (days?: number): Promise<ApiResponse<CGMOverview>> => {\n    const searchParams = new URLSearchParams();\n    if (days) searchParams.append('days', days.toString());\n\n    const queryString = searchParams.toString();\n    const url = `/admin/cgm-overview${queryString ? `?${queryString}` : ''}`;\n\n    return apiRequest(url);\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAqB;AAArB,MAAM,eAAe,iEAAmC;AA+MxD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,eAAe,UAAU;IAExC,MAAM,SAAsB;QAC1B,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;QACrD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,QAAQ,OAAO;QAgBb,OAAO,WAAW,sBAAsB;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,sBAAsB;IACtB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QAExE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnE;AACF;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,QAAQ,OAAO;QAKb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAChE;AACF;AAGO,MAAM,gBAAgB;IAC3B,kBAAkB;IAClB,MAAM,OAAO;QAQX,OAAO,WAAW,0BAA0B;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,4BAA4B;IAC5B,UAAU;QACR,OAAO,WAAW;IACpB;AACF;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,YAAY,OAAO;QAIjB,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACvE;AACF;AAGO,MAAM,cAAc;IACzB,eAAe;IACf,KAAK;QACH,OAAO,WAAW;IACpB;IAEA,kBAAkB;IAClB,QAAQ,OAAO;QACb,OAAO,WAAW,mBAAmB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,SAAS;IACpB,sCAAsC;IACtC,gBAAgB,OACd,WACA;QAMA,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,aAAa,OAAO,SAAS;QACxE,IAAI,QAAQ,SAAS,aAAa,MAAM,CAAC,WAAW,OAAO,OAAO;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAErE,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,gBAAgB,EAAE,UAAU,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE1F,OAAO,WAAW;IACpB;IAEA,oCAAoC;IACpC,aAAa,OAAO;QAClB,MAAM,eAAe,IAAI;QACzB,IAAI,MAAM,aAAa,MAAM,CAAC,QAAQ,KAAK,QAAQ;QAEnD,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,MAAM,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAExE,OAAO,WAAW;IACpB;AACF", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Users, UserPlus, Activity, Mail, TrendingUp, AlertTriangle } from 'lucide-react';\nimport Link from 'next/link';\nimport { reportsApi, assessmentApi, type SummaryReport, type Assessment } from '@/lib/api';\n\nexport default function DashboardPage() {\n  const [summaryData, setSummaryData] = useState<SummaryReport | null>(null);\n  const [recentAssessments, setRecentAssessments] = useState<Assessment[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch summary report\n        const summaryResponse = await reportsApi.getSummary();\n        if (summaryResponse.success) {\n          setSummaryData(summaryResponse.data);\n        }\n\n        // Fetch recent assessments\n        const assessmentsResponse = await assessmentApi.getAll({ limit: 5 });\n        if (assessmentsResponse.success) {\n          setRecentAssessments(assessmentsResponse.data.assessments || []);\n        }\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, []);\n\n  const getRiskGroupBadgeVariant = (group: string) => {\n    switch (group) {\n      case 'A': return 'default';\n      case 'B': return 'secondary';\n      case 'C': return 'destructive';\n      case 'D': return 'destructive';\n      default: return 'default';\n    }\n  };\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Clinic Dashboard</h1>\n        <p className=\"text-gray-600 mt-2\">\n          Manage patient assessments and prediabetes risk evaluations\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Assessments</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {loading ? '...' : summaryData?.summary.totalAssessments || 0}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              Total patient assessments\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">High Risk Patients</CardTitle>\n            <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-red-600\">\n              {loading ? '...' : summaryData?.summary.highRiskPatients || 0}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              Groups C & D patients\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Invitations Sent</CardTitle>\n            <Mail className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {loading ? '...' : summaryData?.summary.invitationsSent || 0}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              App invitations sent\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Accounts Created</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-green-500\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-green-600\">\n              {loading ? '...' : summaryData?.summary.accountsCreated || 0}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              App accounts created\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n            <CardDescription>\n              Start a new patient assessment or manage existing ones\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <Link href=\"/dashboard/assessment\">\n              <Button className=\"w-full\" size=\"lg\">\n                <UserPlus className=\"mr-2 h-4 w-4\" />\n                New Patient Assessment\n              </Button>\n            </Link>\n            <Link href=\"/dashboard/patients\">\n              <Button variant=\"outline\" className=\"w-full\" size=\"lg\">\n                <Users className=\"mr-2 h-4 w-4\" />\n                View All Patients\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Recent Assessments</CardTitle>\n            <CardDescription>\n              Latest patient risk assessments\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"space-y-4\">\n                <div className=\"animate-pulse\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n                <div className=\"animate-pulse\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n                <div className=\"animate-pulse\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ) : recentAssessments.length > 0 ? (\n              <div className=\"space-y-4\">\n                {recentAssessments.map((assessment) => (\n                  <div key={assessment.id} className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"font-medium\">\n                        {assessment.patient?.firstName} {assessment.patient?.lastName}\n                      </p>\n                      <p className=\"text-sm text-gray-600\">\n                        {assessment.patient?.email}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        Score: {assessment.riskScore} points • {new Date(assessment.assessmentDate).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <Badge variant={getRiskGroupBadgeVariant(assessment.riskGroup) as any}>\n                      Group {assessment.riskGroup}\n                    </Badge>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">No recent assessments</p>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;8DAAqB;oBACzB,IAAI;wBACF,WAAW;wBAEX,uBAAuB;wBACvB,MAAM,kBAAkB,MAAM,oHAAA,CAAA,aAAU,CAAC,UAAU;wBACnD,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,eAAe,gBAAgB,IAAI;wBACrC;wBAEA,2BAA2B;wBAC3B,MAAM,sBAAsB,MAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;4BAAE,OAAO;wBAAE;wBAClE,IAAI,oBAAoB,OAAO,EAAE;4BAC/B,qBAAqB,oBAAoB,IAAI,CAAC,WAAW,IAAI,EAAE;wBACjE;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,2BAA2B,CAAC;QAChC,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,UAAU,QAAQ,aAAa,QAAQ,oBAAoB;;;;;;kDAE9D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,UAAU,QAAQ,aAAa,QAAQ,oBAAoB;;;;;;kDAE9D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAElB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,UAAU,QAAQ,aAAa,QAAQ,mBAAmB;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,UAAU,QAAQ,aAAa,QAAQ,mBAAmB;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAS,MAAK;;8DAC9B,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIzC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;4CAAS,MAAK;;8DAChD,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACT,wBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;2CAGjB,kBAAkB,MAAM,GAAG,kBAC7B,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;;gEACV,WAAW,OAAO,EAAE;gEAAU;gEAAE,WAAW,OAAO,EAAE;;;;;;;sEAEvD,6LAAC;4DAAE,WAAU;sEACV,WAAW,OAAO,EAAE;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;;gEAAwB;gEAC3B,WAAW,SAAS;gEAAC;gEAAW,IAAI,KAAK,WAAW,cAAc,EAAE,kBAAkB;;;;;;;;;;;;;8DAGlG,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,yBAAyB,WAAW,SAAS;;wDAAU;wDAC9D,WAAW,SAAS;;;;;;;;2CAbrB,WAAW,EAAE;;;;;;;;;yDAmB3B,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;GA9LwB;KAAA", "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}