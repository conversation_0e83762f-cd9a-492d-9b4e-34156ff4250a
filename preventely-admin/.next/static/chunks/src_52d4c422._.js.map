{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/dashboard/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { \n  LayoutDashboard, \n  Users, \n  UserPlus, \n  FileText, \n  Settings,\n  Activity\n} from 'lucide-react';\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'New Assessment',\n    href: '/dashboard/assessment',\n    icon: UserPlus,\n  },\n  {\n    name: 'Patients',\n    href: '/dashboard/patients',\n    icon: Users,\n  },\n  {\n    name: 'Risk Groups',\n    href: '/dashboard/risk-groups',\n    icon: Activity,\n  },\n  {\n    name: 'Reports',\n    href: '/dashboard/reports',\n    icon: FileText,\n  },\n  {\n    name: 'Settings',\n    href: '/dashboard/settings',\n    icon: Settings,\n  },\n];\n\nexport function Sidebar() {\n  const pathname = usePathname();\n\n  return (\n    <div className=\"w-64 bg-white shadow-sm border-r border-gray-200\">\n      <div className=\"flex flex-col h-full\">\n        {/* Logo */}\n        <div className=\"flex items-center px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <Activity className=\"w-5 h-5 text-white\" />\n            </div>\n            <div className=\"ml-3\">\n              <h1 className=\"text-lg font-semibold text-gray-900\">Preventely</h1>\n              <p className=\"text-xs text-gray-500\">Clinic Dashboard</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href;\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                  isActive\n                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                )}\n              >\n                <item.icon className=\"mr-3 h-5 w-5\" />\n                {item.name}\n              </Link>\n            );\n          })}\n        </nav>\n\n        {/* Footer */}\n        <div className=\"px-4 py-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500\">\n            <p>Preventely Admin v1.0</p>\n            <p>© 2024 Healthcare Solutions</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAcA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAM3C,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;8CAGN,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;gCACpB,KAAK,IAAI;;2BAVL,KAAK,IAAI;;;;;oBAapB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAnDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/dashboard/header.tsx"], "sourcesContent": ["'use client';\n\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Bell, Search, User } from 'lucide-react';\nimport { Input } from '@/components/ui/input';\n\nexport function Header() {\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Search */}\n        <div className=\"flex items-center flex-1 max-w-md\">\n          <div className=\"relative w-full\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              type=\"text\"\n              placeholder=\"Search patients...\"\n              className=\"pl-10 pr-4 py-2 w-full\"\n            />\n          </div>\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n            <Bell className=\"h-5 w-5\" />\n            <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center\">\n              3\n            </span>\n          </Button>\n\n          {/* User Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-gray-900\">Dr. Sarah Wilson</p>\n              <p className=\"text-xs text-gray-500\">Healthcare Professional</p>\n            </div>\n            <Button variant=\"ghost\" size=\"sm\" className=\"rounded-full\">\n              <User className=\"h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;;8CAC1C,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAA+G;;;;;;;;;;;;sCAMjI,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;KAxCgB", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/FREELANCE/Prev-proj/preventely-admin/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}]}