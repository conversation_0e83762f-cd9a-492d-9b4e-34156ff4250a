# Preventely Admin Dashboard

A comprehensive clinic dashboard for healthcare professionals to assess prediabetes risk using the Finnish Diabetes Risk Score system and manage patient invitations to the Preventely mobile app.

## 🏥 Features

### Core Functionality
- **Finnish Diabetes Risk Score Assessment** - Complete 8-question assessment tool
- **Risk Group Classification** - Automatic assignment to groups A, B, C, or D
- **Patient Management** - View and manage all assessed patients
- **Email Invitations** - Send personalized app download invitations
- **Beautiful UI** - Built with shadcn/ui components

### Assessment Features
- ✅ Age-based scoring (0-4 points)
- ✅ BMI calculation and scoring (0-3 points)
- ✅ Waist circumference assessment (0-4 points)
- ✅ Physical activity evaluation (0-2 points)
- ✅ Dietary habits assessment (0-1 points)
- ✅ Blood pressure medication history (0-2 points)
- ✅ High glucose history (0-5 points)
- ✅ Family diabetes history (0-5 points)

### Risk Groups
- **Group A (0-6 points)**: Low Risk - 1% chance of developing diabetes
- **Group B (7-11 points)**: Slightly Elevated Risk - 4% chance
- **Group C (12-14 points)**: Moderate Risk - 17% chance
- **Group D (15+ points)**: High Risk - 33% chance

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Email service credentials (Gmail, SendGrid, etc.)

### Installation

1. **Clone and install dependencies:**
```bash
cd preventely-admin
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

3. **Run the development server:**
```bash
npm run dev
```

4. **Open your browser:**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📱 Dashboard Pages

### Main Dashboard (`/dashboard`)
- Overview statistics
- Quick actions
- Recent assessments
- Patient metrics

### New Assessment (`/dashboard/assessment`)
- Complete Finnish Diabetes Risk Score form
- Real-time risk calculation
- Instant results with risk group assignment
- Send email invitations

### Patient Management (`/dashboard/patients`)
- View all assessed patients
- Search and filter functionality
- Patient details modal
- Invitation status tracking
- Resend invitations

## 🎯 Finnish Diabetes Risk Score

The dashboard implements the official Finnish Diabetes Risk Score (FINDRISC) questionnaire:

### Scoring System
1. **Age**: 0-4 points based on age groups
2. **BMI**: 0-3 points based on body mass index
3. **Waist Circumference**: 0-4 points (gender-specific)
4. **Physical Activity**: 0-2 points
5. **Vegetable Consumption**: 0-1 points
6. **Blood Pressure Medication**: 0-2 points
7. **High Glucose History**: 0-5 points
8. **Family Diabetes History**: 0-5 points

### Risk Interpretation
- **0-6 points**: Low risk (1% chance)
- **7-11 points**: Slightly elevated (4% chance)
- **12-14 points**: Moderate risk (17% chance)
- **15+ points**: High risk (33% chance)

## 📧 Email Integration

The dashboard sends personalized email invitations containing:
- Assessment results and risk group
- Personalized health recommendations
- Mobile app download links (iOS/Android)
- Account registration link
- Risk-specific guidance

### Email Templates
- **Group A**: Maintenance and prevention focus
- **Group B**: Lifestyle modification recommendations
- **Group C**: Intervention and monitoring guidance
- **Group D**: Intensive support and immediate action

## 🛠 Technology Stack

- **Framework**: Next.js 14 with App Router
- **UI Components**: shadcn/ui + Tailwind CSS
- **Forms**: React Hook Form + Zod validation
- **Email**: Nodemailer
- **Icons**: Lucide React
- **Notifications**: Sonner

## 🔧 Configuration

### Email Setup
Configure your email service in `.env.local`:

**Gmail:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

**SendGrid:**
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
```

### App Links
Update the mobile app download links in:
- `src/app/api/send-invitation/route.ts`

## 📊 Future Enhancements

- [ ] Database integration (PostgreSQL/MongoDB)
- [ ] User authentication and roles
- [ ] Advanced analytics and reporting
- [ ] Export functionality (PDF/Excel)
- [ ] Multi-language support
- [ ] Integration with EHR systems
- [ ] Appointment scheduling
- [ ] Progress tracking dashboard

## 🤝 Support

For questions or support:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

## 📄 License

© 2024 Preventely Health Solutions. All rights reserved.

---

**Built with ❤️ for healthcare professionals**
