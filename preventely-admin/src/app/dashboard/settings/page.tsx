'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { toast } from 'sonner';
import { 
  ArrowLeft, 
  Settings, 
  Mail, 
  Shield, 
  Bell, 
  Smartphone,
  Save,
  TestTube,
  Database,
  Globe
} from 'lucide-react';
import Link from 'next/link';

// Settings schemas
const emailSettingsSchema = z.object({
  smtpHost: z.string().min(1, 'SMTP host is required'),
  smtpPort: z.string().min(1, 'SMTP port is required'),
  smtpUser: z.string().email('Valid email is required'),
  smtpPassword: z.string().min(1, 'SMTP password is required'),
  fromEmail: z.string().email('Valid from email is required'),
  fromName: z.string().min(1, 'From name is required'),
});

const appSettingsSchema = z.object({
  appName: z.string().min(1, 'App name is required'),
  clinicName: z.string().min(1, 'Clinic name is required'),
  clinicAddress: z.string().min(1, 'Clinic address is required'),
  clinicPhone: z.string().min(1, 'Clinic phone is required'),
  iosAppUrl: z.string().url('Valid iOS app URL is required'),
  androidAppUrl: z.string().url('Valid Android app URL is required'),
  webAppUrl: z.string().url('Valid web app URL is required'),
});

const notificationSettingsSchema = z.object({
  emailNotifications: z.boolean(),
  assessmentAlerts: z.boolean(),
  highRiskAlerts: z.boolean(),
  dailyReports: z.boolean(),
  weeklyReports: z.boolean(),
  monthlyReports: z.boolean(),
});

type EmailSettings = z.infer<typeof emailSettingsSchema>;
type AppSettings = z.infer<typeof appSettingsSchema>;
type NotificationSettings = z.infer<typeof notificationSettingsSchema>;

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false);

  // Email Settings Form
  const emailForm = useForm<EmailSettings>({
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: '587',
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'Preventely Health',
    },
  });

  // App Settings Form
  const appForm = useForm<AppSettings>({
    resolver: zodResolver(appSettingsSchema),
    defaultValues: {
      appName: 'Preventely',
      clinicName: 'Healthcare Clinic',
      clinicAddress: '123 Health Street, Medical City, MC 12345',
      clinicPhone: '+****************',
      iosAppUrl: 'https://apps.apple.com/app/preventely',
      androidAppUrl: 'https://play.google.com/store/apps/details?id=com.preventely',
      webAppUrl: 'https://app.preventely.com',
    },
  });

  // Notification Settings Form
  const notificationForm = useForm<NotificationSettings>({
    resolver: zodResolver(notificationSettingsSchema),
    defaultValues: {
      emailNotifications: true,
      assessmentAlerts: true,
      highRiskAlerts: true,
      dailyReports: false,
      weeklyReports: true,
      monthlyReports: true,
    },
  });

  const onEmailSettingsSubmit = async (data: EmailSettings) => {
    setIsLoading(true);
    try {
      // Here you would save email settings to your backend
      console.log('Email settings:', data);
      toast.success('Email settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save email settings');
    } finally {
      setIsLoading(false);
    }
  };

  const onAppSettingsSubmit = async (data: AppSettings) => {
    setIsLoading(true);
    try {
      // Here you would save app settings to your backend
      console.log('App settings:', data);
      toast.success('App settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save app settings');
    } finally {
      setIsLoading(false);
    }
  };

  const onNotificationSettingsSubmit = async (data: NotificationSettings) => {
    setIsLoading(true);
    try {
      // Here you would save notification settings to your backend
      console.log('Notification settings:', data);
      toast.success('Notification settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  const testEmailConnection = async () => {
    const emailData = emailForm.getValues();
    setIsLoading(true);
    try {
      // Here you would test the email connection
      console.log('Testing email connection with:', emailData);
      toast.success('Email connection test successful!');
    } catch (error) {
      toast.error('Email connection test failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-2">
              Configure your clinic dashboard and application settings
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="email" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="email" className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <span>Email</span>
          </TabsTrigger>
          <TabsTrigger value="app" className="flex items-center space-x-2">
            <Smartphone className="h-4 w-4" />
            <span>App Settings</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                Email Configuration
              </CardTitle>
              <CardDescription>
                Configure SMTP settings for sending patient invitations and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...emailForm}>
                <form onSubmit={emailForm.handleSubmit(onEmailSettingsSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={emailForm.control}
                      name="smtpHost"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SMTP Host</FormLabel>
                          <FormControl>
                            <Input placeholder="smtp.gmail.com" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={emailForm.control}
                      name="smtpPort"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SMTP Port</FormLabel>
                          <FormControl>
                            <Input placeholder="587" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={emailForm.control}
                      name="smtpUser"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SMTP Username</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={emailForm.control}
                      name="smtpPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SMTP Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="Your app password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={emailForm.control}
                      name="fromEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={emailForm.control}
                      name="fromName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Preventely Health" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="flex space-x-4">
                    <Button type="submit" disabled={isLoading}>
                      <Save className="mr-2 h-4 w-4" />
                      Save Email Settings
                    </Button>
                    <Button type="button" variant="outline" onClick={testEmailConnection} disabled={isLoading}>
                      <TestTube className="mr-2 h-4 w-4" />
                      Test Connection
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="app" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Smartphone className="mr-2 h-5 w-5" />
                Application Settings
              </CardTitle>
              <CardDescription>
                Configure clinic information and mobile app links
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...appForm}>
                <form onSubmit={appForm.handleSubmit(onAppSettingsSubmit)} className="space-y-6">
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={appForm.control}
                        name="appName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Application Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Preventely" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={appForm.control}
                        name="clinicName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Clinic Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Healthcare Clinic" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={appForm.control}
                      name="clinicAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Clinic Address</FormLabel>
                          <FormControl>
                            <Textarea placeholder="123 Health Street, Medical City, MC 12345" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={appForm.control}
                      name="clinicPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Clinic Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="+****************" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Mobile App Links</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={appForm.control}
                          name="iosAppUrl"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>iOS App Store URL</FormLabel>
                              <FormControl>
                                <Input placeholder="https://apps.apple.com/app/preventely" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={appForm.control}
                          name="androidAppUrl"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Android Play Store URL</FormLabel>
                              <FormControl>
                                <Input placeholder="https://play.google.com/store/apps/details?id=com.preventely" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={appForm.control}
                        name="webAppUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Web App URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://app.preventely.com" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <Button type="submit" disabled={isLoading}>
                    <Save className="mr-2 h-4 w-4" />
                    Save App Settings
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="mr-2 h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>
                Configure when and how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...notificationForm}>
                <form onSubmit={notificationForm.handleSubmit(onNotificationSettingsSubmit)} className="space-y-6">
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">General Notifications</h3>
                      <FormField
                        control={notificationForm.control}
                        name="emailNotifications"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Email Notifications</FormLabel>
                              <FormDescription>
                                Receive email notifications for important events
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={notificationForm.control}
                        name="assessmentAlerts"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Assessment Alerts</FormLabel>
                              <FormDescription>
                                Get notified when new assessments are completed
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={notificationForm.control}
                        name="highRiskAlerts"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">High Risk Alerts</FormLabel>
                              <FormDescription>
                                Immediate alerts for high-risk patients (Groups C & D)
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Report Notifications</h3>
                      <FormField
                        control={notificationForm.control}
                        name="dailyReports"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Daily Reports</FormLabel>
                              <FormDescription>
                                Daily summary of assessments and activities
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={notificationForm.control}
                        name="weeklyReports"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Weekly Reports</FormLabel>
                              <FormDescription>
                                Weekly analytics and trend reports
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={notificationForm.control}
                        name="monthlyReports"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Monthly Reports</FormLabel>
                              <FormDescription>
                                Comprehensive monthly performance reports
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <Button type="submit" disabled={isLoading}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Notification Settings
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Security & Privacy
              </CardTitle>
              <CardDescription>
                Manage security settings and data privacy options
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Data Management</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline" className="justify-start">
                      <Database className="mr-2 h-4 w-4" />
                      Export Patient Data
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <Shield className="mr-2 h-4 w-4" />
                      Data Retention Settings
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">System Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="p-4 border rounded-lg">
                      <p className="font-medium">Dashboard Version</p>
                      <p className="text-gray-600">v1.0.0</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <p className="font-medium">Last Backup</p>
                      <p className="text-gray-600">2024-01-15 14:30:00</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <p className="font-medium">Database Status</p>
                      <p className="text-green-600">Connected</p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <p className="font-medium">Email Service</p>
                      <p className="text-green-600">Active</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Support</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline" className="justify-start">
                      <Globe className="mr-2 h-4 w-4" />
                      Documentation
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <Mail className="mr-2 h-4 w-4" />
                      Contact Support
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
