'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Activity, TrendingUp, TrendingDown, Minus, Calendar, Clock } from 'lucide-react';
import Link from 'next/link';
import { cgmApi, type PatientCGMData, type GlucoseReading } from '@/lib/api';
import { toast } from 'sonner';
import { GlucoseChart } from '@/components/cgm/glucose-chart';
import { GlucoseStatsChart, GlucoseTrendChart } from '@/components/cgm/glucose-stats-chart';

const getTrendIcon = (trend?: string) => {
  switch (trend) {
    case 'rising':
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    case 'falling':
      return <TrendingDown className="h-4 w-4 text-blue-500" />;
    default:
      return <Minus className="h-4 w-4 text-gray-500" />;
  }
};

const getGlucoseColor = (value: number, targetMin: number, targetMax: number) => {
  if (value < targetMin) {
    return 'text-blue-600 bg-blue-100';
  } else if (value > targetMax) {
    return 'text-red-600 bg-red-100';
  } else {
    return 'text-green-600 bg-green-100';
  }
};

export default function PatientCGMDetailPage() {
  const params = useParams();
  const patientId = params.patientId as string;

  const [cgmData, setCgmData] = useState<PatientCGMData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDays, setSelectedDays] = useState(7);

  useEffect(() => {
    const fetchPatientCGMData = async () => {
      try {
        setLoading(true);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - selectedDays);

        const response = await cgmApi.getPatientData(patientId, {
          startDate: startDate.toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
          limit: selectedDays * 96, // 96 readings per day (every 15 minutes)
        });

        if (response.success) {
          setCgmData(response.data);
        } else {
          toast.error('Failed to load patient CGM data');
        }
      } catch (error) {
        console.error('Error fetching patient CGM data:', error);
        toast.error('Failed to load patient CGM data');
      } finally {
        setLoading(false);
      }
    };

    if (patientId) {
      fetchPatientCGMData();
    }
  }, [patientId, selectedDays]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/cgm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to CGM Overview
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Patient CGM Data</h1>
            <p className="text-gray-600 mt-2">Loading glucose monitoring data...</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="h-24 bg-gray-200 rounded"></div>
            <div className="h-24 bg-gray-200 rounded"></div>
            <div className="h-24 bg-gray-200 rounded"></div>
            <div className="h-24 bg-gray-200 rounded"></div>
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!cgmData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/cgm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to CGM Overview
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Patient Not Found</h1>
            <p className="text-gray-600 mt-2">The requested patient could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  const { patient, cgmData: data } = cgmData;
  const { statistics, readings } = data;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/cgm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to CGM Overview
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {patient.firstName} {patient.lastName}
            </h1>
            <p className="text-gray-600 mt-2">
              Continuous glucose monitoring data • {patient.email}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <Select value={selectedDays.toString()} onValueChange={(value) => setSelectedDays(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1 Day</SelectItem>
              <SelectItem value="3">3 Days</SelectItem>
              <SelectItem value="7">7 Days</SelectItem>
              <SelectItem value="14">14 Days</SelectItem>
              <SelectItem value="30">30 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Glucose</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.averageGlucose} mg/dL</div>
            <p className="text-xs text-muted-foreground">
              Target: {statistics.targetRange.min}-{statistics.targetRange.max} mg/dL
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time in Range</CardTitle>
            <div className={`h-4 w-4 rounded-full ${statistics.timeInRange >= 70 ? 'bg-green-500' : statistics.timeInRange >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.timeInRange}%</div>
            <p className="text-xs text-muted-foreground">
              {statistics.targetRange.min}-{statistics.targetRange.max} mg/dL
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Above Range</CardTitle>
            <TrendingUp className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{statistics.timeAboveRange}%</div>
            <p className="text-xs text-muted-foreground">
              Above {statistics.targetRange.max} mg/dL
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Below Range</CardTitle>
            <TrendingDown className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{statistics.timeBelowRange}%</div>
            <p className="text-xs text-muted-foreground">
              Below {statistics.targetRange.min} mg/dL
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Glucose Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Glucose Trend Chart</CardTitle>
          <CardDescription>
            Continuous glucose monitoring over the last {selectedDays} days
          </CardDescription>
        </CardHeader>
        <CardContent>
          <GlucoseChart
            readings={readings}
            statistics={statistics}
            height={400}
            showTargetRange={true}
          />
        </CardContent>
      </Card>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Time in Range Analysis</CardTitle>
            <CardDescription>
              Glucose control distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <GlucoseStatsChart
                statistics={statistics}
                type="doughnut"
                height={300}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance vs Targets</CardTitle>
            <CardDescription>
              Current glucose control vs clinical targets
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GlucoseTrendChart
              statistics={statistics}
              height={300}
            />
          </CardContent>
        </Card>
      </div>

      {/* Recent Readings */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Glucose Readings</CardTitle>
          <CardDescription>
            Latest {Math.min(20, readings.length)} glucose readings from the last {selectedDays} days
          </CardDescription>
        </CardHeader>
        <CardContent>
          {readings.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No CGM Data Available</h3>
              <p className="text-gray-500">
                No glucose readings found for this patient in the selected time period.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {readings.slice(0, 20).map((reading) => (
                <div key={reading.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${getGlucoseColor(reading.value, statistics.targetRange.min, statistics.targetRange.max)}`}>
                      {reading.value} mg/dL
                    </div>
                    {getTrendIcon(reading.trend)}
                    {reading.trend && reading.trendRate && (
                      <span className="text-sm text-gray-500">
                        {reading.trendRate > 0 ? '+' : ''}{reading.trendRate.toFixed(1)} mg/dL/min
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(reading.timestamp).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{new Date(reading.timestamp).toLocaleTimeString()}</span>
                    </div>
                    {reading.source && (
                      <Badge variant="outline">{reading.source}</Badge>
                    )}
                  </div>
                </div>
              ))}

              {readings.length > 20 && (
                <div className="text-center pt-4">
                  <p className="text-sm text-gray-500">
                    Showing 20 of {readings.length} total readings
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Info */}
      <Card>
        <CardHeader>
          <CardTitle>Data Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Total Readings:</span>
              <span className="ml-2">{statistics.totalReadings}</span>
            </div>
            <div>
              <span className="font-medium">Date Range:</span>
              <span className="ml-2">
                {new Date(data.dateRange.startDate).toLocaleDateString()} - {new Date(data.dateRange.endDate).toLocaleDateString()}
              </span>
            </div>
            <div>
              <span className="font-medium">Data Source:</span>
              <span className="ml-2">{readings[0]?.source || 'Unknown'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
