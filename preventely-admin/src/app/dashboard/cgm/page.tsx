'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Activity, TrendingUp, TrendingDown, Minus, AlertTriangle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { cgmApi, type CGMOverview } from '@/lib/api';
import { toast } from 'sonner';
import { GlucoseStatsChart } from '@/components/cgm/glucose-stats-chart';

const getTrendIcon = (trend?: string) => {
  switch (trend) {
    case 'rising':
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    case 'falling':
      return <TrendingDown className="h-4 w-4 text-blue-500" />;
    default:
      return <Minus className="h-4 w-4 text-gray-500" />;
  }
};

const getGlucoseStatus = (value: number, targetMin: number, targetMax: number) => {
  if (value < targetMin) {
    return { status: 'low', color: 'text-blue-600', bg: 'bg-blue-100' };
  } else if (value > targetMax) {
    return { status: 'high', color: 'text-red-600', bg: 'bg-red-100' };
  } else {
    return { status: 'normal', color: 'text-green-600', bg: 'bg-green-100' };
  }
};

const getRiskGroupColor = (group: string) => {
  switch (group) {
    case 'A': return 'default';
    case 'B': return 'secondary';
    case 'C': return 'destructive';
    case 'D': return 'destructive';
    default: return 'default';
  }
};

export default function CGMOverviewPage() {
  const [cgmData, setCgmData] = useState<CGMOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDays, setSelectedDays] = useState(7);

  useEffect(() => {
    const fetchCGMData = async () => {
      try {
        setLoading(true);
        const response = await cgmApi.getOverview(selectedDays);
        if (response.success) {
          setCgmData(response.data);
        } else {
          toast.error('Failed to load CGM data');
        }
      } catch (error) {
        console.error('Error fetching CGM data:', error);
        toast.error('Failed to load CGM data');
      } finally {
        setLoading(false);
      }
    };

    fetchCGMData();
  }, [selectedDays]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">CGM Overview</h1>
            <p className="text-gray-600 mt-2">Loading continuous glucose monitoring data...</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">CGM Overview</h1>
            <p className="text-gray-600 mt-2">
              Continuous glucose monitoring data for all patients
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <Select value={selectedDays.toString()} onValueChange={(value) => setSelectedDays(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1 Day</SelectItem>
              <SelectItem value="3">3 Days</SelectItem>
              <SelectItem value="7">7 Days</SelectItem>
              <SelectItem value="14">14 Days</SelectItem>
              <SelectItem value="30">30 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Patients with CGM</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cgmData?.summary.totalPatientsWithCGM || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              of {cgmData?.summary.totalPatients || 0} total patients
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Time in Range</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cgmData?.overview.length ?
                Math.round(cgmData.overview.reduce((sum, p) => sum + p.cgmSummary.timeInRange, 0) / cgmData.overview.length)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Target glucose range
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk with CGM</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {cgmData?.overview.filter(p => p.patient.riskGroup === 'C' || p.patient.riskGroup === 'D').length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Groups C & D patients
            </p>
          </CardContent>
        </Card>
      </div>

      {/* CGM Analytics Chart */}
      {cgmData?.overview.length ? (
        <Card>
          <CardHeader>
            <CardTitle>CGM Analytics Overview</CardTitle>
            <CardDescription>
              Average glucose control metrics across all patients with CGM data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GlucoseStatsChart
              statistics={{
                totalReadings: cgmData.overview.reduce((sum, p) => sum + p.cgmSummary.totalReadings, 0),
                averageGlucose: Math.round(
                  cgmData.overview.reduce((sum, p) => sum + p.cgmSummary.averageGlucose, 0) / cgmData.overview.length
                ),
                timeInRange: Math.round(
                  cgmData.overview.reduce((sum, p) => sum + p.cgmSummary.timeInRange, 0) / cgmData.overview.length
                ),
                timeAboveRange: Math.round(
                  cgmData.overview.reduce((sum, p) => sum + (100 - p.cgmSummary.timeInRange), 0) / cgmData.overview.length * 0.7
                ),
                timeBelowRange: Math.round(
                  cgmData.overview.reduce((sum, p) => sum + (100 - p.cgmSummary.timeInRange), 0) / cgmData.overview.length * 0.3
                ),
                targetRange: {
                  min: 70,
                  max: 180,
                },
              }}
              type="bar"
              height={300}
            />
          </CardContent>
        </Card>
      ) : null}

      {/* CGM Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Patient CGM Data</CardTitle>
          <CardDescription>
            Real-time glucose monitoring data for the last {selectedDays} days
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!cgmData?.overview.length ? (
            <div className="text-center py-8">
              <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No CGM Data Available</h3>
              <p className="text-gray-500">
                No patients have connected CGM devices or uploaded glucose data in the selected time period.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Patient</TableHead>
                  <TableHead>Risk Group</TableHead>
                  <TableHead>Latest Reading</TableHead>
                  <TableHead>Average Glucose</TableHead>
                  <TableHead>Time in Range</TableHead>
                  <TableHead>Total Readings</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cgmData.overview.map((patientData) => {
                  const { patient, cgmSummary } = patientData;
                  const glucoseStatus = getGlucoseStatus(
                    cgmSummary.latestReading.value,
                    cgmSummary.targetRange.min,
                    cgmSummary.targetRange.max
                  );

                  return (
                    <TableRow key={patient.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{patient.firstName} {patient.lastName}</p>
                          <p className="text-sm text-gray-500">{patient.email}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {patient.riskGroup ? (
                          <Badge variant={getRiskGroupColor(patient.riskGroup) as any}>
                            Group {patient.riskGroup}
                          </Badge>
                        ) : (
                          <span className="text-gray-500">No assessment</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded text-sm font-medium ${glucoseStatus.bg} ${glucoseStatus.color}`}>
                            {cgmSummary.latestReading.value} mg/dL
                          </span>
                          {getTrendIcon(cgmSummary.latestReading.trend)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(cgmSummary.latestReading.timestamp).toLocaleString()}
                        </p>
                      </TableCell>
                      <TableCell>
                        <span className="font-mono text-lg">{cgmSummary.averageGlucose} mg/dL</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className={`font-medium ${cgmSummary.timeInRange >= 70 ? 'text-green-600' : cgmSummary.timeInRange >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                            {cgmSummary.timeInRange}%
                          </span>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${cgmSummary.timeInRange >= 70 ? 'bg-green-500' : cgmSummary.timeInRange >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                              style={{ width: `${Math.min(100, cgmSummary.timeInRange)}%` }}
                            ></div>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Target: {cgmSummary.targetRange.min}-{cgmSummary.targetRange.max} mg/dL
                        </p>
                      </TableCell>
                      <TableCell>
                        <span className="font-mono">{cgmSummary.totalReadings}</span>
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/cgm/${patient.id}`}>
                            View Details
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
