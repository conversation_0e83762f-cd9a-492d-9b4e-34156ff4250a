'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  ArrowLeft, 
  Download, 
  Calendar, 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText,
  Filter,
  Mail,
  Activity
} from 'lucide-react';
import Link from 'next/link';

// Mock data for reports
const reportData = {
  summary: {
    totalAssessments: 342,
    thisMonth: 89,
    lastMonth: 67,
    averageScore: 8.2,
    highRiskPatients: 99,
    invitationsSent: 298,
    accountsCreated: 156
  },
  monthlyTrends: [
    { month: 'Jan 2024', assessments: 45, avgScore: 7.8, highRisk: 12 },
    { month: 'Feb 2024', assessments: 52, avgScore: 8.1, highRisk: 15 },
    { month: 'Mar 2024', assessments: 67, avgScore: 8.0, highRisk: 18 },
    { month: 'Apr 2024', assessments: 89, avgScore: 8.2, highRisk: 21 },
  ],
  riskDistribution: [
    { group: 'A', count: 145, percentage: 42.4 },
    { group: 'B', count: 98, percentage: 28.7 },
    { group: 'C', count: 67, percentage: 19.6 },
    { group: 'D', count: 32, percentage: 9.4 }
  ],
  recentAssessments: [
    { id: 1, patient: 'John Doe', score: 15, group: 'C', date: '2024-01-15', invited: true },
    { id: 2, patient: 'Jane Smith', score: 8, group: 'B', date: '2024-01-14', invited: true },
    { id: 3, patient: 'Mike Johnson', score: 4, group: 'A', date: '2024-01-13', invited: true },
    { id: 4, patient: 'Sarah Wilson', score: 18, group: 'D', date: '2024-01-12', invited: false },
    { id: 5, patient: 'David Brown', score: 11, group: 'B', date: '2024-01-11', invited: true },
  ]
};

export default function ReportsPage() {
  const [dateRange, setDateRange] = useState('last30days');
  const [reportType, setReportType] = useState('summary');

  const exportReport = (format: string) => {
    // Here you would implement actual export functionality
    console.log(`Exporting ${reportType} report as ${format}`);
    // For now, just show a success message
    alert(`${reportType} report exported as ${format.toUpperCase()}`);
  };

  const getRiskGroupColor = (group: string) => {
    switch (group) {
      case 'A': return 'default';
      case 'B': return 'secondary';
      case 'C': return 'destructive';
      case 'D': return 'destructive';
      default: return 'default';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-2">
              Comprehensive analysis of patient assessments and risk distributions
            </p>
          </div>
        </div>
      </div>

      {/* Report Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dateRange">Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last7days">Last 7 days</SelectItem>
                  <SelectItem value="last30days">Last 30 days</SelectItem>
                  <SelectItem value="last3months">Last 3 months</SelectItem>
                  <SelectItem value="last6months">Last 6 months</SelectItem>
                  <SelectItem value="lastyear">Last year</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="reportType">Report Type</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary Report</SelectItem>
                  <SelectItem value="detailed">Detailed Assessment Report</SelectItem>
                  <SelectItem value="risk-analysis">Risk Analysis Report</SelectItem>
                  <SelectItem value="patient-list">Patient List Report</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Export Options</Label>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => exportReport('pdf')}>
                  <FileText className="mr-2 h-4 w-4" />
                  PDF
                </Button>
                <Button variant="outline" size="sm" onClick={() => exportReport('excel')}>
                  <Download className="mr-2 h-4 w-4" />
                  Excel
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quick Actions</Label>
              <Button className="w-full" size="sm">
                <BarChart3 className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="risk-analysis">Risk Analysis</TabsTrigger>
          <TabsTrigger value="patient-data">Patient Data</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportData.summary.totalAssessments}</div>
                <p className="text-xs text-muted-foreground">
                  +{reportData.summary.thisMonth - reportData.summary.lastMonth} from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Risk Score</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportData.summary.averageScore}</div>
                <p className="text-xs text-muted-foreground">
                  points average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Risk Patients</CardTitle>
                <TrendingUp className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{reportData.summary.highRiskPatients}</div>
                <p className="text-xs text-muted-foreground">
                  Groups C & D
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">App Invitations</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportData.summary.invitationsSent}</div>
                <p className="text-xs text-muted-foreground">
                  {reportData.summary.accountsCreated} accounts created
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Risk Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Risk Group Distribution</CardTitle>
              <CardDescription>
                Current distribution of patients across risk groups
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {reportData.riskDistribution.map((group) => (
                  <div key={group.group} className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold mb-2">
                      <Badge variant={getRiskGroupColor(group.group) as any} className="text-lg px-3 py-1">
                        Group {group.group}
                      </Badge>
                    </div>
                    <div className="text-3xl font-bold mb-1">{group.count}</div>
                    <div className="text-sm text-gray-600">{group.percentage}% of patients</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Assessment Trends</CardTitle>
              <CardDescription>
                Assessment volume and risk score trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.monthlyTrends.map((month, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">{month.month}</p>
                        <p className="text-sm text-gray-600">{month.assessments} assessments</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Avg Score: {month.avgScore}</p>
                      <p className="text-sm text-red-600">{month.highRisk} high risk</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk-analysis" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Risk Score Distribution</CardTitle>
                <CardDescription>
                  Breakdown of risk scores across all assessments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>0-6 points (Low Risk)</span>
                    <Badge variant="default">145 patients</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>7-11 points (Slightly Elevated)</span>
                    <Badge variant="secondary">98 patients</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>12-14 points (Moderate Risk)</span>
                    <Badge variant="destructive">67 patients</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>15+ points (High Risk)</span>
                    <Badge variant="destructive">32 patients</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Intervention Recommendations</CardTitle>
                <CardDescription>
                  Suggested actions based on current risk distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="font-medium text-red-800">High Priority</p>
                    <p className="text-sm text-red-600">32 patients need immediate intervention (Group D)</p>
                  </div>
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p className="font-medium text-orange-800">Medium Priority</p>
                    <p className="text-sm text-orange-600">67 patients need lifestyle counseling (Group C)</p>
                  </div>
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="font-medium text-green-800">Maintenance</p>
                    <p className="text-sm text-green-600">243 patients for prevention programs (Groups A & B)</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="patient-data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Assessments</CardTitle>
              <CardDescription>
                Latest patient assessments and their details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Risk Score</TableHead>
                    <TableHead>Risk Group</TableHead>
                    <TableHead>Assessment Date</TableHead>
                    <TableHead>Invitation Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportData.recentAssessments.map((assessment) => (
                    <TableRow key={assessment.id}>
                      <TableCell className="font-medium">{assessment.patient}</TableCell>
                      <TableCell>
                        <span className="font-mono text-lg">{assessment.score}</span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRiskGroupColor(assessment.group) as any}>
                          Group {assessment.group}
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(assessment.date).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant={assessment.invited ? 'default' : 'secondary'}>
                          {assessment.invited ? 'Sent' : 'Pending'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
