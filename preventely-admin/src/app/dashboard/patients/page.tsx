'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ArrowLeft, Search, Mail, Eye, Filter, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import { patientApi, invitationApi, type Patient } from '@/lib/api';
import { toast } from 'sonner';



const getRiskGroupColor = (group: string) => {
  switch (group) {
    case 'A': return 'default';
    case 'B': return 'secondary';
    case 'C': return 'destructive';
    case 'D': return 'destructive';
    default: return 'default';
  }
};

const getRiskGroupLabel = (group: string) => {
  switch (group) {
    case 'A': return 'Low Risk';
    case 'B': return 'Slightly Elevated';
    case 'C': return 'Moderate Risk';
    case 'D': return 'High Risk';
    default: return 'Unknown';
  }
};

export default function PatientsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [filterGroup, setFilterGroup] = useState('all');
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        const response = await patientApi.getAll();
        if (response.success) {
          setPatients(response.data.patients || []);
        }
      } catch (error) {
        console.error('Error fetching patients:', error);
        toast.error('Failed to load patients');
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  const filteredPatients = patients.filter(patient => {
    const matchesSearch =
      patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchTerm.toLowerCase());

    const latestAssessment = patient.assessments?.[0];
    const matchesFilter = filterGroup === 'all' || latestAssessment?.riskGroup === filterGroup;

    return matchesSearch && matchesFilter;
  });

  const sendInvitation = async (patient: Patient) => {
    try {
      const latestAssessment = patient.assessments?.[0];
      if (!latestAssessment) {
        toast.error('No assessment found for this patient');
        return;
      }

      const response = await invitationApi.send({
        patientId: patient.id,
        email: patient.email,
        firstName: patient.firstName,
        lastName: patient.lastName,
        riskGroup: latestAssessment.riskGroup,
        riskScore: latestAssessment.riskScore,
      });

      if (response.success) {
        toast.success(`Invitation sent to ${patient.email}!`);
        // Refresh patients data
        const refreshResponse = await patientApi.getAll();
        if (refreshResponse.success) {
          setPatients(refreshResponse.data.patients || []);
        }
      }
    } catch (error) {
      console.error('Failed to send invitation:', error);
      toast.error('Failed to send invitation');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Patient Management</h1>
            <p className="text-gray-600 mt-2">
              View and manage all assessed patients
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant={filterGroup === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('all')}
              >
                All
              </Button>
              <Button
                variant={filterGroup === 'A' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('A')}
              >
                Group A
              </Button>
              <Button
                variant={filterGroup === 'B' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('B')}
              >
                Group B
              </Button>
              <Button
                variant={filterGroup === 'C' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('C')}
              >
                Group C
              </Button>
              <Button
                variant={filterGroup === 'D' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('D')}
              >
                Group D
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Patients Table */}
      <Card>
        <CardHeader>
          <CardTitle>Patients ({filteredPatients.length})</CardTitle>
          <CardDescription>
            All patients who have completed the diabetes risk assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Patient</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Risk Score</TableHead>
                <TableHead>Risk Group</TableHead>
                <TableHead>Assessment Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="animate-pulse">Loading patients...</div>
                  </TableCell>
                </TableRow>
              ) : filteredPatients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No patients found
                  </TableCell>
                </TableRow>
              ) : (
                filteredPatients.map((patient) => {
                  const latestAssessment = patient.assessments?.[0];
                  const latestInvitation = patient.invitations?.[0];

                  return (
                    <TableRow key={patient.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{patient.firstName} {patient.lastName}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>{patient.email}</p>
                          <p className="text-gray-500">{patient.phone || 'No phone'}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-mono text-lg">
                          {latestAssessment?.riskScore || 'N/A'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {latestAssessment ? (
                          <Badge variant={getRiskGroupColor(latestAssessment.riskGroup) as any}>
                            Group {latestAssessment.riskGroup} - {getRiskGroupLabel(latestAssessment.riskGroup)}
                          </Badge>
                        ) : (
                          <span className="text-gray-500">No assessment</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {latestAssessment ?
                          new Date(latestAssessment.assessmentDate).toLocaleDateString() :
                          'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${latestInvitation?.invitationSent ? 'bg-green-500' : 'bg-red-500'}`} />
                            <span className="text-sm">{latestInvitation?.invitationSent ? 'Invited' : 'Not Invited'}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${latestInvitation?.accountCreated ? 'bg-green-500' : 'bg-gray-300'}`} />
                            <span className="text-sm">{latestInvitation?.accountCreated ? 'Account Created' : 'No Account'}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm" onClick={() => setSelectedPatient(patient)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Patient Details</DialogTitle>
                                <DialogDescription>
                                  Complete assessment information for {patient.firstName} {patient.lastName}
                                </DialogDescription>
                              </DialogHeader>
                              {selectedPatient && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">Name</label>
                                      <p>{selectedPatient.firstName} {selectedPatient.lastName}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">Email</label>
                                      <p>{selectedPatient.email}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">Phone</label>
                                      <p>{selectedPatient.phone || 'Not provided'}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">Address</label>
                                      <p>{selectedPatient.address || 'Not provided'}</p>
                                    </div>
                                    {selectedPatient.assessments?.[0] && (
                                      <>
                                        <div>
                                          <label className="text-sm font-medium">Assessment Date</label>
                                          <p>{new Date(selectedPatient.assessments[0].assessmentDate).toLocaleDateString()}</p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium">Risk Score</label>
                                          <p className="font-mono text-lg">{selectedPatient.assessments[0].riskScore} points</p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium">Risk Group</label>
                                          <Badge variant={getRiskGroupColor(selectedPatient.assessments[0].riskGroup) as any}>
                                            Group {selectedPatient.assessments[0].riskGroup} - {getRiskGroupLabel(selectedPatient.assessments[0].riskGroup)}
                                          </Badge>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium">Assessed By</label>
                                          <p>{selectedPatient.assessments[0].assessedBy || 'Unknown'}</p>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                  {selectedPatient.assessments?.[0]?.notes && (
                                    <div>
                                      <label className="text-sm font-medium">Notes</label>
                                      <p className="text-sm text-gray-600 mt-1">{selectedPatient.assessments[0].notes}</p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>

                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/cgm/${patient.id}`}>
                              <TrendingUp className="h-4 w-4" />
                            </Link>
                          </Button>

                          {!latestInvitation?.invitationSent && latestAssessment && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => sendInvitation(patient)}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
