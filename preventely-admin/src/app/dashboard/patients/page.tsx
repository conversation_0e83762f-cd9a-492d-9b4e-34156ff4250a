'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ArrowLeft, Search, Mail, Eye, Filter } from 'lucide-react';
import Link from 'next/link';

// Mock data - in real app, this would come from your database
const mockPatients = [
  {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    riskScore: 15,
    riskGroup: 'C',
    assessmentDate: '2024-01-15',
    invitationSent: true,
    accountCreated: false,
    notes: 'Patient showed interest in lifestyle changes'
  },
  {
    id: 2,
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    riskScore: 8,
    riskGroup: 'B',
    assessmentDate: '2024-01-14',
    invitationSent: true,
    accountCreated: true,
    notes: 'Very motivated patient'
  },
  {
    id: 3,
    firstName: 'Mike',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+**********',
    riskScore: 4,
    riskGroup: 'A',
    assessmentDate: '2024-01-13',
    invitationSent: true,
    accountCreated: true,
    notes: 'Excellent health awareness'
  },
  {
    id: 4,
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+**********',
    riskScore: 18,
    riskGroup: 'D',
    assessmentDate: '2024-01-12',
    invitationSent: false,
    accountCreated: false,
    notes: 'Requires immediate intervention'
  },
  {
    id: 5,
    firstName: 'David',
    lastName: 'Brown',
    email: '<EMAIL>',
    phone: '+**********',
    riskScore: 11,
    riskGroup: 'B',
    assessmentDate: '2024-01-11',
    invitationSent: true,
    accountCreated: false,
    notes: 'Family history of diabetes'
  }
];

const getRiskGroupColor = (group: string) => {
  switch (group) {
    case 'A': return 'default';
    case 'B': return 'secondary';
    case 'C': return 'destructive';
    case 'D': return 'destructive';
    default: return 'default';
  }
};

const getRiskGroupLabel = (group: string) => {
  switch (group) {
    case 'A': return 'Low Risk';
    case 'B': return 'Slightly Elevated';
    case 'C': return 'Moderate Risk';
    case 'D': return 'High Risk';
    default: return 'Unknown';
  }
};

export default function PatientsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [filterGroup, setFilterGroup] = useState('all');

  const filteredPatients = mockPatients.filter(patient => {
    const matchesSearch = 
      patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterGroup === 'all' || patient.riskGroup === filterGroup;
    
    return matchesSearch && matchesFilter;
  });

  const sendInvitation = async (patient: any) => {
    try {
      // Here you would call your API to send invitation
      console.log('Sending invitation to:', patient.email);
      // Update patient status in your database
    } catch (error) {
      console.error('Failed to send invitation:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Patient Management</h1>
            <p className="text-gray-600 mt-2">
              View and manage all assessed patients
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant={filterGroup === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('all')}
              >
                All
              </Button>
              <Button
                variant={filterGroup === 'A' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('A')}
              >
                Group A
              </Button>
              <Button
                variant={filterGroup === 'B' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('B')}
              >
                Group B
              </Button>
              <Button
                variant={filterGroup === 'C' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('C')}
              >
                Group C
              </Button>
              <Button
                variant={filterGroup === 'D' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterGroup('D')}
              >
                Group D
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Patients Table */}
      <Card>
        <CardHeader>
          <CardTitle>Patients ({filteredPatients.length})</CardTitle>
          <CardDescription>
            All patients who have completed the diabetes risk assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Patient</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Risk Score</TableHead>
                <TableHead>Risk Group</TableHead>
                <TableHead>Assessment Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPatients.map((patient) => (
                <TableRow key={patient.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{patient.firstName} {patient.lastName}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{patient.email}</p>
                      <p className="text-gray-500">{patient.phone}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-mono text-lg">{patient.riskScore}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRiskGroupColor(patient.riskGroup) as any}>
                      Group {patient.riskGroup} - {getRiskGroupLabel(patient.riskGroup)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(patient.assessmentDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${patient.invitationSent ? 'bg-green-500' : 'bg-red-500'}`} />
                        <span className="text-sm">{patient.invitationSent ? 'Invited' : 'Not Invited'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${patient.accountCreated ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span className="text-sm">{patient.accountCreated ? 'Account Created' : 'No Account'}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedPatient(patient)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Patient Details</DialogTitle>
                            <DialogDescription>
                              Complete assessment information for {patient.firstName} {patient.lastName}
                            </DialogDescription>
                          </DialogHeader>
                          {selectedPatient && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Name</label>
                                  <p>{selectedPatient.firstName} {selectedPatient.lastName}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Email</label>
                                  <p>{selectedPatient.email}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Phone</label>
                                  <p>{selectedPatient.phone}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Assessment Date</label>
                                  <p>{new Date(selectedPatient.assessmentDate).toLocaleDateString()}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Risk Score</label>
                                  <p className="font-mono text-lg">{selectedPatient.riskScore} points</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Risk Group</label>
                                  <Badge variant={getRiskGroupColor(selectedPatient.riskGroup) as any}>
                                    Group {selectedPatient.riskGroup} - {getRiskGroupLabel(selectedPatient.riskGroup)}
                                  </Badge>
                                </div>
                              </div>
                              {selectedPatient.notes && (
                                <div>
                                  <label className="text-sm font-medium">Notes</label>
                                  <p className="text-sm text-gray-600 mt-1">{selectedPatient.notes}</p>
                                </div>
                              )}
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                      
                      {!patient.invitationSent && (
                        <Button 
                          variant="default" 
                          size="sm"
                          onClick={() => sendInvitation(patient)}
                        >
                          <Mail className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
