'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { ArrowLeft, Calculator, Mail, User } from 'lucide-react';
import Link from 'next/link';
import { assessmentApi, invitationApi } from '@/lib/api';

// Finnish Diabetes Risk Score Schema
const assessmentSchema = z.object({
  // Patient Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),

  // Finnish Diabetes Risk Score Questions
  age: z.string(),
  bmi: z.string(),
  waistCircumference: z.string(),
  physicalActivity: z.string(),
  vegetableConsumption: z.string(),
  bloodPressureMedication: z.string(),
  highGlucoseHistory: z.string(),
  familyDiabetesHistory: z.string(),

  // Additional notes
  notes: z.string().optional(),
});

type AssessmentForm = z.infer<typeof assessmentSchema>;

// Finnish Diabetes Risk Score calculation
const calculateRiskScore = (data: AssessmentForm) => {
  let score = 0;

  // Age scoring
  if (data.age === '45-54') score += 2;
  else if (data.age === '55-64') score += 3;
  else if (data.age === '65+') score += 4;

  // BMI scoring
  if (data.bmi === '25-30') score += 1;
  else if (data.bmi === '30+') score += 3;

  // Waist circumference scoring (gender-specific)
  if (data.waistCircumference === 'male-94-102' || data.waistCircumference === 'female-80-88') score += 3;
  else if (data.waistCircumference === 'male-102+' || data.waistCircumference === 'female-88+') score += 4;

  // Physical activity
  if (data.physicalActivity === 'no') score += 2;

  // Vegetable consumption
  if (data.vegetableConsumption === 'no') score += 1;

  // Blood pressure medication
  if (data.bloodPressureMedication === 'yes') score += 2;

  // High glucose history
  if (data.highGlucoseHistory === 'yes') score += 5;

  // Family diabetes history
  if (data.familyDiabetesHistory === 'yes-grandparent-aunt-uncle') score += 3;
  else if (data.familyDiabetesHistory === 'yes-parent-sibling-child') score += 5;

  return score;
};

// Risk group assignment
const getRiskGroup = (score: number) => {
  if (score < 7) return { group: 'A', risk: 'Low Risk', color: 'default' };
  if (score < 12) return { group: 'B', risk: 'Slightly Elevated Risk', color: 'secondary' };
  if (score < 15) return { group: 'C', risk: 'Moderate Risk', color: 'destructive' };
  return { group: 'D', risk: 'High Risk', color: 'destructive' };
};

export default function AssessmentPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<{ score: number; riskGroup: any } | null>(null);

  const form = useForm<AssessmentForm>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      age: '',
      bmi: '',
      waistCircumference: '',
      physicalActivity: '',
      vegetableConsumption: '',
      bloodPressureMedication: '',
      highGlucoseHistory: '',
      familyDiabetesHistory: '',
      notes: '',
    },
  });

  const onSubmit = async (data: AssessmentForm) => {
    setIsSubmitting(true);

    try {
      // Submit assessment to backend
      const response = await assessmentApi.create({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        age: data.age,
        bmi: data.bmi,
        waistCircumference: data.waistCircumference,
        physicalActivity: data.physicalActivity,
        vegetableConsumption: data.vegetableConsumption,
        bloodPressureMedication: data.bloodPressureMedication,
        highGlucoseHistory: data.highGlucoseHistory,
        familyDiabetesHistory: data.familyDiabetesHistory,
        notes: data.notes,
        assessedBy: 'Dr. Admin', // You can make this dynamic
      });

      if (response.success) {
        const { riskScore, riskGroup } = response.data;
        const riskGroupInfo = getRiskGroup(riskScore);

        setResult({
          score: riskScore,
          riskGroup: riskGroupInfo,
          patientId: response.data.assessment.patientId,
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          apiRiskGroup: riskGroup
        });

        toast.success('Assessment completed and saved successfully!');
      } else {
        throw new Error('Failed to save assessment');
      }
    } catch (error) {
      console.error('Assessment error:', error);
      toast.error('Failed to complete assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const sendInvitation = async () => {
    if (!result) return;

    try {
      const response = await invitationApi.send({
        patientId: (result as any).patientId,
        email: (result as any).email,
        firstName: (result as any).firstName,
        lastName: (result as any).lastName,
        riskGroup: (result as any).apiRiskGroup,
        riskScore: result.score,
      });

      if (response.success) {
        toast.success(`Invitation sent to ${(result as any).email}!`);
      } else {
        throw new Error('Failed to send invitation');
      }
    } catch (error) {
      console.error('Invitation error:', error);
      toast.error('Failed to send invitation. Please try again.');
    }
  };

  if (result) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              Assessment Results
            </CardTitle>
            <CardDescription>
              Finnish Diabetes Risk Score calculation completed
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <div>
                <h3 className="text-2xl font-bold">Risk Score: {result.score}</h3>
                <Badge variant={result.riskGroup.color as any} className="mt-2">
                  Group {result.riskGroup.group} - {result.riskGroup.risk}
                </Badge>
              </div>

              <Alert>
                <AlertDescription>
                  {result.riskGroup.group === 'A' &&
                    'Low risk of developing type 2 diabetes. Continue healthy lifestyle habits.'
                  }
                  {result.riskGroup.group === 'B' &&
                    'Slightly elevated risk. Consider lifestyle modifications and regular monitoring.'
                  }
                  {result.riskGroup.group === 'C' &&
                    'Moderate risk. Lifestyle intervention recommended. Consider glucose tolerance test.'
                  }
                  {result.riskGroup.group === 'D' &&
                    'High risk. Immediate lifestyle intervention and glucose tolerance test recommended.'
                  }
                </AlertDescription>
              </Alert>
            </div>

            <div className="flex space-x-4 justify-center">
              <Button onClick={sendInvitation} className="flex items-center">
                <Mail className="mr-2 h-4 w-4" />
                Send App Invitation
              </Button>
              <Button variant="outline" onClick={() => setResult(null)}>
                New Assessment
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="mr-2 h-5 w-5" />
            New Patient Assessment
          </CardTitle>
          <CardDescription>
            Finnish Diabetes Risk Score Assessment - Complete the questionnaire to determine prediabetes risk
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Patient Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Patient Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter first name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter last name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter email address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Finnish Diabetes Risk Score Questions */}
              <div className="space-y-6">
                <h3 className="text-lg font-semibold">Risk Assessment Questions</h3>

                {/* Age */}
                <FormField
                  control={form.control}
                  name="age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>1. Age</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="under-45" id="age-under-45" />
                            <label htmlFor="age-under-45">Under 45 years (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="45-54" id="age-45-54" />
                            <label htmlFor="age-45-54">45-54 years (2 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="55-64" id="age-55-64" />
                            <label htmlFor="age-55-64">55-64 years (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="65+" id="age-65-plus" />
                            <label htmlFor="age-65-plus">Over 64 years (4 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* BMI */}
                <FormField
                  control={form.control}
                  name="bmi"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>2. Body Mass Index (BMI)</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="under-25" id="bmi-under-25" />
                            <label htmlFor="bmi-under-25">Lower than 25 kg/m² (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="25-30" id="bmi-25-30" />
                            <label htmlFor="bmi-25-30">25-30 kg/m² (1 point)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="30+" id="bmi-30-plus" />
                            <label htmlFor="bmi-30-plus">Higher than 30 kg/m² (3 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Waist Circumference */}
                <FormField
                  control={form.control}
                  name="waistCircumference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>3. Waist Circumference</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-under-94" id="waist-male-under-94" />
                            <label htmlFor="waist-male-under-94">Men: Less than 94 cm (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-under-80" id="waist-female-under-80" />
                            <label htmlFor="waist-female-under-80">Women: Less than 80 cm (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-94-102" id="waist-male-94-102" />
                            <label htmlFor="waist-male-94-102">Men: 94-102 cm (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-80-88" id="waist-female-80-88" />
                            <label htmlFor="waist-female-80-88">Women: 80-88 cm (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="male-102+" id="waist-male-102-plus" />
                            <label htmlFor="waist-male-102-plus">Men: More than 102 cm (4 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="female-88+" id="waist-female-88-plus" />
                            <label htmlFor="waist-female-88-plus">Women: More than 88 cm (4 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Physical Activity */}
                <FormField
                  control={form.control}
                  name="physicalActivity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>4. Do you usually have at least 30 minutes of physical activity at work and/or during leisure time?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="activity-yes" />
                            <label htmlFor="activity-yes">Yes (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="activity-no" />
                            <label htmlFor="activity-no">No (2 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Vegetable Consumption */}
                <FormField
                  control={form.control}
                  name="vegetableConsumption"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>5. How often do you eat vegetables, fruit or berries?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="vegetables-yes" />
                            <label htmlFor="vegetables-yes">Every day (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="vegetables-no" />
                            <label htmlFor="vegetables-no">Not every day (1 point)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Blood Pressure Medication */}
                <FormField
                  control={form.control}
                  name="bloodPressureMedication"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>6. Have you ever taken medication for high blood pressure?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="bp-med-no" />
                            <label htmlFor="bp-med-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="bp-med-yes" />
                            <label htmlFor="bp-med-yes">Yes (2 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* High Glucose History */}
                <FormField
                  control={form.control}
                  name="highGlucoseHistory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>7. Have you ever been found to have high blood glucose?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="glucose-no" />
                            <label htmlFor="glucose-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes" id="glucose-yes" />
                            <label htmlFor="glucose-yes">Yes (5 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Family Diabetes History */}
                <FormField
                  control={form.control}
                  name="familyDiabetesHistory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>8. Have any of your family members been diagnosed with diabetes?</FormLabel>
                      <FormControl>
                        <RadioGroup onValueChange={field.onChange} value={field.value}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="no" id="family-diabetes-no" />
                            <label htmlFor="family-diabetes-no">No (0 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes-grandparent-aunt-uncle" id="family-diabetes-distant" />
                            <label htmlFor="family-diabetes-distant">Yes: grandparent, aunt, uncle, or first cousin (3 points)</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="yes-parent-sibling-child" id="family-diabetes-close" />
                            <label htmlFor="family-diabetes-close">Yes: parent, brother, sister, or own child (5 points)</label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Additional Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional observations or notes about the patient..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end">
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? 'Calculating...' : 'Calculate Risk Score'}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
