'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Users, TrendingUp, AlertTriangle, Activity, Info } from 'lucide-react';
import Link from 'next/link';

// Mock data for risk groups
const riskGroupsData = {
  groupA: {
    name: 'Group A - Low Risk',
    description: 'Score: 0-6 points | Risk: 1% chance of developing diabetes',
    color: 'bg-green-500',
    badgeVariant: 'default',
    count: 145,
    percentage: 42.5,
    patients: [
      { name: '<PERSON>', score: 4, email: '<EMAIL>', date: '2024-01-13' },
      { name: '<PERSON>', score: 5, email: '<EMAIL>', date: '2024-01-12' },
      { name: '<PERSON>', score: 3, email: '<EMAIL>', date: '2024-01-11' },
    ]
  },
  groupB: {
    name: 'Group B - Slightly Elevated Risk',
    description: 'Score: 7-11 points | Risk: 4% chance of developing diabetes',
    color: 'bg-yellow-500',
    badgeVariant: 'secondary',
    count: 98,
    percentage: 28.7,
    patients: [
      { name: 'Jane Smith', score: 8, email: '<EMAIL>', date: '2024-01-14' },
      { name: 'David Brown', score: 11, email: '<EMAIL>', date: '2024-01-11' },
      { name: 'Emma Wilson', score: 9, email: '<EMAIL>', date: '2024-01-10' },
    ]
  },
  groupC: {
    name: 'Group C - Moderate Risk',
    description: 'Score: 12-14 points | Risk: 17% chance of developing diabetes',
    color: 'bg-orange-500',
    badgeVariant: 'destructive',
    count: 67,
    percentage: 19.6,
    patients: [
      { name: 'John Doe', score: 15, email: '<EMAIL>', date: '2024-01-15' },
      { name: 'Maria Garcia', score: 13, email: '<EMAIL>', date: '2024-01-09' },
      { name: 'James Miller', score: 14, email: '<EMAIL>', date: '2024-01-08' },
    ]
  },
  groupD: {
    name: 'Group D - High Risk',
    description: 'Score: 15+ points | Risk: 33% chance of developing diabetes',
    color: 'bg-red-600',
    badgeVariant: 'destructive',
    count: 32,
    percentage: 9.4,
    patients: [
      { name: 'Sarah Wilson', score: 18, email: '<EMAIL>', date: '2024-01-12' },
      { name: 'Thomas Anderson', score: 19, email: '<EMAIL>', date: '2024-01-07' },
      { name: 'Patricia Lee', score: 16, email: '<EMAIL>', date: '2024-01-06' },
    ]
  }
};

const totalPatients = Object.values(riskGroupsData).reduce((sum, group) => sum + group.count, 0);

export default function RiskGroupsPage() {
  const [selectedGroup, setSelectedGroup] = useState('overview');

  const RiskGroupCard = ({ groupKey, group }: { groupKey: string, group: any }) => (
    <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setSelectedGroup(groupKey)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-4 h-4 rounded-full ${group.color}`} />
            <CardTitle className="text-lg">{group.name}</CardTitle>
          </div>
          <Badge variant={group.badgeVariant as any}>
            {group.count} patients
          </Badge>
        </div>
        <CardDescription className="text-sm">
          {group.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>Percentage of total patients</span>
            <span className="font-medium">{group.percentage}%</span>
          </div>
          <Progress value={group.percentage} className="h-2" />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Recent assessments</span>
            <span>{group.patients.length} this week</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const GroupDetailView = ({ groupKey, group }: { groupKey: string, group: any }) => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`w-6 h-6 rounded-full ${group.color}`} />
          <div>
            <h2 className="text-2xl font-bold">{group.name}</h2>
            <p className="text-gray-600">{group.description}</p>
          </div>
        </div>
        <Button variant="outline" onClick={() => setSelectedGroup('overview')}>
          Back to Overview
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{group.count}</div>
            <p className="text-sm text-gray-600">{group.percentage}% of all patients</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {(group.patients.reduce((sum: number, p: any) => sum + p.score, 0) / group.patients.length).toFixed(1)}
            </div>
            <p className="text-sm text-gray-600">points</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {groupKey === 'groupA' && '1%'}
              {groupKey === 'groupB' && '4%'}
              {groupKey === 'groupC' && '17%'}
              {groupKey === 'groupD' && '33%'}
            </div>
            <p className="text-sm text-gray-600">chance of diabetes</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Patients in {group.name}</CardTitle>
          <CardDescription>Latest assessments for this risk group</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {group.patients.map((patient: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-3 rounded-full ${group.color}`} />
                  <div>
                    <p className="font-medium">{patient.name}</p>
                    <p className="text-sm text-gray-600">{patient.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-mono text-lg">{patient.score} pts</p>
                  <p className="text-sm text-gray-600">{new Date(patient.date).toLocaleDateString()}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Risk Groups Analysis</h1>
            <p className="text-gray-600 mt-2">
              Finnish Diabetes Risk Score distribution and patient management
            </p>
          </div>
        </div>
      </div>

      {selectedGroup === 'overview' ? (
        <>
          {/* Overview Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalPatients}</div>
                <p className="text-xs text-muted-foreground">
                  Assessed patients
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Risk Patients</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {riskGroupsData.groupC.count + riskGroupsData.groupD.count}
                </div>
                <p className="text-xs text-muted-foreground">
                  Groups C & D
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Low Risk Patients</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {riskGroupsData.groupA.count}
                </div>
                <p className="text-xs text-muted-foreground">
                  Group A
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Risk Score</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.2</div>
                <p className="text-xs text-muted-foreground">
                  points
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Risk Groups Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <RiskGroupCard groupKey="groupA" group={riskGroupsData.groupA} />
            <RiskGroupCard groupKey="groupB" group={riskGroupsData.groupB} />
            <RiskGroupCard groupKey="groupC" group={riskGroupsData.groupC} />
            <RiskGroupCard groupKey="groupD" group={riskGroupsData.groupD} />
          </div>

          {/* Risk Distribution Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Risk Distribution Overview</CardTitle>
              <CardDescription>
                Distribution of patients across different risk groups
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(riskGroupsData).map(([key, group]) => (
                  <div key={key} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${group.color}`} />
                        <span>{group.name}</span>
                      </div>
                      <span className="font-medium">{group.count} patients ({group.percentage}%)</span>
                    </div>
                    <Progress value={group.percentage} className="h-3" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        <GroupDetailView 
          groupKey={selectedGroup} 
          group={riskGroupsData[selectedGroup as keyof typeof riskGroupsData]} 
        />
      )}
    </div>
  );
}
