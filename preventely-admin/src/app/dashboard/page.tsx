'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, UserPlus, Activity, Mail, TrendingUp, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { reportsApi, assessmentApi, type SummaryReport, type Assessment } from '@/lib/api';

export default function DashboardPage() {
  const [summaryData, setSummaryData] = useState<SummaryReport | null>(null);
  const [recentAssessments, setRecentAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch summary report
        const summaryResponse = await reportsApi.getSummary();
        if (summaryResponse.success) {
          setSummaryData(summaryResponse.data);
        }

        // Fetch recent assessments
        const assessmentsResponse = await assessmentApi.getAll({ limit: 5 });
        if (assessmentsResponse.success) {
          setRecentAssessments(assessmentsResponse.data.assessments || []);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getRiskGroupBadgeVariant = (group: string) => {
    switch (group) {
      case 'A': return 'default';
      case 'B': return 'secondary';
      case 'C': return 'destructive';
      case 'D': return 'destructive';
      default: return 'default';
    }
  };
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Clinic Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Manage patient assessments and prediabetes risk evaluations
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : summaryData?.summary.totalAssessments || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Total patient assessments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Risk Patients</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {loading ? '...' : summaryData?.summary.highRiskPatients || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Groups C & D patients
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invitations Sent</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : summaryData?.summary.invitationsSent || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              App invitations sent
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accounts Created</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {loading ? '...' : summaryData?.summary.accountsCreated || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              App accounts created
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Start a new patient assessment or manage existing ones
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/dashboard/assessment">
              <Button className="w-full" size="lg">
                <UserPlus className="mr-2 h-4 w-4" />
                New Patient Assessment
              </Button>
            </Link>
            <Link href="/dashboard/patients">
              <Button variant="outline" className="w-full" size="lg">
                <Users className="mr-2 h-4 w-4" />
                View All Patients
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Assessments</CardTitle>
            <CardDescription>
              Latest patient risk assessments
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ) : recentAssessments.length > 0 ? (
              <div className="space-y-4">
                {recentAssessments.map((assessment) => (
                  <div key={assessment.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">
                        {assessment.patient?.firstName} {assessment.patient?.lastName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {assessment.patient?.email}
                      </p>
                      <p className="text-xs text-gray-500">
                        Score: {assessment.riskScore} points • {new Date(assessment.assessmentDate).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant={getRiskGroupBadgeVariant(assessment.riskGroup) as any}>
                      Group {assessment.riskGroup}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No recent assessments</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
