import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const { email, firstName, lastName, riskGroup, score } = await request.json();

    // Create transporter (you'll need to configure with your email service)
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Generate app download links (you'll need to update these with actual links)
    const appStoreLink = 'https://apps.apple.com/app/preventely';
    const playStoreLink = 'https://play.google.com/store/apps/details?id=com.preventely';
    const registrationLink = `${process.env.NEXT_PUBLIC_APP_URL}/register?email=${encodeURIComponent(email)}`;

    // Email content based on risk group
    const getRiskMessage = (group: string) => {
      switch (group) {
        case 'A':
          return {
            title: 'Low Risk Assessment Result',
            message: 'Your assessment shows a low risk for developing type 2 diabetes. The Preventely app will help you maintain your healthy lifestyle.',
            color: '#10B981'
          };
        case 'B':
          return {
            title: 'Slightly Elevated Risk Assessment Result',
            message: 'Your assessment shows a slightly elevated risk. The Preventely app will provide personalized recommendations to help reduce your risk.',
            color: '#F59E0B'
          };
        case 'C':
          return {
            title: 'Moderate Risk Assessment Result',
            message: 'Your assessment shows a moderate risk for developing type 2 diabetes. The Preventely app will provide comprehensive lifestyle interventions.',
            color: '#EF4444'
          };
        case 'D':
          return {
            title: 'High Risk Assessment Result',
            message: 'Your assessment shows a high risk for developing type 2 diabetes. The Preventely app will provide intensive support and monitoring.',
            color: '#DC2626'
          };
        default:
          return {
            title: 'Assessment Result',
            message: 'Your diabetes risk assessment has been completed.',
            color: '#6B7280'
          };
      }
    };

    const riskInfo = getRiskMessage(riskGroup);

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Preventely Assessment Results</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .risk-badge { display: inline-block; padding: 10px 20px; border-radius: 25px; color: white; font-weight: bold; margin: 20px 0; }
            .download-section { background: white; padding: 25px; border-radius: 10px; margin: 20px 0; text-align: center; }
            .download-button { display: inline-block; padding: 12px 25px; margin: 10px; background: #007AFF; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏥 Preventely Health Assessment</h1>
                <p>Your Diabetes Risk Evaluation Results</p>
            </div>
            
            <div class="content">
                <h2>Hello ${firstName} ${lastName},</h2>
                
                <p>Thank you for completing your diabetes risk assessment at our clinic. Based on the Finnish Diabetes Risk Score, here are your results:</p>
                
                <div style="text-align: center;">
                    <h3>Your Risk Score: ${score} points</h3>
                    <div class="risk-badge" style="background-color: ${riskInfo.color};">
                        Risk Group ${riskGroup} - ${riskInfo.title.replace(' Assessment Result', '')}
                    </div>
                </div>
                
                <p>${riskInfo.message}</p>
                
                <div class="download-section">
                    <h3>📱 Download the Preventely App</h3>
                    <p>Get personalized health recommendations, track your progress, and connect with healthcare professionals.</p>
                    
                    <a href="${appStoreLink}" class="download-button">📱 Download for iOS</a>
                    <a href="${playStoreLink}" class="download-button">🤖 Download for Android</a>
                    
                    <p style="margin-top: 20px;">
                        <a href="${registrationLink}" style="color: #007AFF; text-decoration: none; font-weight: bold;">
                            🔗 Create Your Account Here
                        </a>
                    </p>
                </div>
                
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>🎯 What's Next?</h4>
                    <ul>
                        <li>Download the Preventely app using the links above</li>
                        <li>Create your account with this email address</li>
                        <li>Complete your health profile</li>
                        <li>Start receiving personalized recommendations</li>
                        <li>Track your progress and connect with our healthcare team</li>
                    </ul>
                </div>
                
                <p><strong>Questions?</strong> Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            
            <div class="footer">
                <p>This email was sent by your healthcare provider using Preventely's clinical assessment system.</p>
                <p>© 2024 Preventely Health Solutions. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;

    const textContent = `
Hello ${firstName} ${lastName},

Thank you for completing your diabetes risk assessment at our clinic.

Your Results:
- Risk Score: ${score} points
- Risk Group: ${riskGroup}
- ${riskInfo.message}

Download the Preventely App:
- iOS: ${appStoreLink}
- Android: ${playStoreLink}
- Create Account: ${registrationLink}

What's Next?
1. Download the Preventely app
2. Create your account with this email address
3. Complete your health profile
4. Start receiving personalized recommendations

Questions? Contact <NAME_EMAIL>

© 2024 Preventely Health Solutions
    `;

    // Send email
    await transporter.sendMail({
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: email,
      subject: `Your Preventely Health Assessment Results - Risk Group ${riskGroup}`,
      text: textContent,
      html: htmlContent,
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Invitation sent successfully' 
    });

  } catch (error) {
    console.error('Error sending invitation:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send invitation' },
      { status: 500 }
    );
  }
}
