const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Types for API responses
export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  assessments: Assessment[];
  invitations: PatientInvitation[];
  createdAt: string;
  updatedAt: string;
}

export interface Assessment {
  id: string;
  patientId: string;
  patient?: Patient;
  age: string;
  bmi: string;
  waistCircumference: string;
  physicalActivity: string;
  vegetableConsumption: string;
  bloodPressureMedication: string;
  highGlucoseHistory: string;
  familyDiabetesHistory: string;
  riskScore: number;
  riskGroup: 'A' | 'B' | 'C' | 'D';
  notes?: string;
  assessedBy?: string;
  assessmentDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface PatientInvitation {
  id: string;
  patientId: string;
  patient?: Patient;
  email: string;
  riskGroup: 'A' | 'B' | 'C' | 'D';
  riskScore: number;
  invitationSent: boolean;
  sentAt?: string;
  accountCreated: boolean;
  accountCreatedAt?: string;
  emailOpened: boolean;
  emailOpenedAt?: string;
  appDownloaded: boolean;
  appDownloadedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminSettings {
  id: string;
  smtpHost?: string;
  smtpPort?: string;
  smtpUser?: string;
  fromEmail?: string;
  fromName?: string;
  appName?: string;
  clinicName?: string;
  clinicAddress?: string;
  clinicPhone?: string;
  iosAppUrl?: string;
  androidAppUrl?: string;
  webAppUrl?: string;
  emailNotifications: boolean;
  assessmentAlerts: boolean;
  highRiskAlerts: boolean;
  dailyReports: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RiskGroupStats {
  totalAssessments: number;
  averageScore: number;
  riskDistribution: Array<{
    group: 'A' | 'B' | 'C' | 'D';
    count: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    date: string;
    assessments: number;
    averageScore: number;
  }>;
}

export interface SummaryReport {
  summary: {
    totalAssessments: number;
    highRiskPatients: number;
    invitationsSent: number;
    accountsCreated: number;
  };
  riskGroupBreakdown: Array<{
    group: 'A' | 'B' | 'C' | 'D';
    count: number;
    averageScore: number;
  }>;
  recentAssessments: Assessment[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface GlucoseReading {
  id: string;
  value: number;
  timestamp: string;
  trend?: string;
  trendRate?: number;
  source?: string;
  notes?: string;
}

export interface CGMStatistics {
  totalReadings: number;
  averageGlucose: number;
  timeInRange: number;
  timeAboveRange: number;
  timeBelowRange: number;
  targetRange: {
    min: number;
    max: number;
  };
}

export interface PatientCGMData {
  patient: Patient;
  cgmData: {
    readings: GlucoseReading[];
    statistics: CGMStatistics;
    dateRange: {
      startDate: string;
      endDate: string;
    };
  };
}

export interface CGMOverview {
  overview: Array<{
    patient: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      riskGroup: 'A' | 'B' | 'C' | 'D' | null;
      riskScore: number | null;
    };
    cgmSummary: {
      totalReadings: number;
      averageGlucose: number;
      timeInRange: number;
      latestReading: {
        value: number;
        timestamp: string;
        trend?: string;
      };
      targetRange: {
        min: number;
        max: number;
      };
    };
  }>;
  summary: {
    totalPatientsWithCGM: number;
    totalPatients: number;
    dateRange: {
      startDate: string;
      endDate: string;
      days: number;
    };
  };
}

// API response wrapper
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: {
    [key: string]: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// Assessment API functions
export const assessmentApi = {
  // Create new assessment
  create: async (assessmentData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    age: string;
    bmi: string;
    waistCircumference: string;
    physicalActivity: string;
    vegetableConsumption: string;
    bloodPressureMedication: string;
    highGlucoseHistory: string;
    familyDiabetesHistory: string;
    notes?: string;
    assessedBy?: string;
  }): Promise<ApiResponse<{ assessment: Assessment; riskScore: number; riskGroup: string }>> => {
    return apiRequest('/admin/assessments', {
      method: 'POST',
      body: JSON.stringify(assessmentData),
    });
  },

  // Get all assessments
  getAll: async (params?: {
    page?: number;
    limit?: number;
    riskGroup?: 'A' | 'B' | 'C' | 'D';
  }): Promise<PaginatedResponse<Assessment>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.riskGroup) searchParams.append('riskGroup', params.riskGroup);

    const query = searchParams.toString();
    return apiRequest(`/admin/assessments${query ? `?${query}` : ''}`);
  },
};

// Patient API functions
export const patientApi = {
  // Get all patients
  getAll: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<PaginatedResponse<Patient>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const query = searchParams.toString();
    return apiRequest(`/admin/patients${query ? `?${query}` : ''}`);
  },
};

// Invitation API functions
export const invitationApi = {
  // Send invitation
  send: async (invitationData: {
    patientId: string;
    email: string;
    firstName: string;
    lastName: string;
    riskGroup: 'A' | 'B' | 'C' | 'D';
    riskScore: number;
  }): Promise<ApiResponse<{ message: string }>> => {
    return apiRequest('/admin/send-invitation', {
      method: 'POST',
      body: JSON.stringify(invitationData),
    });
  },
};

// Risk Groups API functions
export const riskGroupApi = {
  // Get risk group statistics
  getStats: async (): Promise<ApiResponse<RiskGroupStats>> => {
    return apiRequest('/admin/risk-groups/stats');
  },
};

// Reports API functions
export const reportsApi = {
  // Get summary report
  getSummary: async (params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<SummaryReport>> => {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);

    const query = searchParams.toString();
    return apiRequest(`/admin/reports/summary${query ? `?${query}` : ''}`);
  },
};

// Settings API functions
export const settingsApi = {
  // Get settings
  get: async (): Promise<ApiResponse<AdminSettings>> => {
    return apiRequest('/admin/settings');
  },

  // Update settings
  update: async (settings: Partial<AdminSettings>): Promise<ApiResponse<AdminSettings>> => {
    return apiRequest('/admin/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  },
};

// CGM API functions
export const cgmApi = {
  // Get CGM data for a specific patient
  getPatientData: async (
    patientId: string,
    params?: {
      startDate?: string;
      endDate?: string;
      limit?: number;
    }
  ): Promise<ApiResponse<PatientCGMData>> => {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const queryString = searchParams.toString();
    const url = `/admin/patients/${patientId}/cgm-data${queryString ? `?${queryString}` : ''}`;

    return apiRequest(url);
  },

  // Get CGM overview for all patients
  getOverview: async (days?: number): Promise<ApiResponse<CGMOverview>> => {
    const searchParams = new URLSearchParams();
    if (days) searchParams.append('days', days.toString());

    const queryString = searchParams.toString();
    const url = `/admin/cgm-overview${queryString ? `?${queryString}` : ''}`;

    return apiRequest(url);
  },
};
