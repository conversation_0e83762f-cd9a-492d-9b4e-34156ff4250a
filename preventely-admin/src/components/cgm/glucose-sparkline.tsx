'use client';

import { useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement);

interface GlucoseSparklineProps {
  readings: Array<{ value: number; timestamp: string }>;
  targetMin?: number;
  targetMax?: number;
  width?: number;
  height?: number;
}

export function GlucoseSparkline({ 
  readings, 
  targetMin = 70, 
  targetMax = 180,
  width = 120,
  height = 40 
}: GlucoseSparklineProps) {
  const chartData = useMemo(() => {
    // Sort and take last 24 readings (last 6 hours if every 15 minutes)
    const sortedReadings = [...readings]
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
      .slice(-24);

    const glucoseData = sortedReadings.map(reading => reading.value);

    return {
      labels: sortedReadings.map(() => ''), // Empty labels for sparkline
      datasets: [
        {
          data: glucoseData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          pointRadius: 0,
          pointHoverRadius: 0,
          fill: false,
          tension: 0.2,
        },
      ],
    };
  }, [readings]);

  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
        min: Math.max(50, Math.min(...readings.map(r => r.value)) - 10),
        max: Math.min(400, Math.max(...readings.map(r => r.value)) + 10),
      },
    },
    elements: {
      point: {
        radius: 0,
      },
    },
    interaction: {
      intersect: false,
    },
  }), [readings]);

  if (readings.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-100 rounded"
        style={{ width, height }}
      >
        <span className="text-xs text-gray-400">No data</span>
      </div>
    );
  }

  const latestReading = readings[readings.length - 1];
  const isInRange = latestReading.value >= targetMin && latestReading.value <= targetMax;
  const isHigh = latestReading.value > targetMax;
  const isLow = latestReading.value < targetMin;

  const borderColor = isHigh ? 'border-red-200' : isLow ? 'border-blue-200' : 'border-green-200';

  return (
    <div 
      className={`relative rounded border ${borderColor}`}
      style={{ width, height }}
    >
      <Line data={chartData} options={options} />
      
      {/* Latest value overlay */}
      <div className="absolute top-0 right-0 px-1 py-0.5 bg-white bg-opacity-90 rounded-bl text-xs font-medium">
        <span className={`${isHigh ? 'text-red-600' : isLow ? 'text-blue-600' : 'text-green-600'}`}>
          {latestReading.value}
        </span>
      </div>
    </div>
  );
}

// Component for showing glucose trend in a compact format
interface GlucoseTrendIndicatorProps {
  currentValue: number;
  previousValue?: number;
  targetMin?: number;
  targetMax?: number;
}

export function GlucoseTrendIndicator({ 
  currentValue, 
  previousValue,
  targetMin = 70,
  targetMax = 180 
}: GlucoseTrendIndicatorProps) {
  const isInRange = currentValue >= targetMin && currentValue <= targetMax;
  const isHigh = currentValue > targetMax;
  const isLow = currentValue < targetMin;

  const trend = previousValue ? currentValue - previousValue : 0;
  const trendDirection = Math.abs(trend) < 5 ? 'stable' : trend > 0 ? 'rising' : 'falling';

  const statusColor = isHigh ? 'text-red-600 bg-red-50' : isLow ? 'text-blue-600 bg-blue-50' : 'text-green-600 bg-green-50';
  const trendIcon = trendDirection === 'rising' ? '↗️' : trendDirection === 'falling' ? '↘️' : '➡️';

  return (
    <div className="flex items-center space-x-2">
      <div className={`px-2 py-1 rounded text-sm font-medium ${statusColor}`}>
        {currentValue} mg/dL
      </div>
      <span className="text-sm">{trendIcon}</span>
      {previousValue && (
        <span className="text-xs text-gray-500">
          {trend > 0 ? '+' : ''}{trend.toFixed(0)}
        </span>
      )}
    </div>
  );
}

// Component for showing a mini glucose distribution chart
interface GlucoseDistributionMiniProps {
  timeInRange: number;
  timeAboveRange: number;
  timeBelowRange: number;
  width?: number;
  height?: number;
}

export function GlucoseDistributionMini({ 
  timeInRange, 
  timeAboveRange, 
  timeBelowRange,
  width = 100,
  height = 20 
}: GlucoseDistributionMiniProps) {
  const total = timeInRange + timeAboveRange + timeBelowRange;
  
  if (total === 0) {
    return (
      <div 
        className="bg-gray-200 rounded"
        style={{ width, height }}
      />
    );
  }

  const inRangeWidth = (timeInRange / total) * width;
  const aboveRangeWidth = (timeAboveRange / total) * width;
  const belowRangeWidth = (timeBelowRange / total) * width;

  return (
    <div 
      className="flex rounded overflow-hidden"
      style={{ width, height }}
    >
      {belowRangeWidth > 0 && (
        <div 
          className="bg-blue-500"
          style={{ width: belowRangeWidth }}
          title={`Below range: ${timeBelowRange.toFixed(1)}%`}
        />
      )}
      {inRangeWidth > 0 && (
        <div 
          className="bg-green-500"
          style={{ width: inRangeWidth }}
          title={`In range: ${timeInRange.toFixed(1)}%`}
        />
      )}
      {aboveRangeWidth > 0 && (
        <div 
          className="bg-red-500"
          style={{ width: aboveRangeWidth }}
          title={`Above range: ${timeAboveRange.toFixed(1)}%`}
        />
      )}
    </div>
  );
}
