'use client';

import { useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  TimeScale,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';
import { format } from 'date-fns';
import { type GlucoseReading, type CGMStatistics } from '@/lib/api';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  TimeScale
);

interface GlucoseChartProps {
  readings: GlucoseReading[];
  statistics: CGMStatistics;
  height?: number;
  showTargetRange?: boolean;
}

export function GlucoseChart({ 
  readings, 
  statistics, 
  height = 400, 
  showTargetRange = true 
}: GlucoseChartProps) {
  const chartData = useMemo(() => {
    // Sort readings by timestamp
    const sortedReadings = [...readings].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    const glucoseData = sortedReadings.map(reading => ({
      x: new Date(reading.timestamp),
      y: reading.value,
      trend: reading.trend,
      trendRate: reading.trendRate,
    }));

    const datasets = [
      {
        label: 'Glucose Level',
        data: glucoseData,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: glucoseData.map(point => {
          if (point.y < statistics.targetRange.min) return 'rgb(37, 99, 235)'; // Blue for low
          if (point.y > statistics.targetRange.max) return 'rgb(239, 68, 68)'; // Red for high
          return 'rgb(34, 197, 94)'; // Green for in range
        }),
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        fill: false,
        tension: 0.1,
      },
    ];

    // Add target range bands if enabled
    if (showTargetRange && sortedReadings.length > 0) {
      const timeRange = {
        start: new Date(sortedReadings[0].timestamp),
        end: new Date(sortedReadings[sortedReadings.length - 1].timestamp),
      };

      // Target range band
      datasets.push({
        label: 'Target Range',
        data: [
          { x: timeRange.start, y: statistics.targetRange.max },
          { x: timeRange.end, y: statistics.targetRange.max },
        ],
        borderColor: 'rgba(34, 197, 94, 0.3)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 1,
        pointRadius: 0,
        fill: '+1',
        tension: 0,
      } as any);

      datasets.push({
        label: 'Target Range Lower',
        data: [
          { x: timeRange.start, y: statistics.targetRange.min },
          { x: timeRange.end, y: statistics.targetRange.min },
        ],
        borderColor: 'rgba(34, 197, 94, 0.3)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        borderWidth: 1,
        pointRadius: 0,
        fill: false,
        tension: 0,
      } as any);
    }

    return { datasets };
  }, [readings, statistics, showTargetRange]);

  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: 'Continuous Glucose Monitoring',
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
      legend: {
        display: true,
        position: 'top' as const,
        labels: {
          filter: (legendItem: any) => {
            // Hide the target range lower line from legend
            return legendItem.text !== 'Target Range Lower';
          },
        },
      },
      tooltip: {
        callbacks: {
          title: (context: any) => {
            const date = new Date(context[0].parsed.x);
            return format(date, 'MMM dd, yyyy HH:mm');
          },
          label: (context: any) => {
            if (context.datasetIndex === 0) {
              const reading = readings.find(r => 
                new Date(r.timestamp).getTime() === context.parsed.x
              );
              
              let label = `Glucose: ${context.parsed.y} mg/dL`;
              
              if (reading?.trend && reading.trend !== 'stable') {
                const trendSymbol = reading.trend === 'rising' ? '↗️' : '↘️';
                label += ` ${trendSymbol}`;
                
                if (reading.trendRate) {
                  label += ` (${reading.trendRate > 0 ? '+' : ''}${reading.trendRate.toFixed(1)} mg/dL/min)`;
                }
              }
              
              // Add status
              if (context.parsed.y < statistics.targetRange.min) {
                label += ' - LOW';
              } else if (context.parsed.y > statistics.targetRange.max) {
                label += ' - HIGH';
              } else {
                label += ' - IN RANGE';
              }
              
              return label;
            }
            return context.dataset.label;
          },
        },
      },
    },
    scales: {
      x: {
        type: 'time' as const,
        time: {
          displayFormats: {
            hour: 'HH:mm',
            day: 'MMM dd',
          },
        },
        title: {
          display: true,
          text: 'Time',
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Glucose (mg/dL)',
        },
        min: Math.max(50, Math.min(...readings.map(r => r.value)) - 20),
        max: Math.min(400, Math.max(...readings.map(r => r.value)) + 20),
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          callback: function(value: any) {
            return value + ' mg/dL';
          },
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  }), [readings, statistics]);

  if (readings.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300"
        style={{ height }}
      >
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📊</div>
          <p className="text-gray-500 font-medium">No glucose data available</p>
          <p className="text-gray-400 text-sm">Connect a CGM device to see glucose trends</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height }}>
      <Line data={chartData} options={options} />
    </div>
  );
}
