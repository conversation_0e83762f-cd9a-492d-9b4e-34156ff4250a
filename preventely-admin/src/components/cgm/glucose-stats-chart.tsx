'use client';

import { useMemo } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
} from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';
import { type CGMStatistics } from '@/lib/api';

ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);

interface GlucoseStatsChartProps {
  statistics: CGMStatistics;
  type?: 'doughnut' | 'bar';
  height?: number;
}

export function GlucoseStatsChart({ 
  statistics, 
  type = 'doughnut', 
  height = 300 
}: GlucoseStatsChartProps) {
  const timeInRangeData = useMemo(() => {
    const data = {
      labels: ['Time in Range', 'Time Above Range', 'Time Below Range'],
      datasets: [
        {
          data: [
            statistics.timeInRange,
            statistics.timeAboveRange,
            statistics.timeBelowRange,
          ],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)', // Green for in range
            'rgba(239, 68, 68, 0.8)',  // Red for above range
            'rgba(59, 130, 246, 0.8)', // Blue for below range
          ],
          borderColor: [
            'rgb(34, 197, 94)',
            'rgb(239, 68, 68)',
            'rgb(59, 130, 246)',
          ],
          borderWidth: 2,
        },
      ],
    };

    return data;
  }, [statistics]);

  const doughnutOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: true,
        text: 'Time in Range Analysis',
        font: {
          size: 14,
          weight: 'bold' as const,
        },
      },
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
        },
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label;
            const value = context.parsed;
            const target = getTargetForRange(label);
            
            let tooltipText = `${label}: ${value.toFixed(1)}%`;
            if (target) {
              tooltipText += ` (Target: ${target})`;
            }
            
            return tooltipText;
          },
        },
      },
    },
    cutout: '60%',
  }), []);

  const barOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: true,
        text: 'Glucose Range Distribution',
        font: {
          size: 14,
          weight: 'bold' as const,
        },
      },
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label;
            const value = context.parsed.y;
            const target = getTargetForRange(label);
            
            let tooltipText = `${label}: ${value.toFixed(1)}%`;
            if (target) {
              tooltipText += ` (Target: ${target})`;
            }
            
            return tooltipText;
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Percentage (%)',
        },
        ticks: {
          callback: function(value: any) {
            return value + '%';
          },
        },
      },
      x: {
        title: {
          display: true,
          text: 'Glucose Ranges',
        },
      },
    },
  }), []);

  const getTargetForRange = (label: string): string | null => {
    switch (label) {
      case 'Time in Range':
        return '>70%';
      case 'Time Above Range':
        return '<25%';
      case 'Time Below Range':
        return '<4%';
      default:
        return null;
    }
  };

  const getRangeStatus = (label: string, value: number): 'good' | 'warning' | 'poor' => {
    switch (label) {
      case 'Time in Range':
        if (value >= 70) return 'good';
        if (value >= 50) return 'warning';
        return 'poor';
      case 'Time Above Range':
        if (value <= 25) return 'good';
        if (value <= 50) return 'warning';
        return 'poor';
      case 'Time Below Range':
        if (value <= 4) return 'good';
        if (value <= 10) return 'warning';
        return 'poor';
      default:
        return 'good';
    }
  };

  if (type === 'doughnut') {
    return (
      <div style={{ height }}>
        <Doughnut data={timeInRangeData} options={doughnutOptions} />
        
        {/* Center text showing average glucose */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {statistics.averageGlucose}
            </div>
            <div className="text-sm text-gray-500">mg/dL avg</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height }}>
      <Bar data={timeInRangeData} options={barOptions} />
      
      {/* Status indicators below the chart */}
      <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
        {timeInRangeData.labels.map((label, index) => {
          const value = timeInRangeData.datasets[0].data[index];
          const status = getRangeStatus(label, value);
          const statusColor = {
            good: 'text-green-600 bg-green-50',
            warning: 'text-yellow-600 bg-yellow-50',
            poor: 'text-red-600 bg-red-50',
          }[status];
          
          return (
            <div key={label} className={`p-2 rounded text-center ${statusColor}`}>
              <div className="font-medium">{value.toFixed(1)}%</div>
              <div className="text-xs opacity-75">{label.replace('Time ', '')}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Component for displaying glucose trends over time
interface GlucoseTrendChartProps {
  statistics: CGMStatistics;
  height?: number;
}

export function GlucoseTrendChart({ statistics, height = 200 }: GlucoseTrendChartProps) {
  const trendData = useMemo(() => {
    return {
      labels: ['Below Range', 'In Range', 'Above Range'],
      datasets: [
        {
          label: 'Current Period',
          data: [
            statistics.timeBelowRange,
            statistics.timeInRange,
            statistics.timeAboveRange,
          ],
          backgroundColor: [
            'rgba(59, 130, 246, 0.6)',
            'rgba(34, 197, 94, 0.6)',
            'rgba(239, 68, 68, 0.6)',
          ],
          borderColor: [
            'rgb(59, 130, 246)',
            'rgb(34, 197, 94)',
            'rgb(239, 68, 68)',
          ],
          borderWidth: 1,
        },
        {
          label: 'Target',
          data: [4, 70, 25], // Standard diabetes targets
          backgroundColor: [
            'rgba(156, 163, 175, 0.3)',
            'rgba(156, 163, 175, 0.3)',
            'rgba(156, 163, 175, 0.3)',
          ],
          borderColor: 'rgb(156, 163, 175)',
          borderWidth: 1,
          borderDash: [5, 5],
        },
      ],
    };
  }, [statistics]);

  const trendOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: true,
        text: 'Current vs Target Ranges',
        font: {
          size: 14,
          weight: 'bold' as const,
        },
      },
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Percentage (%)',
        },
        ticks: {
          callback: function(value: any) {
            return value + '%';
          },
        },
      },
    },
  }), []);

  return (
    <div style={{ height }}>
      <Bar data={trendData} options={trendOptions} />
    </div>
  );
}
