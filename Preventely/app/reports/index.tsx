import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/colors';
import GlucoseChart from '../components/reports/GlucoseChart';
import WeightChart from '../components/reports/WeightChart';
import NutritionChart from '../components/reports/NutritionChart';

// Date range types
type DateRangeType = 'day' | 'week' | 'month' | 'custom';

export default function ReportsScreen() {
  const [dateRange, setDateRange] = useState<DateRangeType>('week');
  
  // Handle date range selection
  const handleDateRangeSelect = (type: DateRangeType) => {
    setDateRange(type);
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Reports</Text>
        <TouchableOpacity style={styles.exportButton}>
          <Ionicons name="download-outline" size={20} color={Colors.primary} />
          <Text style={styles.exportText}>Export</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.dateRangeSelector}>
        <TouchableOpacity 
          style={[
            styles.dateRangeButton, 
            dateRange === 'day' && styles.dateRangeButtonActive
          ]}
          onPress={() => handleDateRangeSelect('day')}
        >
          <Text 
            style={[
              styles.dateRangeButtonText,
              dateRange === 'day' && styles.dateRangeButtonTextActive
            ]}
          >
            Day
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[
            styles.dateRangeButton, 
            dateRange === 'week' && styles.dateRangeButtonActive
          ]}
          onPress={() => handleDateRangeSelect('week')}
        >
          <Text 
            style={[
              styles.dateRangeButtonText,
              dateRange === 'week' && styles.dateRangeButtonTextActive
            ]}
          >
            Week
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[
            styles.dateRangeButton, 
            dateRange === 'month' && styles.dateRangeButtonActive
          ]}
          onPress={() => handleDateRangeSelect('month')}
        >
          <Text 
            style={[
              styles.dateRangeButtonText,
              dateRange === 'month' && styles.dateRangeButtonTextActive
            ]}
          >
            Month
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[
            styles.dateRangeButton, 
            dateRange === 'custom' && styles.dateRangeButtonActive
          ]}
          onPress={() => handleDateRangeSelect('custom')}
        >
          <Text 
            style={[
              styles.dateRangeButtonText,
              dateRange === 'custom' && styles.dateRangeButtonTextActive
            ]}
          >
            Custom
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Glucose Report Card */}
        <View style={styles.reportCard}>
          <View style={styles.reportCardHeader}>
            <View style={styles.reportCardTitleContainer}>
              <Ionicons name="pulse-outline" size={20} color={Colors.primary} />
              <Text style={styles.reportCardTitle}>Glucose</Text>
            </View>
            <TouchableOpacity style={styles.viewDetailsButton}>
              <Text style={styles.viewDetailsText}>Details</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.reportCardContent}>
            <GlucoseChart period={dateRange === 'custom' ? 'week' : dateRange} data={[]} />
            
            <View style={styles.metricsContainer}>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>118</Text>
                <Text style={styles.metricLabel}>Average</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>142</Text>
                <Text style={styles.metricLabel}>Peak</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>85%</Text>
                <Text style={styles.metricLabel}>In Range</Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* Weight Report Card */}
        <View style={styles.reportCard}>
          <View style={styles.reportCardHeader}>
            <View style={styles.reportCardTitleContainer}>
              <Ionicons name="fitness-outline" size={20} color={Colors.success} />
              <Text style={styles.reportCardTitle}>Weight & BMI</Text>
            </View>
            <TouchableOpacity style={styles.viewDetailsButton}>
              <Text style={styles.viewDetailsText}>Details</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.reportCardContent}>
            <WeightChart period={dateRange === 'custom' ? 'week' : dateRange} data={[]} />
            
            <View style={styles.metricsContainer}>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>76.5</Text>
                <Text style={styles.metricLabel}>Current</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>-0.8</Text>
                <Text style={styles.metricLabel}>Change</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>23.6</Text>
                <Text style={styles.metricLabel}>BMI</Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* Nutrition Report Card */}
        <View style={styles.reportCard}>
          <View style={styles.reportCardHeader}>
            <View style={styles.reportCardTitleContainer}>
              <Ionicons name="nutrition-outline" size={20} color={Colors.warning} />
              <Text style={styles.reportCardTitle}>Nutrition</Text>
            </View>
            <TouchableOpacity style={styles.viewDetailsButton}>
              <Text style={styles.viewDetailsText}>Details</Text>
              <Ionicons name="chevron-forward" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.reportCardContent}>
            <NutritionChart period={dateRange === 'custom' ? 'week' : dateRange} data={[]} />
            
            <View style={styles.metricsContainer}>
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>45</Text>
                <Text style={styles.metricLabel}>Avg GI</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>12</Text>
                <Text style={styles.metricLabel}>Avg GL</Text>
              </View>
              <View style={styles.metricDivider} />
              <View style={styles.metricItem}>
                <Text style={styles.metricValue}>80%</Text>
                <Text style={styles.metricLabel}>Low GI Meals</Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* AI Insights Card */}
        <View style={styles.reportCard}>
          <View style={styles.reportCardHeader}>
            <View style={styles.reportCardTitleContainer}>
              <Ionicons name="bulb-outline" size={20} color={Colors.info} />
              <Text style={styles.reportCardTitle}>AI Insights</Text>
            </View>
          </View>
          
          <View style={styles.reportCardContent}>
            <View style={styles.insightContainer}>
              <Text style={styles.insightText}>
                Your glucose levels tend to be higher in the mornings. Consider having a protein-rich breakfast to help stabilize your blood sugar throughout the day.
              </Text>
              <TouchableOpacity style={styles.insightActionButton}>
                <Text style={styles.insightActionText}>View Breakfast Suggestions</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.insightDivider} />
            
            <View style={styles.insightContainer}>
              <Text style={styles.insightText}>
                Your weight has decreased by 0.8 kg this week, which is a healthy rate of weight loss. Keep up with your current diet and exercise routine.
              </Text>
              <TouchableOpacity style={styles.insightActionButton}>
                <Text style={styles.insightActionText}>View Exercise Plan</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        
        {/* Export Options Card */}
        <View style={styles.reportCard}>
          <View style={styles.reportCardHeader}>
            <View style={styles.reportCardTitleContainer}>
              <Ionicons name="document-text-outline" size={20} color={Colors.primary} />
              <Text style={styles.reportCardTitle}>Export Options</Text>
            </View>
          </View>
          
          <View style={[styles.reportCardContent, styles.exportOptionsContainer]}>
            <TouchableOpacity style={styles.exportOption}>
              <Ionicons name="document-outline" size={32} color={Colors.primary} />
              <Text style={styles.exportOptionText}>PDF Report</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.exportOption}>
              <Ionicons name="mail-outline" size={32} color={Colors.primary} />
              <Text style={styles.exportOptionText}>Email Report</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.exportOption}>
              <Ionicons name="print-outline" size={32} color={Colors.primary} />
              <Text style={styles.exportOptionText}>Print Report</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.exportOption}>
              <Ionicons name="share-social-outline" size={32} color={Colors.primary} />
              <Text style={styles.exportOptionText}>Share Report</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${Colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  exportText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
  },
  dateRangeSelector: {
    flexDirection: 'row',
    padding: 16,
  },
  dateRangeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  dateRangeButtonActive: {
    borderBottomColor: Colors.primary,
  },
  dateRangeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  dateRangeButtonTextActive: {
    color: Colors.primary,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  reportCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reportCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  reportCardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportCardTitle: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewDetailsText: {
    fontSize: 14,
    color: Colors.primary,
    marginRight: 4,
  },
  reportCardContent: {
    padding: 16,
  },
  chartPlaceholder: {
    height: 150,
    backgroundColor: `${Colors.primary}10`,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartPlaceholderText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '500',
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metricItem: {
    flex: 1,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  metricDivider: {
    width: 1,
    height: 40,
    backgroundColor: Colors.border,
  },
  insightContainer: {
    marginBottom: 16,
  },
  insightText: {
    fontSize: 14,
    color: Colors.textPrimary,
    lineHeight: 20,
    marginBottom: 8,
  },
  insightActionButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: `${Colors.primary}15`,
    borderRadius: 16,
  },
  insightActionText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
  insightDivider: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 16,
  },
  exportOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  exportOption: {
    width: '48%',
    backgroundColor: `${Colors.primary}08`,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  exportOptionText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
    marginTop: 8,
  },
});
