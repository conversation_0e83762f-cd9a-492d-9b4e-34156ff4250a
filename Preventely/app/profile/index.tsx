import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Colors from '../constants/colors';

// Dummy user data
const userData = {
  name: 'Maj<PERSON>',
  age: 32,
  gender: 'Male',
  condition: 'Group B',
  photoUrl: null, // We'll use an icon placeholder
  weight: 78.5, // kg
  height: 175, // cm
  bmi: 25.6,
  glucoseTargetRange: {
    min: 70,
    max: 140
  },
  medications: [
    { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' },
    { name: 'Glipizide', dosage: '5mg', frequency: 'Once daily' }
  ],
  allergies: ['Penicillin', 'Shellfish'],
  doctor: {
    name: 'Dr. <PERSON>',
    specialty: 'Endocrinologist',
    phone: '+****************',
    email: 'dr.joh<PERSON>@healthcare.com'
  },
  preferences: {
    notifications: true,
    language: 'EN',
    darkMode: false,
    units: 'metric'
  },
  connectedDevices: [
    { name: 'Apple Health', connected: true },
    { name: 'Fitbit', connected: false },
    { name: 'Dexcom G6', connected: true },
    { name: 'FreeStyle Libre 2', connected: false }
  ]
};

export default function ProfileScreen() {
  const router = useRouter();
  const [notificationsEnabled, setNotificationsEnabled] = useState(userData.preferences.notifications);
  const [darkModeEnabled, setDarkModeEnabled] = useState(userData.preferences.darkMode);
  const [language, setLanguage] = useState(userData.preferences.language);
  
  // Calculate BMI
  const calculateBMI = (weightKg: number, heightCm: number): number => {
    const heightM = heightCm / 100;
    return weightKg / (heightM * heightM);
  };
  
  // Get BMI category
  const getBMICategory = (bmi: number): string => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  };
  
  // Toggle language
  const toggleLanguage = () => {
    setLanguage(language === 'EN' ? 'AR' : 'EN');
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity style={styles.editButton}>
          <Ionicons name="settings-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Profile Top Section */}
        <View style={styles.profileSection}>
          <View style={styles.profilePhotoContainer}>
            {userData.photoUrl ? (
              <Image 
                source={{ uri: userData.photoUrl }} 
                style={styles.profilePhoto} 
              />
            ) : (
              <View style={styles.profilePhotoPlaceholder}>
                <Ionicons name="person" size={60} color={Colors.white} />
              </View>
            )}
          </View>
          
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{userData.name}</Text>
            <Text style={styles.profileDetails}>
              {userData.age}, {userData.gender}, {userData.condition}
            </Text>
            
            <TouchableOpacity style={styles.editProfileButton}>
              <Text style={styles.editProfileText}>Edit Profile</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Health Details Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Health Details</Text>
          
          <View style={styles.healthMetricsContainer}>
            {/* Weight Card */}
            <View style={styles.healthMetricCard}>
              <View style={styles.healthMetricIconContainer}>
                <Ionicons name="fitness-outline" size={24} color={Colors.primary} />
              </View>
              <Text style={styles.healthMetricValue}>{userData.weight} kg</Text>
              <Text style={styles.healthMetricLabel}>Weight</Text>
            </View>
            
            {/* Height Card */}
            <View style={styles.healthMetricCard}>
              <View style={styles.healthMetricIconContainer}>
                <Ionicons name="resize-outline" size={24} color={Colors.primary} />
              </View>
              <Text style={styles.healthMetricValue}>{userData.height} cm</Text>
              <Text style={styles.healthMetricLabel}>Height</Text>
            </View>
            
            {/* BMI Card */}
            <View style={styles.healthMetricCard}>
              <View style={styles.healthMetricIconContainer}>
                <Ionicons name="stats-chart-outline" size={24} color={Colors.primary} />
              </View>
              <Text style={styles.healthMetricValue}>{userData.bmi.toFixed(1)}</Text>
              <Text style={styles.healthMetricLabel}>BMI ({getBMICategory(userData.bmi)})</Text>
            </View>
          </View>
          
          {/* Glucose Target Range Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                <Ionicons name="pulse-outline" size={24} color={Colors.primary} />
                <Text style={styles.cardTitle}>Glucose Target Range</Text>
              </View>
              <TouchableOpacity>
                <Ionicons name="pencil-outline" size={20} color={Colors.primary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.glucoseRangeContainer}>
              <View style={styles.glucoseRangeItem}>
                <Text style={styles.glucoseRangeValue}>{userData.glucoseTargetRange.min}</Text>
                <Text style={styles.glucoseRangeLabel}>Min (mg/dL)</Text>
              </View>
              <View style={styles.glucoseRangeDivider} />
              <View style={styles.glucoseRangeItem}>
                <Text style={styles.glucoseRangeValue}>{userData.glucoseTargetRange.max}</Text>
                <Text style={styles.glucoseRangeLabel}>Max (mg/dL)</Text>
              </View>
            </View>
          </View>
          
          {/* Medications Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                <Ionicons name="medkit-outline" size={24} color={Colors.primary} />
                <Text style={styles.cardTitle}>Current Medications</Text>
              </View>
              <TouchableOpacity>
                <Ionicons name="add-outline" size={24} color={Colors.primary} />
              </TouchableOpacity>
            </View>
            
            {userData.medications.map((medication, index) => (
              <View key={index} style={styles.medicationItem}>
                <View style={styles.medicationIconContainer}>
                  <Ionicons name="medical-outline" size={20} color={Colors.white} />
                </View>
                <View style={styles.medicationInfo}>
                  <Text style={styles.medicationName}>{medication.name}</Text>
                  <Text style={styles.medicationDetails}>
                    {medication.dosage} • {medication.frequency}
                  </Text>
                </View>
                <TouchableOpacity>
                  <Ionicons name="ellipsis-vertical" size={20} color={Colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ))}
            
            <TouchableOpacity style={styles.addItemButton}>
              <Text style={styles.addItemText}>Add Medication</Text>
            </TouchableOpacity>
          </View>
          
          {/* Allergies Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                <Ionicons name="alert-circle-outline" size={24} color={Colors.primary} />
                <Text style={styles.cardTitle}>Allergies</Text>
              </View>
              <TouchableOpacity>
                <Ionicons name="add-outline" size={24} color={Colors.primary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.allergiesContainer}>
              {userData.allergies.map((allergy, index) => (
                <View key={index} style={styles.allergyTag}>
                  <Text style={styles.allergyText}>{allergy}</Text>
                  <TouchableOpacity style={styles.allergyRemoveButton}>
                    <Ionicons name="close" size={16} color={Colors.error} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <TouchableOpacity style={styles.addItemButton}>
              <Text style={styles.addItemText}>Add Allergy</Text>
            </TouchableOpacity>
          </View>
          
          {/* Doctor Info Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                <Ionicons name="person-outline" size={24} color={Colors.primary} />
                <Text style={styles.cardTitle}>Healthcare Provider</Text>
              </View>
              <TouchableOpacity>
                <Ionicons name="pencil-outline" size={20} color={Colors.primary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.doctorInfoContainer}>
              <View style={styles.doctorAvatarContainer}>
                <Ionicons name="medical-outline" size={32} color={Colors.white} />
              </View>
              
              <View style={styles.doctorDetails}>
                <Text style={styles.doctorName}>{userData.doctor.name}</Text>
                <Text style={styles.doctorSpecialty}>{userData.doctor.specialty}</Text>
                
                <View style={styles.doctorContactContainer}>
                  <TouchableOpacity style={styles.doctorContactButton}>
                    <Ionicons name="call-outline" size={20} color={Colors.primary} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.doctorContactButton}>
                    <Ionicons name="mail-outline" size={20} color={Colors.primary} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.doctorContactButton}>
                    <Ionicons name="calendar-outline" size={20} color={Colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
        
        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          
          <View style={styles.card}>
            {/* Notifications Setting */}
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Ionicons name="notifications-outline" size={24} color={Colors.primary} />
                <View style={styles.settingTextContainer}>
                  <Text style={styles.settingTitle}>Notifications</Text>
                  <Text style={styles.settingDescription}>Receive alerts and reminders</Text>
                </View>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: Colors.border, true: `${Colors.primary}80` }}
                thumbColor={notificationsEnabled ? Colors.primary : '#f4f3f4'}
              />
            </View>
            
            {/* Language Setting */}
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Ionicons name="language-outline" size={24} color={Colors.primary} />
                <View style={styles.settingTextContainer}>
                  <Text style={styles.settingTitle}>Language</Text>
                  <Text style={styles.settingDescription}>Choose your preferred language</Text>
                </View>
              </View>
              <TouchableOpacity 
                style={styles.languageToggle}
                onPress={toggleLanguage}
              >
                <View 
                  style={[
                    styles.languageOption, 
                    language === 'EN' && styles.languageOptionActive
                  ]}
                >
                  <Text 
                    style={[
                      styles.languageOptionText,
                      language === 'EN' && styles.languageOptionTextActive
                    ]}
                  >
                    EN
                  </Text>
                </View>
                <View 
                  style={[
                    styles.languageOption, 
                    language === 'AR' && styles.languageOptionActive
                  ]}
                >
                  <Text 
                    style={[
                      styles.languageOptionText,
                      language === 'AR' && styles.languageOptionTextActive
                    ]}
                  >
                    AR
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            
            {/* Dark Mode Setting */}
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Ionicons name="moon-outline" size={24} color={Colors.primary} />
                <View style={styles.settingTextContainer}>
                  <Text style={styles.settingTitle}>Dark Mode</Text>
                  <Text style={styles.settingDescription}>Switch to dark theme</Text>
                </View>
              </View>
              <Switch
                value={darkModeEnabled}
                onValueChange={setDarkModeEnabled}
                trackColor={{ false: Colors.border, true: `${Colors.primary}80` }}
                thumbColor={darkModeEnabled ? Colors.primary : '#f4f3f4'}
              />
            </View>
          </View>
          
          {/* Connected Devices Card */}
          <View style={styles.card}>
            <View style={styles.cardHeader}>
              <View style={styles.cardHeaderLeft}>
                <Ionicons name="watch-outline" size={24} color={Colors.primary} />
                <Text style={styles.cardTitle}>Connected Devices</Text>
              </View>
              <TouchableOpacity>
                <Ionicons name="add-outline" size={24} color={Colors.primary} />
              </TouchableOpacity>
            </View>
            
            {userData.connectedDevices.map((device, index) => (
              <TouchableOpacity 
                key={index} 
                style={styles.deviceItem}
                onPress={() => {
                  if (device.name === 'FreeStyle Libre 2') {
                    router.push('/screens/devices/libre-connection');
                  }
                }}
              >
                <View style={styles.deviceInfo}>
                  <Ionicons 
                    name={
                      device.name === 'Apple Health' ? 'heart-outline' :
                      device.name === 'Fitbit' ? 'fitness-outline' :
                      device.name === 'FreeStyle Libre 2' ? 'pulse' : 'pulse-outline'
                    } 
                    size={24} 
                    color={device.connected ? Colors.primary : Colors.textSecondary} 
                  />
                  <Text 
                    style={[
                      styles.deviceName,
                      !device.connected && styles.deviceNameDisconnected
                    ]}
                  >
                    {device.name}
                  </Text>
                </View>
                <View 
                  style={[
                    styles.deviceStatus,
                    device.connected ? styles.deviceConnected : styles.deviceDisconnected
                  ]}
                >
                  <Text 
                    style={[
                      styles.deviceStatusText,
                      device.connected ? styles.deviceConnectedText : styles.deviceDisconnectedText
                    ]}
                  >
                    {device.connected ? 'Connected' : 'Disconnected'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
            
            <TouchableOpacity 
              style={styles.addItemButton}
              onPress={() => router.push('/onboarding/libre-onboarding')}
            >
              <Text style={styles.addItemText}>Connect New Device</Text>
            </TouchableOpacity>
          </View>
          
          {/* Data & Privacy Card */}
          <TouchableOpacity style={styles.card}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Ionicons name="shield-checkmark-outline" size={24} color={Colors.primary} />
                <View style={styles.settingTextContainer}>
                  <Text style={styles.settingTitle}>Data & Privacy</Text>
                  <Text style={styles.settingDescription}>Manage your data and privacy settings</Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
            </View>
          </TouchableOpacity>
          
          {/* Logout Button */}
          <TouchableOpacity style={styles.logoutButton}>
            <Ionicons name="log-out-outline" size={20} color={Colors.white} />
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
          
          <View style={styles.versionInfo}>
            <Text style={styles.versionText}>Preventely v1.0.0</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  editButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  profilePhotoContainer: {
    marginRight: 16,
  },
  profilePhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profilePhotoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  profileDetails: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  editProfileButton: {
    backgroundColor: `${Colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  editProfileText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  healthMetricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  healthMetricCard: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthMetricIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  healthMetricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  healthMetricLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginLeft: 8,
  },
  glucoseRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  glucoseRangeItem: {
    alignItems: 'center',
  },
  glucoseRangeValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: 4,
  },
  glucoseRangeLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  glucoseRangeDivider: {
    width: 1,
    height: 40,
    backgroundColor: Colors.border,
  },
  medicationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  medicationIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  medicationDetails: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  addItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  addItemText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  allergiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  allergyTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${Colors.error}15`,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  allergyText: {
    fontSize: 14,
    color: Colors.error,
    marginRight: 4,
  },
  allergyRemoveButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  doctorInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorAvatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  doctorContactContainer: {
    flexDirection: 'row',
  },
  doctorContactButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTextContainer: {
    marginLeft: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  languageToggle: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 16,
    overflow: 'hidden',
  },
  languageOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  languageOptionActive: {
    backgroundColor: Colors.primary,
  },
  languageOptionText: {
    fontSize: 14,
    color: Colors.textPrimary,
  },
  languageOptionTextActive: {
    color: Colors.white,
    fontWeight: '500',
  },
  deviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceName: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginLeft: 12,
  },
  deviceNameDisconnected: {
    color: Colors.textSecondary,
  },
  deviceStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  deviceConnected: {
    backgroundColor: `${Colors.success}15`,
  },
  deviceDisconnected: {
    backgroundColor: `${Colors.textSecondary}15`,
  },
  deviceStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  deviceConnectedText: {
    color: Colors.success,
  },
  deviceDisconnectedText: {
    color: Colors.textSecondary,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.error,
    borderRadius: 12,
    paddingVertical: 12,
    marginVertical: 16,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginLeft: 8,
  },
  versionInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  versionText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
});
