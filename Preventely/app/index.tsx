import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { View, ActivityIndicator, StyleSheet } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

export default function Index() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);

  useEffect(() => {
    // Check authentication status and onboarding completion
    const checkAuthStatus = async () => {
      try {
        const [token, onboardingCompleted] = await Promise.all([
          AsyncStorage.getItem('auth_token'),
          AsyncStorage.getItem('onboarding_completed')
        ]);

        setIsAuthenticated(!!token);
        setHasCompletedOnboarding(onboardingCompleted === 'true');
      } catch (error) {
        console.error('Error checking auth status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#6B5CE5" />
      </View>
    );
  }

  // Redirect based on authentication and onboarding status
  if (!hasCompletedOnboarding) {
    return <Redirect href="/onboarding" />;
  }

  if (!isAuthenticated) {
    // Using string literal to avoid TypeScript errors with route paths
    return <Redirect href="/login" />;  
  }

  // If authenticated and onboarding completed, go to home screen
  return <Redirect href="/home" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F1EFFF',
  },
});
