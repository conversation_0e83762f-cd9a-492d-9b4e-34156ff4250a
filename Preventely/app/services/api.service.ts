import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Base URL for API requests
// Use your computer's local IP address instead of localhost when testing on a physical device
// For iOS simulator or Android emulator, you can use special addresses
// const API_BASE_URL = Platform.select({
//   ios: 'http://localhost:3000/api',     // Works for iOS simulator
//   android: 'http://********:3000/api',  // Special IP for Android emulator
//   default: 'http://localhost:3000/api'  // Default to localhost for development
// });

const API_BASE_URL = 'http://localhost:3000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token in requests
apiClient.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// API service methods
export const apiService = {
  // Set auth token manually
  setAuthToken: async (token: string) => {
    await AsyncStorage.setItem('auth_token', token);
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    console.log('Auth token set in API service');
  },
  // Authentication
  login: async (email: string, password: string) => {
    const response = await apiClient.post('/auth/login', { email, password });
    
    // Store the token and user info
    if (response.data && response.data.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
      await AsyncStorage.setItem('user_id', response.data.id);
      await AsyncStorage.setItem('user_email', response.data.email);
    }
    
    return response.data;
  },
  
  register: async (name: string, email: string, password: string) => {
    const response = await apiClient.post('/auth/register', { name, email, password });
    
    // Store the token and user info
    if (response.data && response.data.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
      await AsyncStorage.setItem('user_id', response.data.id);
      await AsyncStorage.setItem('user_email', response.data.email);
    }
    
    return response.data;
  },
  
  logout: async () => {
    // Clear auth data
    await AsyncStorage.removeItem('auth_token');
    await AsyncStorage.removeItem('user_id');
    await AsyncStorage.removeItem('user_email');
  },
  
  isAuthenticated: async () => {
    const token = await AsyncStorage.getItem('auth_token');
    return !!token;
  },
  
  // User Profile
  getUserProfile: async () => {
    try {
      // Get the user ID from storage
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.get(`/profile/${userId}`);
      return response.data?.data || {};
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  },
  
  updateUserProfile: async (profileData: any) => {
    try {
      // Get the user ID from storage
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.put(`/profile/${userId}`, profileData);
      return response.data?.data || {};
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },
  
  // Medications
  getMedications: async () => {
    try {
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.get(`/profile/${userId}/medications`);
      return response.data?.data || [];
    } catch (error) {
      console.error('Error fetching medications:', error);
      return [];
    }
  },
  
  addMedication: async (medicationData: any) => {
    try {
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.post(`/profile/${userId}/medications`, medicationData);
      return response.data;
    } catch (error) {
      console.error('Error adding medication:', error);
      throw error;
    }
  },
  
  // Allergies
  getAllergies: async () => {
    try {
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.get(`/profile/${userId}/allergies`);
      return response.data?.data || [];
    } catch (error) {
      console.error('Error fetching allergies:', error);
      return [];
    }
  },
  
  addAllergy: async (allergyData: any) => {
    try {
      const userId = await AsyncStorage.getItem('user_id');
      if (!userId) {
        throw new Error('User ID not found in storage');
      }
      
      const response = await apiClient.post(`/profile/${userId}/allergies`, allergyData);
      return response.data;
    } catch (error) {
      console.error('Error adding allergy:', error);
      throw error;
    }
  },
  
  // CGM Data
  getGlucoseReadings: async (params?: { limit?: number, startDate?: string, endDate?: string }) => {
    try {
      const response = await apiClient.get('/cgm/readings', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching glucose readings:', error);
      return [];
    }
  },
  
  getLatestGlucoseReading: async () => {
    try {
      const response = await apiClient.get('/cgm/readings/latest');
      return response.data;
    } catch (error) {
      console.error('Error fetching latest glucose reading:', error);
      return null;
    }
  },
  
  getGlucoseStatistics: async (period: 'day' | 'week' | 'month' = 'day') => {
    try {
      const response = await apiClient.get(`/cgm/statistics?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching glucose statistics:', error);
      return {
        averageGlucose: 0,
        timeInRange: 0,
        timeAboveRange: 0,
        timeBelowRange: 0,
        standardDeviation: 0,
        minimumGlucose: 0,
        maximumGlucose: 0,
        readingsCount: 0
      };
    }
  },
  
  // Dexcom specific methods
  connectDexcom: async (code: string, redirectUri: string) => {
    try {
      const response = await apiClient.post('/cgm/authenticate', { code, redirectUri });
      return response.data;
    } catch (error) {
      console.error('Error connecting Dexcom:', error);
      throw error;
    }
  },
  
  getDexcomAuthUrl: async (redirectUri: string) => {
    try {
      const response = await apiClient.get(`/cgm/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`);
      return response.data.url;
    } catch (error) {
      console.error('Error getting Dexcom auth URL:', error);
      throw error;
    }
  },
  
  syncGlucoseReadings: async () => {
    try {
      const response = await apiClient.post('/cgm/sync');
      return response.data;
    } catch (error) {
      console.error('Error syncing glucose readings:', error);
      return { success: false, message: 'Failed to sync glucose readings' };
    }
  },
  
  // Food Scanning
  scanFoodByBarcode: async (barcode: string) => {
    try {
      const response = await apiClient.get(`/food-scan/barcode/${barcode}`);
      return response.data;
    } catch (error) {
      console.error('Error scanning food by barcode:', error);
      return { success: false, message: 'Food not found or error scanning' };
    }
  },
  
  searchFoods: async (query: string, page: number = 1, pageSize: number = 20) => {
    try {
      const response = await apiClient.get('/food-scan/search', {
        params: { query, page, pageSize }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching foods:', error);
      return { success: false, data: [], count: 0 };
    }
  },
  
  // Meals and Nutrition
  getDailyNutritionLog: async (date?: string) => {
    const params = date ? { date } : {};
    const response = await apiClient.get('/nutrition/daily-log', { params });
    return response.data;
  },
  
  getMeals: async (params?: { limit?: number, startDate?: string, endDate?: string }) => {
    const response = await apiClient.get('/meals', { params });
    return response.data;
  },
  
  getMealById: async (id: string) => {
    try {
      const response = await apiClient.get(`/meals/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching meal:', error);
      return { success: false, message: 'Failed to fetch meal' };
    }
  },
  
  createMeal: async (mealData: {
    name: string;
    date: string;
    time: string;
    mealItems: Array<{ foodId: string; quantity: number; servingSize?: string }>;
    notes?: string;
  }) => {
    try {
      const response = await apiClient.post('/meals', mealData);
      return response.data;
    } catch (error) {
      console.error('Error creating meal:', error);
      return { success: false, message: 'Failed to create meal' };
    }
  },
  
  updateMeal: async (id: string, mealData: any) => {
    try {
      const response = await apiClient.put(`/meals/${id}`, mealData);
      return response.data;
    } catch (error) {
      console.error('Error updating meal:', error);
      return { success: false, message: 'Failed to update meal' };
    }
  },
  
  deleteMeal: async (id: string) => {
    try {
      const response = await apiClient.delete(`/meals/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting meal:', error);
      return { success: false, message: 'Failed to delete meal' };
    }
  },
  
  // Reminders
  getReminders: async (date?: string) => {
    const params = date ? { date } : {};
    const response = await apiClient.get('/reminders', { params });
    return response.data;
  },
  
  createReminder: async (reminderData: {
    title: string;
    description?: string;
    date: string;
    time: string;
    isRecurring?: boolean;
    recurringPattern?: string;
    priority?: 'low' | 'medium' | 'high';
  }) => {
    try {
      const response = await apiClient.post('/reminders', reminderData);
      return response.data;
    } catch (error) {
      console.error('Error creating reminder:', error);
      return { success: false, message: 'Failed to create reminder' };
    }
  },
  
  updateReminder: async (id: string, data: any) => {
    const response = await apiClient.patch(`/reminders/${id}`, data);
    return response.data;
  },
  
  deleteReminder: async (id: string) => {
    try {
      const response = await apiClient.delete(`/reminders/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting reminder:', error);
      return { success: false, message: 'Failed to delete reminder' };
    }
  },
  
  // AI Assistant
  getAIResponse: async (message: string, includeGlucoseData: boolean = true) => {
    try {
      const response = await apiClient.post('/ai-assistant/message', { 
        message,
        includeGlucoseData
      });
      return response.data;
    } catch (error) {
      console.error('Error getting AI response:', error);
      return { 
        success: false, 
        message: 'Failed to get AI response',
        response: 'I apologize, but I encountered an error processing your request. Please try again later.'
      };
    }
  }
};

export default apiService;
