import { Stack } from "expo-router";
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import BottomTabBar from './components/navigation/BottomTabBar';
import { usePathname } from 'expo-router';
import AuthProvider from './contexts/AuthContext';

// Screens that should not show the bottom tab bar
const noTabBarScreens = [
  // Onboarding screens
  '/screens/onboarding/WelcomeScreen',
  '/screens/onboarding/UserInfoScreen',
  '/screens/onboarding/HealthGoalsScreen',
  '/screens/onboarding/NotificationsScreen',
  '/screens/onboarding/LoginScreen',
  '/screens/onboarding/SignupScreen',
  '/screens/onboarding/MedicalQuizScreen',
  '/screens/onboarding/ProfileSetupScreen',
  '/onboarding',
  '/onboarding/login',
  '/onboarding/signup',
  '/onboarding/medical-quiz',
  '/onboarding/profile-setup',
  // Auth screens
  '/login',
  '/register',
  '/forgot-password'
];

export default function RootLayout() {
  const pathname = usePathname();
  const showTabBar = !noTabBarScreens.some(screen => pathname === screen || pathname.startsWith(screen + '/'));
  
  return (
    <AuthProvider>
      <StatusBar style="dark" />
      <View style={styles.container}>
        <View style={styles.content}>
          <Stack screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: '#F8F9FA' },
            animation: 'slide_from_right',
          }} />
        </View>
        {showTabBar && <BottomTabBar />}
      </View>
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  }
});
