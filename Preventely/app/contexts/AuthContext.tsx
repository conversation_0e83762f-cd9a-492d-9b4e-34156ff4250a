import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter, useSegments } from 'expo-router';
import apiService from '../services/api.service';

// Define the shape of the auth context
interface AuthContextType {
  user: any | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

// Create the auth context
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  login: async () => false,
  logout: async () => {},
  isAuthenticated: false,
});

// Auth provider component
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const segments = useSegments();

  // Check if user is authenticated and redirect accordingly
  useEffect(() => {
    if (!isLoading) {
      const inAuthGroup = segments[0] === 'onboarding';
      
      if (!user && !inAuthGroup) {
        // Redirect to login if not authenticated and not in auth group
        router.replace('/onboarding');
      } else if (user && inAuthGroup) {
        // Redirect to home if authenticated and in auth group
        router.replace('/dashboard');
      }
    }
  }, [user, segments, isLoading]);

  // Load user from storage on app start
  useEffect(() => {
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem('user');
        const token = await AsyncStorage.getItem('auth_token');
        
        if (userJson && token) {
          // Set the token in the API service headers
          apiService.setAuthToken(token);
          setUser(JSON.parse(userJson));
          console.log('User authenticated from storage');
        } else {
          console.log('No valid auth data found in storage');
        }
      } catch (error) {
        console.error('Failed to load user from storage:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadUser();
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.login(email, password);
      
      if (response && response.token) {
        // Store user data in state
        const userData = {
          id: response.id,
          email: response.email,
          name: response.name,
        };
        
        // Set the token in the API service headers
        await apiService.setAuthToken(response.token);
        
        setUser(userData);
        
        // Store user data in AsyncStorage
        await AsyncStorage.setItem('user', JSON.stringify(userData));
        
        console.log('Login successful, user authenticated');
        return true;
      }
      console.log('Login failed: Invalid response from server');
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Clear user from state
      setUser(null);
      
      // Clear storage
      await AsyncStorage.removeItem('user');
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_id');
      await AsyncStorage.removeItem('user_email');
      
      // Redirect to login
      router.replace('/onboarding');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider 
      value={{ 
        user, 
        isLoading, 
        login, 
        logout,
        isAuthenticated: !!user 
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth() {
  return useContext(AuthContext);
}

// Default export for the AuthProvider
export default AuthProvider;
