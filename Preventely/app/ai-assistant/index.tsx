import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/colors';

// Define message types
type MessageType = 'user' | 'assistant' | 'suggestion';

// Define message interface
interface Message {
  id: string;
  text: string;
  type: MessageType;
  timestamp: Date;
}

// Define suggestion interface
interface Suggestion {
  id: string;
  text: string;
}

// Dummy user profile data
const userProfile = {
  name: '<PERSON>',
  age: 42,
  weight: 185, // in lbs
  height: 5.9, // in feet
  conditions: ['Prediabetes', 'Hypertension'],
  medications: ['Metformin'],
  recentGlucose: 115, // mg/dL
  averageGlucose: 118, // mg/dL
  recentMeals: [
    { name: 'Oatmeal with berries', gi: 55, gl: 8 },
    { name: 'Grilled chicken salad', gi: 35, gl: 5 },
    { name: 'Apple with almonds', gi: 40, gl: 6 },
  ],
};

// Initial suggestions based on user profile
const initialSuggestions: Suggestion[] = [
  { id: '1', text: 'What foods can help lower my blood sugar?' },
  { id: '2', text: 'How can I improve my sleep quality?' },
  { id: '3', text: 'What exercises are best for prediabetes?' },
  { id: '4', text: 'Tell me about my glucose trends' },
  { id: '5', text: 'Suggest a low-GI breakfast' },
];

// Predefined responses for common questions
const predefinedResponses: Record<string, string[]> = {
  'blood sugar': [
    "Based on your recent glucose readings (average: 118 mg/dL), I recommend incorporating more fiber-rich foods like beans, lentils, and non-starchy vegetables. These foods have minimal impact on blood sugar levels.",
    "Consider adding cinnamon to your meals, as some studies suggest it may help improve insulin sensitivity. A teaspoon a day in your oatmeal or coffee might be beneficial.",
    "Your recent meal log shows good choices like the grilled chicken salad (GI: 35). Keep prioritizing low-GI foods under 55."
  ],
  'sleep': [
    "Quality sleep is crucial for blood sugar management. Aim for 7-8 hours per night and try to maintain a consistent sleep schedule.",
    "Consider limiting screen time 1-2 hours before bed, as blue light can interfere with melatonin production.",
    "Based on your profile, I'd suggest avoiding caffeine after 2 PM and creating a relaxing bedtime routine."
  ],
  'exercise': [
    "For prediabetes management, a combination of aerobic exercise and resistance training is ideal. Aim for 150 minutes of moderate activity per week.",
    "Walking after meals, even for just 10-15 minutes, can significantly help manage post-meal blood sugar spikes.",
    "Given your health profile, activities like swimming, cycling, or brisk walking would be excellent choices to improve insulin sensitivity."
  ],
  'glucose': [
    "Your recent glucose average of 118 mg/dL is above the ideal range of 70-99 mg/dL for fasting glucose, but you're making progress.",
    "I notice your glucose tends to be higher in the mornings, which could be due to the dawn phenomenon - a normal rise in blood sugar caused by morning hormones.",
    "Your glucose readings after the grilled chicken salad were notably better than after other meals. Consider incorporating similar low-GI meals more frequently."
  ],
  'breakfast': [
    "Greek yogurt with berries and a sprinkle of nuts would be an excellent low-GI breakfast option. The protein helps slow digestion and minimizes blood sugar spikes.",
    "A vegetable omelet with a small portion of whole grain toast provides a good balance of protein and complex carbs.",
    "Overnight oats made with steel-cut oats, chia seeds, and unsweetened almond milk would be a convenient, blood-sugar friendly option."
  ],
};

export default function AIAssistantScreen() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '0',
      text: "Hello Alex! I'm your Preventely AI Assistant. I can help you with personalized health and nutrition guidance for managing prediabetes. How can I assist you today?",
      type: 'assistant',
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>(initialSuggestions);
  const scrollViewRef = useRef<ScrollView>(null);

  // Function to handle sending a message
  const handleSendMessage = (text: string) => {
    if (!text.trim()) return;
    
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: text,
      type: 'user',
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    
    // Generate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(text);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        type: 'assistant',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, aiMessage]);
      
      // Update suggestions based on the conversation
      updateSuggestions(text);
      
      // Scroll to bottom
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 1000);
  };

  // Function to generate AI response based on user input
  const generateAIResponse = (userInput: string): string => {
    const userInputLower = userInput.toLowerCase();
    
    // Check for matches with predefined responses
    for (const [keyword, responses] of Object.entries(predefinedResponses)) {
      if (userInputLower.includes(keyword)) {
        // Return a random response from the matching category
        return responses[Math.floor(Math.random() * responses.length)];
      }
    }
    
    // Default responses if no keyword match
    const defaultResponses = [
      `Based on your recent glucose readings (${userProfile.recentGlucose} mg/dL) and health profile, I recommend focusing on low-GI foods and regular physical activity.`,
      "I notice you've been logging meals consistently. Great job maintaining this healthy habit!",
      "Your recent food choices show good awareness of glycemic impact. Continue prioritizing foods with a GI under 55 when possible.",
      "Remember that stress management is also important for blood sugar control. Consider incorporating mindfulness practices into your daily routine."
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  };

  // Function to update suggestions based on conversation
  const updateSuggestions = (userInput: string) => {
    const userInputLower = userInput.toLowerCase();
    
    // Contextual suggestions based on user's query
    if (userInputLower.includes('food') || userInputLower.includes('eat') || userInputLower.includes('meal')) {
      setSuggestions([
        { id: '1', text: 'What should I eat before exercise?' },
        { id: '2', text: 'Are there fruits I should avoid?' },
        { id: '3', text: 'How can I reduce carb cravings?' },
      ]);
    } else if (userInputLower.includes('exercise') || userInputLower.includes('workout') || userInputLower.includes('activity')) {
      setSuggestions([
        { id: '1', text: 'How long should I exercise each day?' },
        { id: '2', text: 'Is walking enough exercise?' },
        { id: '3', text: 'Best time of day to exercise for blood sugar?' },
      ]);
    } else if (userInputLower.includes('glucose') || userInputLower.includes('sugar') || userInputLower.includes('blood')) {
      setSuggestions([
        { id: '1', text: 'Why is my morning glucose higher?' },
        { id: '2', text: 'How often should I check my glucose?' },
        { id: '3', text: "What's a dangerous glucose level?" },
      ]);
    } else {
      // Default suggestions if no context is detected
      setSuggestions([
        { id: '1', text: 'How can I improve my sleep?' },
        { id: '2', text: 'Tips for eating at restaurants' },
        { id: '3', text: 'How to stay motivated with my health goals' },
      ]);
    }
  };

  // Function to handle suggestion tap
  const handleSuggestionTap = (suggestion: Suggestion) => {
    handleSendMessage(suggestion.text);
  };

  // Render message item
  const renderMessage = (message: Message) => {
    if (message.type === 'user') {
      return (
        <View key={message.id} style={styles.userMessageContainer}>
          <View style={styles.userMessage}>
            <Text style={styles.userMessageText}>{message.text}</Text>
          </View>
        </View>
      );
    } else {
      return (
        <View key={message.id} style={styles.assistantMessageContainer}>
          <View style={styles.assistantIconContainer}>
            <Ionicons name="medkit" size={16} color={Colors.white} />
          </View>
          <View style={styles.assistantMessage}>
            <Text style={styles.assistantMessageText}>{message.text}</Text>
          </View>
        </View>
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Health Assistant</Text>
        <TouchableOpacity style={styles.infoButton}>
          <Ionicons name="information-circle-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.healthSummary}>
        <Text style={styles.healthSummaryTitle}>Today's Health Summary</Text>
        <View style={styles.healthMetrics}>
          <View style={styles.metric}>
            <Text style={styles.metricValue}>{userProfile.recentGlucose}</Text>
            <Text style={styles.metricLabel}>Glucose</Text>
          </View>
          <View style={styles.metricDivider} />
          <View style={styles.metric}>
            <Text style={styles.metricValue}>45</Text>
            <Text style={styles.metricLabel}>Avg GI</Text>
          </View>
          <View style={styles.metricDivider} />
          <View style={styles.metric}>
            <Text style={styles.metricValue}>{userProfile.weight}</Text>
            <Text style={styles.metricLabel}>Weight</Text>
          </View>
        </View>
      </View>
      
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.chatContainer}
        keyboardVerticalOffset={100}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {messages.map(message => renderMessage(message))}
        </ScrollView>
        
        <View style={styles.suggestionsContainer}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.suggestionsContent}
          >
            {suggestions.map(suggestion => (
              <TouchableOpacity
                key={suggestion.id}
                style={styles.suggestionButton}
                onPress={() => handleSuggestionTap(suggestion)}
              >
                <Text style={styles.suggestionText}>{suggestion.text}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Ask me anything about your health..."
            value={inputText}
            onChangeText={setInputText}
            multiline
          />
          <TouchableOpacity 
            style={styles.sendButton}
            onPress={() => handleSendMessage(inputText)}
            disabled={!inputText.trim()}
          >
            <Ionicons 
              name="send" 
              size={20} 
              color={inputText.trim() ? Colors.white : Colors.lightGray} 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  infoButton: {
    padding: 4,
  },
  healthSummary: {
    backgroundColor: Colors.white,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  healthMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metric: {
    flex: 1,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  metricDivider: {
    width: 1,
    height: 24,
    backgroundColor: Colors.border,
  },
  chatContainer: {
    flex: 1,
    marginTop: 16,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messagesContent: {
    paddingBottom: 16,
  },
  userMessageContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginVertical: 8,
  },
  userMessage: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    borderBottomRightRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxWidth: '80%',
  },
  userMessageText: {
    color: Colors.white,
    fontSize: 16,
  },
  assistantMessageContainer: {
    flexDirection: 'row',
    marginVertical: 8,
    alignItems: 'flex-end',
  },
  assistantIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  assistantMessage: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxWidth: '80%',
  },
  assistantMessageText: {
    color: Colors.textPrimary,
    fontSize: 16,
  },
  suggestionsContainer: {
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  suggestionsContent: {
    paddingHorizontal: 16,
  },
  suggestionButton: {
    backgroundColor: Colors.lightBackground,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionText: {
    color: Colors.textPrimary,
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.white,
  },
  input: {
    flex: 1,
    backgroundColor: Colors.lightBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});
