import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';

export default function DevicesRedirect() {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the devices list screen
    router.replace('/screens/devices/libre-connection');
  }, []);
  
  return <View style={styles.container} />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
