import React from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity } from 'react-native';
import Colors from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';
import PatientProfileCard from '../components/PatientProfileCard';
import CheckupScheduleCard from '../components/CheckupScheduleCard';
import Card from '../components/Card';
import Badge from '../components/Badge';

// Dummy data
const patient = {
  name: '<PERSON>',
  gender: 'Female',
  age: 23,
  height: '5\'4"',
  image: null, // Placeholder for patient image
  treatmentPlans: 3,
};

const checkupSchedule = [
  {
    id: '1',
    date: { month: 'Sep', day: '07' },
    type: 'Clinic Visit Appointment',
    doctor: { name: '<PERSON>' },
  },
  {
    id: '2',
    date: { month: 'Sep', day: '15' },
    type: 'Video Consulting',
    doctor: { name: '<PERSON><PERSON><PERSON><PERSON>' },
  },
];

const recommendedDoctors = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    specialty: 'Endocrinologist',
    experience: '8 years experience',
    image: null, // Placeholder for doctor image
  },
  {
    id: '2',
    name: 'Dr. <PERSON>',
    specialty: 'Nutritionist',
    experience: '6 years experience',
    image: null, // Placeholder for doctor image
  },
];

const treatmentPlans = [
  {
    id: '1',
    title: 'Glucose Monitoring',
    description: 'Check blood sugar levels 3 times daily',
    progress: 75,
    color: Colors.primary,
  },
  {
    id: '2',
    title: 'Diet Plan',
    description: 'Low-carb, high-protein meal plan',
    progress: 60,
    color: Colors.secondary,
  },
  {
    id: '3',
    title: 'Physical Activity',
    description: '30 minutes of moderate exercise daily',
    progress: 40,
    color: Colors.tertiary,
  },
];

const TreatmentScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity>
          <Ionicons name="chevron-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Treatment</Text>
        <TouchableOpacity>
          <Ionicons name="ellipsis-vertical" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <PatientProfileCard patient={patient} />

          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>My Checkup Schedule</Text>
              <Text style={styles.seeAll}>See All</Text>
            </View>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.scheduleScrollView}
            >
              {checkupSchedule.map((checkup) => (
                <CheckupScheduleCard
                  key={checkup.id}
                  date={checkup.date}
                  type={checkup.type}
                  doctor={checkup.doctor}
                />
              ))}
            </ScrollView>
          </View>

          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Treatment Plans</Text>
              <Badge label="Active" color={Colors.success} />
            </View>
            {treatmentPlans.map((plan) => (
              <Card key={plan.id} style={styles.planCard}>
                <View style={styles.planHeader}>
                  <Text style={styles.planTitle}>{plan.title}</Text>
                  <TouchableOpacity>
                    <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
                  </TouchableOpacity>
                </View>
                <Text style={styles.planDescription}>{plan.description}</Text>
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View 
                      style={[
                        styles.progressFill, 
                        { width: `${plan.progress}%`, backgroundColor: plan.color }
                      ]} 
                    />
                  </View>
                  <Text style={styles.progressText}>{plan.progress}%</Text>
                </View>
              </Card>
            ))}
          </View>

          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recommend Doctor</Text>
              <Text style={styles.seeAll}>See All</Text>
            </View>
            {recommendedDoctors.map((doctor) => (
              <Card key={doctor.id} style={styles.doctorCard}>
                <View style={styles.doctorContent}>
                  <View style={styles.doctorImageContainer}>
                    <View style={styles.doctorImage} />
                  </View>
                  <View style={styles.doctorInfo}>
                    <Text style={styles.doctorName}>{doctor.name}</Text>
                    <Text style={styles.doctorSpecialty}>{doctor.specialty}</Text>
                    <Text style={styles.doctorExperience}>{doctor.experience}</Text>
                  </View>
                  <TouchableOpacity style={styles.visitButton}>
                    <Text style={styles.visitButtonText}>Visit Now</Text>
                  </TouchableOpacity>
                </View>
              </Card>
            ))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.tabBar}>
        <View style={styles.tabItem}>
          <Ionicons name="home-outline" size={24} color={Colors.textLight} />
          <Text style={styles.tabText}>Home</Text>
        </View>
        <View style={styles.tabItem}>
          <Ionicons name="calendar-outline" size={24} color={Colors.textLight} />
          <Text style={styles.tabText}>Calendar</Text>
        </View>
        <View style={[styles.tabItem, styles.activeTab]}>
          <Ionicons name="medkit" size={24} color={Colors.primary} />
          <Text style={styles.activeTabText}>Treat</Text>
        </View>
        <View style={styles.tabItem}>
          <Ionicons name="person-outline" size={24} color={Colors.textLight} />
          <Text style={styles.tabText}>Profile</Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  content: {
    padding: 16,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  seeAll: {
    fontSize: 14,
    color: Colors.primary,
  },
  scheduleScrollView: {
    paddingRight: 8,
  },
  planCard: {
    marginBottom: 12,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  planDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
    width: 40,
    textAlign: 'right',
  },
  doctorCard: {
    marginBottom: 12,
  },
  doctorContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorImageContainer: {
    marginRight: 12,
  },
  doctorImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.border,
  },
  doctorInfo: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  doctorExperience: {
    fontSize: 12,
    color: Colors.textLight,
  },
  visitButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  visitButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
  },
  activeTab: {
    borderTopWidth: 2,
    borderTopColor: Colors.primary,
    marginTop: -14,
    paddingTop: 12,
  },
  tabText: {
    fontSize: 12,
    color: Colors.textLight,
    marginTop: 4,
  },
  activeTabText: {
    fontSize: 12,
    color: Colors.primary,
    marginTop: 4,
    fontWeight: '500',
  },
});


export default TreatmentScreen;
