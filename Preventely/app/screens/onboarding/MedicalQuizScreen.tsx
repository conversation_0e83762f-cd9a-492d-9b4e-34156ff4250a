import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  SafeAreaView, 
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/colors';

// Questions inspired by principles from "Sucre: L'ennemi public n°1"
const quizQuestions = [
  {
    id: 'q1',
    question: 'How often do you consume sugary drinks (sodas, fruit juices, sweetened teas)?',
    options: [
      { id: 'a', text: 'Daily', value: 3 },
      { id: 'b', text: '2-3 times a week', value: 2 },
      { id: 'c', text: 'Occasionally', value: 1 },
      { id: 'd', text: 'Rarely or never', value: 0 },
    ],
  },
  {
    id: 'q2',
    question: 'How would you describe your consumption of processed foods?',
    options: [
      { id: 'a', text: 'Most of my diet consists of processed foods', value: 3 },
      { id: 'b', text: 'I eat processed foods regularly', value: 2 },
      { id: 'c', text: 'I occasionally eat processed foods', value: 1 },
      { id: 'd', text: 'I rarely eat processed foods', value: 0 },
    ],
  },
  {
    id: 'q3',
    question: 'Do you have a family history of diabetes?',
    options: [
      { id: 'a', text: 'Yes, first-degree relative (parent, sibling)', value: 3 },
      { id: 'b', text: 'Yes, second-degree relative (grandparent, aunt, uncle)', value: 2 },
      { id: 'c', text: 'No known family history', value: 0 },
      { id: 'd', text: 'Unsure', value: 1 },
    ],
  },
  {
    id: 'q4',
    question: 'How would you describe your physical activity level?',
    options: [
      { id: 'a', text: 'Sedentary (little to no exercise)', value: 3 },
      { id: 'b', text: 'Light activity (1-2 days per week)', value: 2 },
      { id: 'c', text: 'Moderate activity (3-5 days per week)', value: 1 },
      { id: 'd', text: 'Very active (6-7 days per week)', value: 0 },
    ],
  },
  {
    id: 'q5',
    question: 'How would you rate your stress levels on a typical day?',
    options: [
      { id: 'a', text: 'Very high', value: 3 },
      { id: 'b', text: 'High', value: 2 },
      { id: 'c', text: 'Moderate', value: 1 },
      { id: 'd', text: 'Low', value: 0 },
    ],
  },
  {
    id: 'q6',
    question: 'How many hours of sleep do you typically get per night?',
    options: [
      { id: 'a', text: 'Less than 5 hours', value: 3 },
      { id: 'b', text: '5-6 hours', value: 2 },
      { id: 'c', text: '7-8 hours', value: 0 },
      { id: 'd', text: 'More than 8 hours', value: 1 },
    ],
  },
  {
    id: 'q7',
    question: 'Have you ever been told you have high blood pressure?',
    options: [
      { id: 'a', text: 'Yes, currently taking medication', value: 3 },
      { id: 'b', text: 'Yes, but not taking medication', value: 2 },
      { id: 'c', text: 'Borderline high', value: 1 },
      { id: 'd', text: 'No', value: 0 },
    ],
  },
  {
    id: 'q8',
    question: 'How often do you eat fruits and vegetables?',
    options: [
      { id: 'a', text: 'Rarely or never', value: 3 },
      { id: 'b', text: 'A few times a week', value: 2 },
      { id: 'c', text: 'Once daily', value: 1 },
      { id: 'd', text: 'Multiple times daily', value: 0 },
    ],
  },
  {
    id: 'q9',
    question: 'Have you noticed any of these symptoms recently? (Select all that apply)',
    options: [
      { id: 'a', text: 'Increased thirst', value: 1 },
      { id: 'b', text: 'Frequent urination', value: 1 },
      { id: 'c', text: 'Unexplained fatigue', value: 1 },
      { id: 'd', text: 'None of the above', value: 0 },
    ],
    multiSelect: true,
  },
  {
    id: 'q10',
    question: 'When was your last blood glucose test?',
    options: [
      { id: 'a', text: 'Within the last year', value: 0 },
      { id: 'b', text: '1-3 years ago', value: 1 },
      { id: 'c', text: 'More than 3 years ago', value: 2 },
      { id: 'd', text: 'Never had one / Don\'t know', value: 3 },
    ],
  },
];

// Risk profiles based on quiz results
const riskProfiles = [
  {
    id: 'low-risk',
    name: 'Low Risk',
    description: 'Your responses indicate a lower risk for prediabetes. We\'ll help you maintain your healthy habits.',
    scoreRange: [0, 10],
    color: Colors.success,
    icon: 'shield-checkmark-outline',
  },
  {
    id: 'moderate-risk',
    name: 'Moderate Risk',
    description: 'Your responses suggest some risk factors for prediabetes. We\'ll help you address these areas.',
    scoreRange: [11, 20],
    color: Colors.warning,
    icon: 'alert-circle-outline',
  },
  {
    id: 'high-risk',
    name: 'High Risk',
    description: 'Your responses indicate several risk factors for prediabetes. We\'ll create a comprehensive plan to help you reduce your risk.',
    scoreRange: [21, 30],
    color: Colors.error,
    icon: 'warning-outline',
  },
];

const MedicalQuizScreen = () => {
  const router = useRouter();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [multiSelectAnswers, setMultiSelectAnswers] = useState<Record<string, string[]>>({});
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [riskProfile, setRiskProfile] = useState<any>(null);

  const currentQuestion = quizQuestions[currentQuestionIndex];
  const isMultiSelect = currentQuestion?.multiSelect || false;

  const handleAnswer = (optionId: string) => {
    if (isMultiSelect) {
      const questionId = currentQuestion.id;
      const currentSelections = multiSelectAnswers[questionId] || [];
      
      // Toggle selection
      if (currentSelections.includes(optionId)) {
        setMultiSelectAnswers({
          ...multiSelectAnswers,
          [questionId]: currentSelections.filter(id => id !== optionId)
        });
      } else {
        setMultiSelectAnswers({
          ...multiSelectAnswers,
          [questionId]: [...currentSelections, optionId]
        });
      }
    } else {
      setAnswers({
        ...answers,
        [currentQuestion.id]: optionId
      });

      // Auto-advance to next question for single select
      if (currentQuestionIndex < quizQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      } else {
        completeQuiz();
      }
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < quizQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      completeQuiz();
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    } else {
      router.back();
    }
  };

  const isNextEnabled = () => {
    if (isMultiSelect) {
      const selections = multiSelectAnswers[currentQuestion.id] || [];
      return selections.length > 0;
    }
    return answers[currentQuestion.id] !== undefined;
  };

  const isOptionSelected = (optionId: string) => {
    if (isMultiSelect) {
      const selections = multiSelectAnswers[currentQuestion.id] || [];
      return selections.includes(optionId);
    }
    return answers[currentQuestion.id] === optionId;
  };

  const calculateScore = () => {
    let totalScore = 0;
    
    // Calculate score from single select questions
    Object.keys(answers).forEach(questionId => {
      const question = quizQuestions.find(q => q.id === questionId);
      if (question) {
        const selectedOption = question.options.find(o => o.id === answers[questionId]);
        if (selectedOption) {
          totalScore += selectedOption.value;
        }
      }
    });
    
    // Calculate score from multi-select questions
    Object.keys(multiSelectAnswers).forEach(questionId => {
      const question = quizQuestions.find(q => q.id === questionId);
      if (question) {
        multiSelectAnswers[questionId].forEach(optionId => {
          if (optionId !== 'd') { // Skip "None of the above" option
            const selectedOption = question.options.find(o => o.id === optionId);
            if (selectedOption) {
              totalScore += selectedOption.value;
            }
          }
        });
      }
    });
    
    return totalScore;
  };

  const determineRiskProfile = (score: number) => {
    return riskProfiles.find(profile => 
      score >= profile.scoreRange[0] && score <= profile.scoreRange[1]
    );
  };

  const completeQuiz = () => {
    setLoading(true);
    
    // Simulate API call for processing results
    setTimeout(() => {
      const score = calculateScore();
      const profile = determineRiskProfile(score);
      
      setRiskProfile(profile);
      setQuizCompleted(true);
      setLoading(false);
    }, 1500);
  };

  const handleFinish = () => {
    // Navigate to home screen
    router.replace('/');
  };

  const handleConnectLibre = () => {
    // Navigate to Libre onboarding screen
    router.push('/onboarding/libre-onboarding');
  };

  return (
    <SafeAreaView style={styles.container}>
      {!quizCompleted ? (
        <>
          <View style={styles.header}>
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={Colors.text} />
            </TouchableOpacity>
            <Text style={styles.title}>Health Assessment</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${((currentQuestionIndex + 1) / quizQuestions.length) * 100}%` }
                ]} 
              />
            </View>
            <Text style={styles.progressText}>
              Question {currentQuestionIndex + 1} of {quizQuestions.length}
            </Text>
          </View>

          <ScrollView 
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.questionContainer}>
              <Text style={styles.questionText}>{currentQuestion.question}</Text>
              
              <View style={styles.optionsContainer}>
                {currentQuestion.options.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionButton,
                      isOptionSelected(option.id) && styles.selectedOption
                    ]}
                    onPress={() => handleAnswer(option.id)}
                  >
                    {isMultiSelect ? (
                      <View style={styles.checkboxContainer}>
                        <View 
                          style={[
                            styles.checkbox,
                            isOptionSelected(option.id) && styles.checkboxSelected
                          ]}
                        >
                          {isOptionSelected(option.id) && (
                            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                          )}
                        </View>
                      </View>
                    ) : (
                      <View style={styles.radioContainer}>
                        <View 
                          style={[
                            styles.radio,
                            isOptionSelected(option.id) && styles.radioSelected
                          ]}
                        >
                          {isOptionSelected(option.id) && (
                            <View style={styles.radioInner} />
                          )}
                        </View>
                      </View>
                    )}
                    <Text 
                      style={[
                        styles.optionText,
                        isOptionSelected(option.id) && styles.selectedOptionText
                      ]}
                    >
                      {option.text}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            {isMultiSelect && (
              <TouchableOpacity 
                style={[
                  styles.button, 
                  !isNextEnabled() && styles.disabledButton
                ]} 
                onPress={handleNext}
                disabled={!isNextEnabled()}
              >
                <Text style={styles.buttonText}>
                  {currentQuestionIndex < quizQuestions.length - 1 ? 'Next' : 'Complete'}
                </Text>
                <Ionicons name="arrow-forward" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </View>
        </>
      ) : (
        <View style={styles.resultsContainer}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Analyzing your responses...</Text>
            </View>
          ) : (
            <>
              <View style={styles.resultsHeader}>
                <Text style={styles.resultsTitle}>Your Results</Text>
                <Text style={styles.resultsSubtitle}>
                  Based on your responses, we've created a personalized assessment
                </Text>
              </View>

              <View 
                style={[
                  styles.profileCard, 
                  { borderColor: riskProfile?.color || Colors.primary }
                ]}
              >
                <View 
                  style={[
                    styles.profileIconContainer,
                    { backgroundColor: riskProfile?.color || Colors.primary }
                  ]}
                >
                  <Ionicons 
                    name={riskProfile?.icon || "analytics"} 
                    size={40} 
                    color="#FFFFFF" 
                  />
                </View>
                <Text 
                  style={[
                    styles.profileName,
                    { color: riskProfile?.color || Colors.primary }
                  ]}
                >
                  {riskProfile?.name || "Assessment Complete"}
                </Text>
                <Text style={styles.profileDescription}>
                  {riskProfile?.description || "Thank you for completing the assessment."}
                </Text>
              </View>

              <View style={styles.recommendationsContainer}>
                <Text style={styles.recommendationsTitle}>What's Next?</Text>
                
                <View style={styles.recommendationItem}>
                  <View style={styles.recommendationIcon}>
                    <Ionicons name="calendar-outline" size={24} color={Colors.primary} />
                  </View>
                  <View style={styles.recommendationContent}>
                    <Text style={styles.recommendationTitle}>
                      Personalized Plan
                    </Text>
                    <Text style={styles.recommendationDescription}>
                      We've created a customized plan based on your risk factors
                    </Text>
                  </View>
                </View>

                <View style={styles.recommendationItem}>
                  <View style={styles.recommendationIcon}>
                    <Ionicons name="nutrition-outline" size={24} color={Colors.secondary} />
                  </View>
                  <View style={styles.recommendationContent}>
                    <Text style={styles.recommendationTitle}>
                      Nutrition Guidance
                    </Text>
                    <Text style={styles.recommendationDescription}>
                      Specific dietary recommendations to help manage your risk
                    </Text>
                  </View>
                </View>

                <View style={styles.recommendationItem}>
                  <View style={styles.recommendationIcon}>
                    <Ionicons name="fitness-outline" size={24} color={Colors.tertiary} />
                  </View>
                  <View style={styles.recommendationContent}>
                    <Text style={styles.recommendationTitle}>
                      Activity Suggestions
                    </Text>
                    <Text style={styles.recommendationDescription}>
                      Exercise recommendations tailored to your current activity level
                    </Text>
                  </View>
                </View>
                
                <View style={styles.recommendationItem}>
                  <View style={styles.recommendationIcon}>
                    <Ionicons name="pulse" size={24} color={Colors.error} />
                  </View>
                  <View style={styles.recommendationContent}>
                    <Text style={styles.recommendationTitle}>
                      Glucose Monitoring
                    </Text>
                    <Text style={styles.recommendationDescription}>
                      Connect a continuous glucose monitor for real-time tracking
                    </Text>
                  </View>
                  <TouchableOpacity 
                    style={styles.recommendationButton}
                    onPress={handleConnectLibre}
                  >
                    <Text style={styles.recommendationButtonText}>Connect</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity 
                style={styles.finishButton} 
                onPress={handleFinish}
              >
                <Text style={styles.finishButtonText}>Continue to Dashboard</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'right',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  questionContainer: {
    flex: 1,
  },
  questionText: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 32,
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  selectedOption: {
    backgroundColor: `${Colors.primary}15`,
    borderColor: Colors.primary,
  },
  radioContainer: {
    marginRight: 16,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.textLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: Colors.primary,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary,
  },
  checkboxContainer: {
    marginRight: 16,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.textLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  selectedOptionText: {
    color: Colors.text,
    fontWeight: '500',
  },
  footer: {
    padding: 24,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.textLight,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  resultsContainer: {
    flex: 1,
    padding: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  resultsHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  resultsTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  resultsSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  profileCard: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 32,
    borderWidth: 2,
  },
  profileIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileName: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
  },
  profileDescription: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  recommendationsContainer: {
    marginBottom: 32,
  },
  recommendationsTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 16,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  recommendationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(106, 90, 205, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  recommendationDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  finishButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  finishButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  recommendationButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginLeft: 8,
  },
  recommendationButtonText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
});


export default MedicalQuizScreen;
