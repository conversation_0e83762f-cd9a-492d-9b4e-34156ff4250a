import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import { SafeAreaView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Colors from '../../constants/colors';

const WelcomeScreen = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/onboarding/signup');
  };

  const handleLogin = () => {
    // Navigate to login screen
    router.push('/onboarding/login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Ionicons name="medkit" size={50} color={Colors.primary} />
          </View>
          <Text style={styles.appName}>Prevently</Text>
          <Text style={styles.tagline}>Your partner in diabetes prevention</Text>
        </View>

        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>Key Features</Text>
          
          <View style={styles.featuresGrid}>
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#6366F1' }]}>
                <Ionicons name="shield-checkmark-outline" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>Early Detection</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#FFCC33' }]}>
                <Ionicons name="nutrition-outline" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>Personalized Plans</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#6495ED' }]}>
                <Ionicons name="fitness-outline" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>Track Progress</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#38B000' }]}>
                <Ionicons name="restaurant-outline" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>Glycemic Tracking</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#FF6B6B' }]}>
                <Ionicons name="pulse" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>AI Assistant</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.featureCard}>
              <View style={[styles.featureIconContainer, { backgroundColor: '#3498DB' }]}>
                <Ionicons name="analytics-outline" size={26} color="#FFFFFF" />
              </View>
              <Text style={styles.featureLabel}>Smart Reports</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.button} onPress={handleGetStarted}>
            <View style={styles.buttonContent}>
              <Text style={styles.buttonText}>Get Started</Text>
              <View style={styles.buttonIconContainer}>
                <Ionicons name="arrow-forward" size={20} color={Colors.white} />
              </View>
            </View>
          </TouchableOpacity>
          
          <View style={styles.loginContainer}>
            <View style={styles.loginDivider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>
            
            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Ionicons name="log-in-outline" size={20} color={Colors.primary} style={styles.loginIcon} />
              <Text style={styles.loginButtonText}>Log In to Your Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  logoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 12,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: 4,
  },
  tagline: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 16,
  },
  featuresContainer: {
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureCard: {
    width: '30%',
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 4,
  },
  featureIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
  },
  footer: {
    marginBottom: 16,
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    overflow: 'hidden',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  buttonText: {
    color: Colors.white,
    fontSize: 18,
    fontWeight: '700',
    marginRight: 12,
    letterSpacing: 0.5,
  },
  buttonIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginContainer: {
    marginTop: 8,
  },
  loginDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: Colors.textSecondary,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    borderRadius: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  loginIcon: {
    marginRight: 8,
  },
  loginButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },

});

export default WelcomeScreen;
