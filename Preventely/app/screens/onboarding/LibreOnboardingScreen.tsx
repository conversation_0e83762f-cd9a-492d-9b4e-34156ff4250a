import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image,
  ScrollView,
  SafeAreaView,
  StatusBar
} from 'react-native';
import Colors from '../../constants/colors';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const LibreOnboardingScreen = () => {
  const router = useRouter();
  
  const handleConnectDevice = () => {
    router.push('/screens/devices/libre-connection');
  };
  
  const handleSkip = () => {
    router.replace('/dashboard');
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
            <Text style={styles.skipButtonText}>Skip</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.mainContent}>
          <Image 
            source={{ uri: 'https://placehold.co/400x250/png?text=FreeStyle+Libre+2' }}
            style={styles.deviceImage}
            resizeMode="contain"
          />
          
          <Text style={styles.title}>Continuous Glucose Monitoring</Text>
          <Text style={styles.subtitle}>Connect your FreeStyle Libre 2 for real-time glucose tracking</Text>
          
          <View style={styles.featuresContainer}>
            <View style={styles.featureItem}>
              <View style={[styles.featureIconContainer, { backgroundColor: Colors.primary }]}>
                <Ionicons name="pulse" size={24} color={Colors.white} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>Real-time Monitoring</Text>
                <Text style={styles.featureDescription}>
                  Track your glucose levels continuously without finger pricks
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <View style={[styles.featureIconContainer, { backgroundColor: Colors.secondary }]}>
                <Ionicons name="trending-up" size={24} color={Colors.white} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>Trend Analysis</Text>
                <Text style={styles.featureDescription}>
                  See patterns and trends in your glucose levels over time
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <View style={[styles.featureIconContainer, { backgroundColor: Colors.info }]}>
                <Ionicons name="notifications" size={24} color={Colors.white} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>Smart Alerts</Text>
                <Text style={styles.featureDescription}>
                  Get notified when your glucose is too high or too low
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <View style={[styles.featureIconContainer, { backgroundColor: Colors.success }]}>
                <Ionicons name="analytics" size={24} color={Colors.white} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>Personalized Insights</Text>
                <Text style={styles.featureDescription}>
                  Receive AI-powered recommendations based on your readings
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.infoContainer}>
            <View style={styles.infoHeader}>
              <Ionicons name="information-circle" size={20} color={Colors.primary} />
              <Text style={styles.infoTitle}>How It Works</Text>
            </View>
            <Text style={styles.infoText}>
              The FreeStyle Libre 2 sensor is worn on the back of your upper arm for up to 14 days. 
              It continuously measures glucose in your interstitial fluid and sends readings to your 
              phone via Bluetooth. Preventely will integrate this data to provide you with comprehensive 
              health insights.
            </Text>
          </View>
        </View>
        
        <View style={styles.footer}>
          <TouchableOpacity 
            style={styles.connectButton}
            onPress={handleConnectDevice}
          >
            <View style={styles.buttonContent}>
              <Text style={styles.connectButtonText}>Connect Your Device</Text>
              <View style={styles.buttonIconContainer}>
                <Ionicons name="arrow-forward" size={20} color={Colors.white} />
              </View>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.buyDeviceButton}
            onPress={() => {}}
          >
            <Text style={styles.buyDeviceText}>Don't have a device? Purchase here</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  skipButton: {
    padding: 8,
  },
  skipButtonText: {
    fontSize: 16,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  mainContent: {
    flex: 1,
    alignItems: 'center',
  },
  deviceImage: {
    width: '100%',
    height: 180,
    marginBottom: 24,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
  infoContainer: {
    width: '100%',
    backgroundColor: `${Colors.primary}10`,
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
    marginLeft: 6,
  },
  infoText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  footer: {
    marginTop: 'auto',
    marginBottom: 16,
  },
  connectButton: {
    backgroundColor: Colors.primary,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    overflow: 'hidden',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  connectButtonText: {
    color: Colors.white,
    fontSize: 18,
    fontWeight: '700',
    marginRight: 12,
    letterSpacing: 0.5,
  },
  buttonIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyDeviceButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  buyDeviceText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
});


export default LibreOnboardingScreen;
