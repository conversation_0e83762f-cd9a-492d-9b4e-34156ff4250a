import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView, 
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/colors';

type Gender = 'male' | 'female' | 'other' | '';
type Goal = 'prevention' | 'management' | 'reversal' | '';

const ProfileSetupScreen = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [profile, setProfile] = useState({
    name: '',
    age: '',
    weight: '',
    height: '',
    gender: '' as Gender,
    goal: '' as Goal
  });

  const updateProfile = (field: string, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Navigate to medical quiz
      router.push('/onboarding/medical-quiz');
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return profile.name.trim() !== '' && profile.age.trim() !== '';
      case 2:
        return profile.weight.trim() !== '' && profile.height.trim() !== '';
      case 3:
        return profile.gender !== '' && profile.goal !== '';
      default:
        return false;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Basic Information</Text>
            <Text style={styles.stepDescription}>
              Let's start with some basic information to personalize your experience
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your full name"
                  placeholderTextColor={Colors.textLight}
                  value={profile.name}
                  onChangeText={(value) => updateProfile('name', value)}
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Age</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your age"
                  placeholderTextColor={Colors.textLight}
                  keyboardType="number-pad"
                  value={profile.age}
                  onChangeText={(value) => updateProfile('age', value)}
                />
              </View>
            </View>
          </View>
        );
      case 2:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Body Measurements</Text>
            <Text style={styles.stepDescription}>
              Your measurements help us create a personalized health plan
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Weight (kg)</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your weight"
                  placeholderTextColor={Colors.textLight}
                  keyboardType="decimal-pad"
                  value={profile.weight}
                  onChangeText={(value) => updateProfile('weight', value)}
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Height (cm)</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter your height"
                  placeholderTextColor={Colors.textLight}
                  keyboardType="decimal-pad"
                  value={profile.height}
                  onChangeText={(value) => updateProfile('height', value)}
                />
              </View>
            </View>
          </View>
        );
      case 3:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Personal Preferences</Text>
            <Text style={styles.stepDescription}>
              Help us understand your goals and preferences
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Gender</Text>
              <View style={styles.optionsContainer}>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    profile.gender === 'male' && styles.selectedOption
                  ]}
                  onPress={() => updateProfile('gender', 'male')}
                >
                  <Text 
                    style={[
                      styles.optionText,
                      profile.gender === 'male' && styles.selectedOptionText
                    ]}
                  >
                    Male
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    profile.gender === 'female' && styles.selectedOption
                  ]}
                  onPress={() => updateProfile('gender', 'female')}
                >
                  <Text 
                    style={[
                      styles.optionText,
                      profile.gender === 'female' && styles.selectedOptionText
                    ]}
                  >
                    Female
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    profile.gender === 'other' && styles.selectedOption
                  ]}
                  onPress={() => updateProfile('gender', 'other')}
                >
                  <Text 
                    style={[
                      styles.optionText,
                      profile.gender === 'other' && styles.selectedOptionText
                    ]}
                  >
                    Other
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Your Primary Goal</Text>
              <TouchableOpacity
                style={[
                  styles.goalButton,
                  profile.goal === 'prevention' && styles.selectedGoal
                ]}
                onPress={() => updateProfile('goal', 'prevention')}
              >
                <View style={styles.goalIconContainer}>
                  <Ionicons 
                    name="shield-checkmark-outline" 
                    size={24} 
                    color={profile.goal === 'prevention' ? '#FFFFFF' : Colors.primary} 
                  />
                </View>
                <View style={styles.goalTextContainer}>
                  <Text 
                    style={[
                      styles.goalTitle,
                      profile.goal === 'prevention' && styles.selectedGoalText
                    ]}
                  >
                    Prevention
                  </Text>
                  <Text 
                    style={[
                      styles.goalDescription,
                      profile.goal === 'prevention' && styles.selectedGoalDescription
                    ]}
                  >
                    Prevent prediabetes development
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.goalButton,
                  profile.goal === 'management' && styles.selectedGoal
                ]}
                onPress={() => updateProfile('goal', 'management')}
              >
                <View style={styles.goalIconContainer}>
                  <Ionicons 
                    name="analytics-outline" 
                    size={24} 
                    color={profile.goal === 'management' ? '#FFFFFF' : Colors.secondary} 
                  />
                </View>
                <View style={styles.goalTextContainer}>
                  <Text 
                    style={[
                      styles.goalTitle,
                      profile.goal === 'management' && styles.selectedGoalText
                    ]}
                  >
                    Management
                  </Text>
                  <Text 
                    style={[
                      styles.goalDescription,
                      profile.goal === 'management' && styles.selectedGoalDescription
                    ]}
                  >
                    Manage existing prediabetes condition
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.goalButton,
                  profile.goal === 'reversal' && styles.selectedGoal
                ]}
                onPress={() => updateProfile('goal', 'reversal')}
              >
                <View style={styles.goalIconContainer}>
                  <Ionicons 
                    name="refresh-outline" 
                    size={24} 
                    color={profile.goal === 'reversal' ? '#FFFFFF' : Colors.tertiary} 
                  />
                </View>
                <View style={styles.goalTextContainer}>
                  <Text 
                    style={[
                      styles.goalTitle,
                      profile.goal === 'reversal' && styles.selectedGoalText
                    ]}
                  >
                    Reversal
                  </Text>
                  <Text 
                    style={[
                      styles.goalDescription,
                      profile.goal === 'reversal' && styles.selectedGoalDescription
                    ]}
                  >
                    Work towards reversing prediabetes
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={Colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Profile Setup</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${(currentStep / 3) * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>Step {currentStep} of 3</Text>
        </View>

        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {renderStep()}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity 
            style={[
              styles.button, 
              !isStepValid() && styles.disabledButton
            ]} 
            onPress={handleNext}
            disabled={!isStepValid()}
          >
            <Text style={styles.buttonText}>
              {currentStep < 3 ? 'Next' : 'Start Quiz'}
            </Text>
            <Ionicons 
              name={currentStep < 3 ? "arrow-forward" : "clipboard-outline"} 
              size={20} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'right',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  stepContainer: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  inputContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  input: {
    height: 50,
    paddingHorizontal: 16,
    color: Colors.text,
    fontSize: 16,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  optionButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  selectedOption: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  optionText: {
    color: Colors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  selectedOptionText: {
    color: '#FFFFFF',
  },
  goalButton: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  selectedGoal: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  goalIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(106, 90, 205, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  goalTextContainer: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  goalDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  selectedGoalText: {
    color: '#FFFFFF',
  },
  selectedGoalDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  footer: {
    padding: 24,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.textLight,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
});


export default ProfileSetupScreen;
