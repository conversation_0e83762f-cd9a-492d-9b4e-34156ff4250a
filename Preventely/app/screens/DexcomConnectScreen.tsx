import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput, 
  ActivityIndicator,
  Alert,
  Platform
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import apiService from '../services/api.service';

// Define the type for route params
type RouteParams = {
  redirectAfterConnect?: string;
};

const DexcomConnectScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const params = route.params as RouteParams;
  
  const [loading, setLoading] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [authStage, setAuthStage] = useState<'initial' | 'login' | 'hipaa' | 'success'>('initial');
  const [fullName, setFullName] = useState('');
  
  // Get the redirect URI for the OAuth flow
  const redirectUri = Linking.createURL('dexcom-auth');
  
  // Handle deep linking when returning from Dexcom auth
  useEffect(() => {
    const handleDeepLink = (event: { url: string }) => {
      const { url } = event;
      handleRedirect(url);
    };

    // Add event listener for deep links
    const subscription = Linking.addEventListener('url', handleDeepLink);

    // Check if app was opened with a URL
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleRedirect(url);
      }
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // Handle redirect from Dexcom OAuth
  const handleRedirect = async (url: string) => {
    if (!url || !url.includes('dexcom-auth')) return;
    
    try {
      // Extract the authorization code from the URL
      const code = url.split('code=')[1]?.split('&')[0];
      
      if (code) {
        setLoading(true);
        // Connect to Dexcom using the authorization code
        const response = await apiService.connectDexcom(code, redirectUri);
        setLoading(false);
        
        if (response.success) {
          setAuthStage('success');
          
          // Navigate back to the referring screen after a delay
          setTimeout(() => {
            if (params?.redirectAfterConnect) {
              navigation.navigate(params.redirectAfterConnect as never);
            } else {
              navigation.goBack();
            }
          }, 2000);
        } else {
          Alert.alert('Connection Error', 'Failed to connect to Dexcom. Please try again.');
        }
      }
    } catch (error) {
      setLoading(false);
      console.error('Error handling Dexcom redirect:', error);
      Alert.alert('Connection Error', 'An error occurred while connecting to Dexcom.');
    }
  };

  // Start the Dexcom OAuth flow
  const startDexcomAuth = async () => {
    try {
      setLoading(true);
      // Get the Dexcom authorization URL
      const authUrl = await apiService.getDexcomAuthUrl(redirectUri);
      setLoading(false);
      
      // Open the authorization URL in the browser
      if (authUrl) {
        await WebBrowser.openBrowserAsync(authUrl);
      } else {
        Alert.alert('Error', 'Failed to get Dexcom authorization URL.');
      }
    } catch (error) {
      setLoading(false);
      console.error('Error starting Dexcom auth:', error);
      Alert.alert('Error', 'Failed to start Dexcom authorization process.');
    }
  };

  // Simulate direct login flow (for the UI shown in the screenshot)
  const handleLogin = () => {
    if (!username || !password) {
      Alert.alert('Missing Information', 'Please enter both username and password.');
      return;
    }
    
    setLoading(true);
    // Simulate login process
    setTimeout(() => {
      setLoading(false);
      setAuthStage('hipaa');
    }, 1500);
  };

  // Simulate HIPAA authorization
  const handleHipaaAuthorization = () => {
    if (!fullName) {
      Alert.alert('Missing Information', 'Please enter your full name to authorize.');
      return;
    }
    
    setLoading(true);
    // Simulate authorization process
    setTimeout(() => {
      setLoading(false);
      setAuthStage('success');
      
      // Navigate back after success
      setTimeout(() => {
        if (params?.redirectAfterConnect) {
          navigation.navigate(params.redirectAfterConnect as never);
        } else {
          navigation.goBack();
        }
      }, 2000);
    }, 1500);
  };

  // Render the initial connection screen
  const renderInitialScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.partnerText}>PARTNER APP</Text>
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>dexcom</Text>
          <Text style={styles.logoTagline}>G6 CGM System</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.connectButton}
          onPress={() => setAuthStage('login')}
        >
          <Text style={styles.connectButtonText}>Connect Your Dexcom Account</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.providedByText}>PROVIDED BY DEXCOM</Text>
    </View>
  );

  // Render the login screen
  const renderLoginScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>LOGIN</Text>
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>dexcom</Text>
          <Text style={styles.logoTagline}>G6 CGM System</Text>
        </View>
        
        <Text style={styles.accountTitle}>Your Dexcom Account</Text>
        <Text style={styles.signInText}>Sign in to manage your account.</Text>
        
        <View style={styles.formContainer}>
          <Text style={styles.inputLabel}>Username*</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            autoCapitalize="none"
          />
          
          <Text style={styles.inputLabel}>Password*</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            secureTextEntry
          />
          
          <TouchableOpacity>
            <Text style={styles.forgotText}>Forgot your Dexcom username or password?</Text>
          </TouchableOpacity>
          
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={styles.createAccountButton}
              onPress={() => {/* Handle create account */}}
            >
              <Text style={styles.createAccountText}>Create Account</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.signInButton}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={styles.signInButtonText}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#0066CC" />
        </View>
      )}
    </View>
  );

  // Render the HIPAA authorization screen
  const renderHipaaScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>HIPAA AUTHORIZATION</Text>
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.logoContainer}>
          <Text style={[styles.logoText, { fontSize: 28 }]}>dexcom</Text>
          <Text style={styles.logoTagline}>G6 CGM System</Text>
        </View>
        
        <Text style={styles.hipaaTitle}>refer to information above.</Text>
        
        <View style={styles.hipaaInfoContainer}>
          <Text style={styles.hipaaText}>
            The Personal Information that [insert third party name] is requesting access to includes:
          </Text>
          
          <View style={styles.bulletPointContainer}>
            <Text style={styles.bulletPoint}>• Your Estimated Blood Glucose Levels</Text>
            <Text style={styles.bulletPoint}>• Your Calibration Data</Text>
            <Text style={styles.bulletPoint}>• Your Events Entry Data</Text>
            <Text style={styles.bulletPoint}>• Your Device Details</Text>
            <Text style={styles.bulletPoint}>• Your CGM Statistics</Text>
          </View>
          
          <Text style={styles.signatureText}>Enter electronic signature to authorize:</Text>
          
          <TextInput
            style={styles.signatureInput}
            value={fullName}
            onChangeText={setFullName}
            placeholder="Full Name"
          />
          
          <Text style={styles.signedOnText}>Signed on:</Text>
          
          <View style={styles.authButtonsContainer}>
            <TouchableOpacity 
              style={styles.declineButton}
              onPress={() => setAuthStage('login')}
            >
              <Text style={styles.declineButtonText}>Decline</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.authorizeButton}
              onPress={handleHipaaAuthorization}
            >
              <Text style={styles.authorizeButtonText}>Authorize</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#0066CC" />
        </View>
      )}
    </View>
  );

  // Render the success screen
  const renderSuccessScreen = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.partnerText}>PARTNER APP</Text>
      </View>
      
      <View style={styles.successContainer}>
        <Text style={styles.successTitle}>Successful</Text>
        <Text style={styles.successSubtitle}>HIPAA Authorization</Text>
        
        <ActivityIndicator size="large" color="#4CAF50" style={styles.successIndicator} />
        
        <Text style={styles.redirectingText}>Redirecting back to app...</Text>
      </View>
    </View>
  );

  // Render the appropriate screen based on the current auth stage
  switch (authStage) {
    case 'login':
      return renderLoginScreen();
    case 'hipaa':
      return renderHipaaScreen();
    case 'success':
      return renderSuccessScreen();
    default:
      return renderInitialScreen();
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  partnerText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#00A0E2', // Dexcom blue color
    letterSpacing: -1,
  },
  logoTagline: {
    fontSize: 14,
    color: '#00A0E2',
    marginTop: -5,
  },
  connectButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginTop: 20,
  },
  connectButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  providedByText: {
    textAlign: 'center',
    padding: 15,
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  accountTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  signInText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 5,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  forgotText: {
    color: '#0066CC',
    textAlign: 'right',
    marginBottom: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  createAccountButton: {
    padding: 15,
  },
  createAccountText: {
    color: '#0066CC',
    fontSize: 16,
  },
  signInButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 5,
  },
  signInButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  hipaaTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  hipaaInfoContainer: {
    width: '100%',
    padding: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 5,
  },
  hipaaText: {
    fontSize: 14,
    marginBottom: 10,
  },
  bulletPointContainer: {
    marginVertical: 15,
  },
  bulletPoint: {
    fontSize: 14,
    marginBottom: 5,
  },
  signatureText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
  },
  signatureInput: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 5,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
  },
  signedOnText: {
    fontSize: 14,
    marginBottom: 20,
  },
  authButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  declineButton: {
    backgroundColor: '#F5F5F5',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginRight: 10,
  },
  declineButtonText: {
    color: '#333',
    fontSize: 14,
  },
  authorizeButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  authorizeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  successSubtitle: {
    fontSize: 18,
    color: '#4CAF50',
    marginBottom: 30,
  },
  successIndicator: {
    marginVertical: 20,
  },
  redirectingText: {
    fontSize: 16,
    color: '#666',
  },
});

export default DexcomConnectScreen;
