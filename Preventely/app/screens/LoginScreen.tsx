import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Image,
  ScrollView
} from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { Ionicons } from '@expo/vector-icons';

// Use your computer's local IP address
// const API_URL = Platform.select({
//   ios: 'http://localhost:3000/api',
//   android: 'http://********:3000/api',
//   default: 'http://************:3000/api',
//   web: 'http://************:3000/api',
// });

const API_URL = 'http://localhost:3000/api';
export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const [name, setName] = useState('');
  const router = useRouter();

  const handleAuth = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (isRegisterMode && !name) {
      Alert.alert('Error', 'Please enter your name');
      return;
    }

    try {
      setIsLoading(true);
      
      const endpoint = isRegisterMode ? '/auth/register' : '/auth/login';
      const payload = isRegisterMode 
        ? { name, email, password } 
        : { email, password };
      
      const response = await axios.post(`${API_URL}${endpoint}`, payload);
      
      // Store token
      await AsyncStorage.setItem('auth_token', response.data.token);
      await AsyncStorage.setItem('user_id', response.data.id);
      await AsyncStorage.setItem('user_email', response.data.email);
      
      // For demo purposes, let's create a test user <NAME_EMAIL> and password 'password123'
      if (isRegisterMode) {
        Alert.alert('Success', 'Account created successfully!');
      }
      
      // Navigate to home screen
      router.replace('/home');
    } catch (error) {
      console.error('Auth error:', error);
      
      // For demo purposes, let's allow a test login <NAME_EMAIL> and password 'password123'
      if (email === '<EMAIL>' && password === 'password123') {
        await AsyncStorage.setItem('auth_token', 'demo_token');
        await AsyncStorage.setItem('user_id', 'demo_user_id');
        await AsyncStorage.setItem('user_email', email);
        router.replace('/home');
        return;
      }
      
      Alert.alert(
        'Authentication Failed', 
        'Please check your credentials and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
    // Clear fields when switching modes
    setName('');
    setEmail('');
    setPassword('');
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.logoContainer}>
          <Image 
            source={require('../../assets/images/icon.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>Prevently</Text>
          <Text style={styles.tagline}>Your Health, Your Control</Text>
        </View>
        
        <View style={styles.formContainer}>
          <Text style={styles.title}>
            {isRegisterMode ? 'Create Account' : 'Welcome Back'}
          </Text>
          
          {isRegisterMode && (
            <View style={styles.inputContainer}>
              <Ionicons name="person-outline" size={20} color="#6B5CE5" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Full Name"
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
              />
            </View>
          )}
          
          <View style={styles.inputContainer}>
            <Ionicons name="mail-outline" size={20} color="#6B5CE5" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Email Address"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputContainer}>
            <Ionicons name="lock-closed-outline" size={20} color="#6B5CE5" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
          </View>
          
          {!isRegisterMode && (
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity 
            style={styles.button}
            onPress={handleAuth}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.buttonText}>
                {isRegisterMode ? 'Sign Up' : 'Sign In'}
              </Text>
            )}
          </TouchableOpacity>
          
          <View style={styles.switchModeContainer}>
            <Text style={styles.switchModeText}>
              {isRegisterMode ? 'Already have an account?' : 'Don\'t have an account?'}
            </Text>
            <TouchableOpacity onPress={toggleMode}>
              <Text style={styles.switchModeButton}>
                {isRegisterMode ? 'Sign In' : 'Sign Up'}
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Demo login hint */}
          <Text style={styles.demoHint}>
            Demo: Use <EMAIL> / password123
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F1EFFF',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 10,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#6B5CE5',
    marginBottom: 5,
  },
  tagline: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 10,
    marginBottom: 15,
    paddingHorizontal: 10,
    backgroundColor: '#F8F8F8',
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: '#6B5CE5',
    fontSize: 14,
  },
  button: {
    backgroundColor: '#6B5CE5',
    borderRadius: 10,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  switchModeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchModeText: {
    color: '#666',
    fontSize: 14,
  },
  switchModeButton: {
    color: '#6B5CE5',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  demoHint: {
    textAlign: 'center',
    marginTop: 20,
    color: '#888',
    fontSize: 12,
    fontStyle: 'italic',
  },
});
