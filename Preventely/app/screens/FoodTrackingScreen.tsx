import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  FlatList, 
  TextInput, 
  ActivityIndicator,
  Modal,
  Image,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import apiService from '../services/api.service';

interface Food {
  id: string;
  name: string;
  description: string;
  calories: number;
  carbs: number;
  protein: number;
  fat: number;
  glycemicIndex: number | null;
  servingSize: string;
  servingSizeUnit: string;
  imageUrl: string | null;
}

interface FoodItemProps {
  food: Food;
  onPress: (food: Food) => void;
}

const FoodItem = ({ food, onPress }: FoodItemProps) => (
  <TouchableOpacity style={styles.foodItem} onPress={() => onPress(food)}>
    <View style={styles.foodImageContainer}>
      {food.imageUrl ? (
        <Image source={{ uri: food.imageUrl }} style={styles.foodImage} />
      ) : (
        <View style={styles.placeholderImage}>
          <Ionicons name="nutrition" size={24} color="#999" />
        </View>
      )}
    </View>
    <View style={styles.foodInfo}>
      <Text style={styles.foodName}>{food.name}</Text>
      <Text style={styles.foodDescription} numberOfLines={1}>
        {food.description || 'No description available'}
      </Text>
      <View style={styles.macros}>
        <Text style={styles.macroText}>{food.calories} kcal</Text>
        <Text style={styles.macroText}>C: {food.carbs}g</Text>
        <Text style={styles.macroText}>P: {food.protein}g</Text>
        <Text style={styles.macroText}>F: {food.fat}g</Text>
      </View>
    </View>
    <Ionicons name="chevron-forward" size={24} color="#999" />
  </TouchableOpacity>
);

export default function FoodTrackingScreen() {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [foods, setFoods] = useState<Food[]>([]);
  const [loading, setLoading] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [selectedFood, setSelectedFood] = useState<Food | null>(null);
  const [quantity, setQuantity] = useState('1');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      searchFoods();
    }
  }, [searchQuery, page]);

  const searchFoods = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      const response = await apiService.searchFoods(searchQuery, page);
      
      if (response.success) {
        if (page === 1) {
          setFoods(response.data);
        } else {
          setFoods(prevFoods => [...prevFoods, ...response.data]);
        }
        
        setHasMore(response.data.length > 0 && response.currentPage < response.totalPages);
      } else {
        console.error('Error searching foods:', response.message);
      }
    } catch (error) {
      console.error('Error searching foods:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToFoodSearch = () => {
    navigation.navigate('FoodSearchScreen' as never);
  };

  const handleFoodSelected = (food: Food) => {
    setSelectedFood(food);
  };

  const handleAddToMeal = async () => {
    if (!selectedFood) return;
    
    try {
      // Get today's date and current time
      const now = new Date();
      const date = now.toISOString().split('T')[0];
      const time = now.toISOString();
      
      // Create meal data
      const mealData = {
        name: 'Quick Log',
        date,
        time,
        mealItems: [
          {
            foodId: selectedFood.id,
            quantity: parseFloat(quantity) || 1
          }
        ]
      };
      
      // Create meal
      const response = await apiService.createMeal(mealData);
      
      if (response.success) {
        // Reset state
        setSelectedFood(null);
        setQuantity('1');
        
        // Navigate to meal details or back to home
        navigation.navigate('Home' as never);
      } else {
        console.error('Error adding food to meal:', response.message);
      }
    } catch (error) {
      console.error('Error adding food to meal:', error);
    }
  };

  const loadMoreFoods = () => {
    if (hasMore && !loading) {
      setPage(prevPage => prevPage + 1);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Food Tracking</Text>
      </View>
      
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={24} color="#999" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for foods..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={24} color="#999" />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity 
          style={styles.scanButton}
          onPress={navigateToFoodSearch}
        >
          <Ionicons name="search-outline" size={24} color="#FFF" />
        </TouchableOpacity>
      </View>
      
      {loading && page === 1 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0066CC" />
          <Text style={styles.loadingText}>Searching for foods...</Text>
        </View>
      ) : (
        <FlatList
          data={foods}
          renderItem={({ item }) => (
            <FoodItem food={item} onPress={handleFoodSelected} />
          )}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.foodsList}
          onEndReached={loadMoreFoods}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            loading && page > 1 ? (
              <ActivityIndicator size="small" color="#0066CC" style={styles.loadingMore} />
            ) : null
          }
          ListEmptyComponent={
            searchQuery.length > 2 ? (
              <View style={styles.emptyState}>
                <Ionicons name="nutrition-outline" size={64} color="#CCC" />
                <Text style={styles.emptyStateText}>No foods found</Text>
                <Text style={styles.emptyStateSubtext}>
                  Try a different search term or scan a barcode
                </Text>
              </View>
            ) : searchQuery.length > 0 ? (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateSubtext}>
                  Type at least 3 characters to search
                </Text>
              </View>
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="search" size={64} color="#CCC" />
                <Text style={styles.emptyStateText}>Search for foods</Text>
                <Text style={styles.emptyStateSubtext}>
                  Search by name or scan a barcode to find foods
                </Text>
              </View>
            )
          }
        />
      )}
      
      {/* Navigation to Food Search Screen happens when the search button is pressed */}
      
      {/* Food Details Modal */}
      <Modal
        visible={!!selectedFood}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setSelectedFood(null)}
      >
        {selectedFood && (
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <ScrollView>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>{selectedFood.name}</Text>
                  <TouchableOpacity onPress={() => setSelectedFood(null)}>
                    <Ionicons name="close" size={24} color="#333" />
                  </TouchableOpacity>
                </View>
                
                {selectedFood.imageUrl && (
                  <Image
                    source={{ uri: selectedFood.imageUrl }}
                    style={styles.modalFoodImage}
                    resizeMode="cover"
                  />
                )}
                
                <Text style={styles.sectionTitle}>Nutrition Information</Text>
                <View style={styles.nutritionInfo}>
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>Calories</Text>
                    <Text style={styles.nutritionValue}>{selectedFood.calories} kcal</Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>Carbs</Text>
                    <Text style={styles.nutritionValue}>{selectedFood.carbs}g</Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>Protein</Text>
                    <Text style={styles.nutritionValue}>{selectedFood.protein}g</Text>
                  </View>
                  <View style={styles.nutritionItem}>
                    <Text style={styles.nutritionLabel}>Fat</Text>
                    <Text style={styles.nutritionValue}>{selectedFood.fat}g</Text>
                  </View>
                  {selectedFood.glycemicIndex && (
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionLabel}>Glycemic Index</Text>
                      <Text style={styles.nutritionValue}>{selectedFood.glycemicIndex}</Text>
                    </View>
                  )}
                </View>
                
                <Text style={styles.sectionTitle}>Serving Size</Text>
                <Text style={styles.servingSize}>
                  {selectedFood.servingSize} {selectedFood.servingSizeUnit}
                </Text>
                
                <Text style={styles.sectionTitle}>Quantity</Text>
                <View style={styles.quantityContainer}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => {
                      const newQuantity = Math.max(0.5, parseFloat(quantity) - 0.5);
                      setQuantity(newQuantity.toString());
                    }}
                  >
                    <Ionicons name="remove" size={24} color="#333" />
                  </TouchableOpacity>
                  
                  <TextInput
                    style={styles.quantityInput}
                    value={quantity}
                    onChangeText={setQuantity}
                    keyboardType="numeric"
                  />
                  
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => {
                      const newQuantity = parseFloat(quantity) + 0.5;
                      setQuantity(newQuantity.toString());
                    }}
                  >
                    <Ionicons name="add" size={24} color="#333" />
                  </TouchableOpacity>
                </View>
                
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleAddToMeal}
                >
                  <Text style={styles.addButtonText}>Add to Quick Log</Text>
                </TouchableOpacity>
              </ScrollView>
            </View>
          </View>
        )}
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    backgroundColor: '#FFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 10,
    marginRight: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  scanButton: {
    backgroundColor: '#0066CC',
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  foodsList: {
    paddingVertical: 10,
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    marginHorizontal: 20,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  foodImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 15,
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  foodInfo: {
    flex: 1,
  },
  foodName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  foodDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  macros: {
    flexDirection: 'row',
  },
  macroText: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  loadingMore: {
    padding: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalFoodImage: {
    width: '100%',
    height: 200,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginHorizontal: 20,
  },
  nutritionInfo: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  nutritionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  nutritionLabel: {
    fontSize: 16,
    color: '#666',
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  servingSize: {
    fontSize: 16,
    color: '#666',
    marginHorizontal: 20,
    marginTop: 10,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityInput: {
    width: 60,
    height: 40,
    borderWidth: 1,
    borderColor: '#EEE',
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 16,
    marginHorizontal: 10,
  },
  addButton: {
    backgroundColor: '#0066CC',
    marginHorizontal: 20,
    marginVertical: 30,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  addButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
