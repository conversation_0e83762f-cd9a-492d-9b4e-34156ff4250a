import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Image,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Colors from '../../constants/colors';

type ConnectionStatus = 'intro' | 'scanning' | 'connecting' | 'syncing' | 'success' | 'error';

const LibreConnectionScreen = () => {
  const router = useRouter();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('intro');
  const [connectedDevice, setConnectedDevice] = useState<string | null>(null);
  
  const handleStartConnection = () => {
    setConnectionStatus('scanning');
    
    // Simulate scanning for devices
    setTimeout(() => {
      setConnectionStatus('connecting');
      
      // Simulate connecting to device
      setTimeout(() => {
        setConnectedDevice('FreeStyle Libre 2');
        setConnectionStatus('syncing');
        
        // Simulate syncing data
        setTimeout(() => {
          setConnectionStatus('success');
        }, 2000);
      }, 1500);
    }, 1500);
  };

  const handleGoBack = () => {
    if (connectionStatus === 'intro') {
      router.back();
    } else {
      setConnectionStatus('intro');
    }
  };

  const handleFinish = () => {
    router.replace('/dashboard');
  };
  
  const renderConnectionStatus = () => {
    switch (connectionStatus) {
      case 'intro':
        return (
          <View style={styles.introContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=FreeStyle+Libre+2' }}
              style={styles.deviceImage}
              resizeMode="contain"
            />
            
            <Text style={styles.introTitle}>Connect Your FreeStyle Libre 2</Text>
            
            <View style={styles.instructionsContainer}>
              <View style={styles.instructionItem}>
                <View style={styles.instructionNumber}>
                  <Text style={styles.instructionNumberText}>1</Text>
                </View>
                <Text style={styles.instructionText}>
                  Ensure your FreeStyle Libre 2 sensor is applied to your arm
                </Text>
              </View>
              
              <View style={styles.instructionItem}>
                <View style={styles.instructionNumber}>
                  <Text style={styles.instructionNumberText}>2</Text>
                </View>
                <Text style={styles.instructionText}>
                  Enable Bluetooth on your phone
                </Text>
              </View>
              
              <View style={styles.instructionItem}>
                <View style={styles.instructionNumber}>
                  <Text style={styles.instructionNumberText}>3</Text>
                </View>
                <Text style={styles.instructionText}>
                  Tap "Connect Device" and hold your phone near the sensor
                </Text>
              </View>
            </View>
            
            <TouchableOpacity 
              style={styles.connectButton}
              onPress={handleStartConnection}
            >
              <Ionicons name="bluetooth" size={18} color={Colors.white} />
              <Text style={styles.connectButtonText}>Connect Device</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'scanning':
        return (
          <View style={styles.statusContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=Scanning' }}
              style={styles.statusImage}
              resizeMode="contain"
            />
            <Text style={styles.statusTitle}>Scanning for Devices</Text>
            <View style={styles.statusIndicator}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.statusText}>Looking for your FreeStyle Libre 2...</Text>
            </View>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => setConnectionStatus('intro')}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'connecting':
        return (
          <View style={styles.statusContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=Connecting' }}
              style={styles.statusImage}
              resizeMode="contain"
            />
            <Text style={styles.statusTitle}>Connecting</Text>
            <View style={styles.statusIndicator}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.statusText}>Establishing connection with your device...</Text>
            </View>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => setConnectionStatus('intro')}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'syncing':
        return (
          <View style={styles.statusContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=Syncing' }}
              style={styles.statusImage}
              resizeMode="contain"
            />
            <Text style={styles.statusTitle}>Syncing Data</Text>
            <View style={styles.statusIndicator}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.statusText}>Retrieving glucose data from {connectedDevice}...</Text>
            </View>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => setConnectionStatus('intro')}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        );
      
      case 'success':
        return (
          <View style={styles.statusContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=Success' }}
              style={styles.statusImage}
              resizeMode="contain"
            />
            <View style={styles.successIconContainer}>
              <Ionicons name="checkmark-circle" size={80} color={Colors.success} />
            </View>
            <Text style={styles.statusTitle}>Connection Successful!</Text>
            <Text style={styles.successText}>
              Your FreeStyle Libre 2 is now connected to Preventely. Your glucose readings will be automatically synced.
            </Text>
            
            <View style={styles.dataContainer}>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Current Glucose</Text>
                <Text style={styles.dataValue}>112 mg/dL</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Trend</Text>
                <View style={styles.trendContainer}>
                  <Ionicons name="arrow-up" size={20} color={Colors.warning} />
                  <Text style={[styles.dataValue, {color: Colors.warning}]}>Rising</Text>
                </View>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Last Reading</Text>
                <Text style={styles.dataValue}>Just now</Text>
              </View>
              <View style={styles.dataItem}>
                <Text style={styles.dataLabel}>Sensor Expires</Text>
                <Text style={styles.dataValue}>13 days</Text>
              </View>
            </View>
            
            <TouchableOpacity 
              style={styles.finishButton}
              onPress={handleFinish}
            >
              <Text style={styles.finishButtonText}>Continue to Dashboard</Text>
              <Ionicons name="arrow-forward" size={18} color={Colors.white} />
            </TouchableOpacity>
          </View>
        );
      
      case 'error':
        return (
          <View style={styles.statusContainer}>
            <Image 
              source={{ uri: 'https://placehold.co/300x200/png?text=Error' }}
              style={styles.statusImage}
              resizeMode="contain"
            />
            <View style={styles.errorIconContainer}>
              <Ionicons name="alert-circle" size={80} color={Colors.error} />
            </View>
            <Text style={styles.statusTitle}>Connection Failed</Text>
            <Text style={styles.errorText}>
              We couldn't connect to your FreeStyle Libre 2. Please make sure your sensor is activated and within range.
            </Text>
            <View style={styles.troubleshootContainer}>
              <Text style={styles.troubleshootTitle}>Troubleshooting Tips:</Text>
              <Text style={styles.troubleshootItem}>• Ensure Bluetooth is enabled on your phone</Text>
              <Text style={styles.troubleshootItem}>• Hold your phone directly over the sensor</Text>
              <Text style={styles.troubleshootItem}>• Make sure your sensor is activated and not expired</Text>
              <Text style={styles.troubleshootItem}>• Try restarting your phone</Text>
            </View>
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={handleStartConnection}
            >
              <Ionicons name="refresh" size={18} color={Colors.white} />
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>FreeStyle Libre 2</Text>
        <View style={styles.headerRight} />
      </View>
      <ScrollView contentContainerStyle={styles.content}>
        {renderConnectionStatus()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.card,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  headerRight: {
    width: 32,
  },
  content: {
    flexGrow: 1,
    padding: 16,
  },
  introContainer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  deviceImage: {
    width: 200,
    height: 150,
    marginBottom: 24,
  },
  introTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 24,
    textAlign: 'center',
  },
  instructionsContainer: {
    width: '100%',
    marginBottom: 32,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  instructionNumberText: {
    color: Colors.white,
    fontWeight: '600',
    fontSize: 14,
  },
  instructionText: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
    lineHeight: 22,
  },
  connectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 24,
    width: '100%',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  connectButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  statusContainer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  statusImage: {
    width: 180,
    height: 120,
    marginBottom: 24,
  },
  statusTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  statusIndicator: {
    alignItems: 'center',
    marginBottom: 32,
  },
  statusText: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 12,
    textAlign: 'center',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  cancelButtonText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  successIconContainer: {
    marginBottom: 16,
  },
  successText: {
    fontSize: 16,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
    lineHeight: 22,
  },
  dataContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
  },
  dataItem: {
    width: '48%',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  dataLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  dataValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  finishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 24,
    width: '100%',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  finishButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  errorIconContainer: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
    lineHeight: 22,
  },
  troubleshootContainer: {
    width: '100%',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  troubleshootTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  troubleshootItem: {
    fontSize: 14,
    color: Colors.text,
    marginBottom: 8,
    lineHeight: 20,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 24,
    width: '100%',
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  retryButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});


export default LibreConnectionScreen;
