import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  SafeAreaView,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Colors from '../../constants/colors';
import WeightHistoryChart from '../../components/weight/WeightHistoryChart';
import BMICalculator from '../../components/weight/BMICalculator';
import ScaleSync from '../../components/weight/ScaleSync';

// Dummy weight data
const weightHistoryData = [
  { date: '2025-04-10', weight: 78.2, bmi: 24.1 },
  { date: '2025-04-17', weight: 77.8, bmi: 24.0 },
  { date: '2025-04-24', weight: 77.5, bmi: 23.9 },
  { date: '2025-05-01', weight: 76.9, bmi: 23.7 },
  { date: '2025-05-08', weight: 76.4, bmi: 23.6 },
];

// User profile data
const userProfile = {
  height: 180, // in cm
  weightGoal: 75.0, // in kg
  startWeight: 80.0, // in kg
  bmiGoal: 23.1,
};

const WeightTrackingScreen = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('weight');
  const [showAddWeightModal, setShowAddWeightModal] = useState(false);
  const [newWeight, setNewWeight] = useState('');
  const [weightData, setWeightData] = useState(weightHistoryData);
  
  // Calculate current weight (most recent entry)
  const currentWeight = weightData[weightData.length - 1].weight;
  const currentBMI = weightData[weightData.length - 1].bmi;
  
  // Calculate weight change
  const weightChange = currentWeight - weightData[0].weight;
  const weightChangePercentage = (weightChange / weightData[0].weight) * 100;
  
  // Calculate progress towards goal
  const totalWeightToLose = userProfile.startWeight - userProfile.weightGoal;
  const weightLostSoFar = userProfile.startWeight - currentWeight;
  const progressPercentage = (weightLostSoFar / totalWeightToLose) * 100;
  
  const handleAddWeight = () => {
    if (newWeight && !isNaN(parseFloat(newWeight))) {
      const weight = parseFloat(newWeight);
      const heightInMeters = userProfile.height / 100;
      const bmi = parseFloat((weight / (heightInMeters * heightInMeters)).toFixed(1));
      
      const today = new Date();
      const dateString = today.toISOString().split('T')[0];
      
      const newEntry = {
        date: dateString,
        weight,
        bmi,
      };
      
      setWeightData([...weightData, newEntry]);
      setNewWeight('');
      setShowAddWeightModal(false);
    }
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Weight Tracking</Text>
        <TouchableOpacity>
          <Ionicons name="settings-outline" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabBar}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'weight' && styles.activeTab]}
          onPress={() => setActiveTab('weight')}
        >
          <Text style={[styles.tabText, activeTab === 'weight' && styles.activeTabText]}>Weight</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'bmi' && styles.activeTab]}
          onPress={() => setActiveTab('bmi')}
        >
          <Text style={[styles.tabText, activeTab === 'bmi' && styles.activeTabText]}>BMI</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'progress' && styles.activeTab]}
          onPress={() => setActiveTab('progress')}
        >
          <Text style={[styles.tabText, activeTab === 'progress' && styles.activeTabText]}>Progress</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {activeTab === 'weight' && (
          <>
            <View style={styles.weightSummaryContainer}>
              <View style={styles.currentWeightContainer}>
                <Text style={styles.currentWeightLabel}>Current Weight</Text>
                <View style={styles.currentWeightValue}>
                  <Text style={styles.weightValue}>{currentWeight}</Text>
                  <Text style={styles.weightUnit}>kg</Text>
                </View>
                <View style={styles.weightChangeContainer}>
                  <Ionicons 
                    name={weightChange < 0 ? "trending-down" : "trending-up"} 
                    size={16} 
                    color={weightChange < 0 ? Colors.success : Colors.error} 
                  />
                  <Text 
                    style={[
                      styles.weightChangeText, 
                      { color: weightChange < 0 ? Colors.success : Colors.error }
                    ]}
                  >
                    {Math.abs(weightChange).toFixed(1)} kg ({Math.abs(weightChangePercentage).toFixed(1)}%)
                  </Text>
                </View>
              </View>
              
              <View style={styles.goalContainer}>
                <Text style={styles.goalLabel}>Goal Weight</Text>
                <Text style={styles.goalValue}>{userProfile.weightGoal} kg</Text>
                <View style={styles.progressContainer}>
                  <View style={styles.progressBarBackground}>
                    <View 
                      style={[
                        styles.progressBarFill, 
                        { width: `${Math.min(progressPercentage, 100)}%` }
                      ]} 
                    />
                  </View>
                  <Text style={styles.progressText}>{progressPercentage.toFixed(0)}%</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.chartContainer}>
              <View style={styles.chartHeader}>
                <Text style={styles.chartTitle}>Weight History</Text>
                <View style={styles.chartPeriodSelector}>
                  <TouchableOpacity style={[styles.periodButton, styles.activePeriod]}>
                    <Text style={styles.activePeriodText}>1M</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.periodButton}>
                    <Text style={styles.periodText}>3M</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.periodButton}>
                    <Text style={styles.periodText}>6M</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.periodButton}>
                    <Text style={styles.periodText}>1Y</Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              <WeightHistoryChart data={weightData} goalWeight={userProfile.weightGoal} />
            </View>
            
            <ScaleSync />
            
            <TouchableOpacity 
              style={styles.addWeightButton}
              onPress={() => setShowAddWeightModal(true)}
            >
              <Ionicons name="add-circle" size={20} color={Colors.white} />
              <Text style={styles.addWeightButtonText}>Add Weight Measurement</Text>
            </TouchableOpacity>
          </>
        )}
        
        {activeTab === 'bmi' && (
          <BMICalculator 
            currentWeight={currentWeight} 
            height={userProfile.height}
            currentBMI={currentBMI}
          />
        )}
        
        {activeTab === 'progress' && (
          <View style={styles.progressTabContainer}>
            <Text style={styles.progressTitle}>Your Health Progress</Text>
            
            {/* Weight Progress */}
            <View style={styles.progressCard}>
              <View style={styles.progressCardHeader}>
                <View style={[styles.progressIconContainer, { backgroundColor: `${Colors.primary}15` }]}>
                  <Ionicons name="scale-outline" size={24} color={Colors.primary} />
                </View>
                <View style={styles.progressCardTitleContainer}>
                  <Text style={styles.progressCardTitle}>Weight</Text>
                  <Text style={styles.progressCardSubtitle}>Last 30 days</Text>
                </View>
                <View style={styles.progressCardValue}>
                  <Text 
                    style={[
                      styles.progressCardValueText, 
                      { color: weightChange < 0 ? Colors.success : Colors.error }
                    ]}
                  >
                    {weightChange < 0 ? "-" : "+"}{Math.abs(weightChange).toFixed(1)} kg
                  </Text>
                </View>
              </View>
              <View style={styles.progressCardChart}>
                <View style={styles.miniChartBar}>
                  {weightData.map((entry, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.miniChartColumn, 
                        { 
                          height: `${((entry.weight - userProfile.weightGoal) / (userProfile.startWeight - userProfile.weightGoal)) * 100}%`,
                          backgroundColor: entry.weight < userProfile.weightGoal ? Colors.success : Colors.primary
                        }
                      ]} 
                    />
                  ))}
                </View>
              </View>
            </View>
            
            {/* BMI Progress */}
            <View style={styles.progressCard}>
              <View style={styles.progressCardHeader}>
                <View style={[styles.progressIconContainer, { backgroundColor: `${Colors.secondary}15` }]}>
                  <Ionicons name="body-outline" size={24} color={Colors.secondary} />
                </View>
                <View style={styles.progressCardTitleContainer}>
                  <Text style={styles.progressCardTitle}>BMI</Text>
                  <Text style={styles.progressCardSubtitle}>Last 30 days</Text>
                </View>
                <View style={styles.progressCardValue}>
                  <Text 
                    style={[
                      styles.progressCardValueText, 
                      { color: currentBMI < 25 ? Colors.success : Colors.warning }
                    ]}
                  >
                    {currentBMI.toFixed(1)}
                  </Text>
                </View>
              </View>
              <View style={styles.progressCardChart}>
                <View style={styles.miniChartBar}>
                  {weightData.map((entry, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.miniChartColumn, 
                        { 
                          height: `${((entry.bmi - 18.5) / (30 - 18.5)) * 100}%`,
                          backgroundColor: entry.bmi < 25 ? Colors.success : 
                                          entry.bmi < 30 ? Colors.warning : Colors.error
                        }
                      ]} 
                    />
                  ))}
                </View>
              </View>
            </View>
            
            {/* Glucose Progress */}
            <View style={styles.progressCard}>
              <View style={styles.progressCardHeader}>
                <View style={[styles.progressIconContainer, { backgroundColor: `${Colors.tertiary}15` }]}>
                  <Ionicons name="water-outline" size={24} color={Colors.tertiary} />
                </View>
                <View style={styles.progressCardTitleContainer}>
                  <Text style={styles.progressCardTitle}>Glucose</Text>
                  <Text style={styles.progressCardSubtitle}>Last 30 days</Text>
                </View>
                <View style={styles.progressCardValue}>
                  <Text 
                    style={[
                      styles.progressCardValueText, 
                      { color: Colors.success }
                    ]}
                  >
                    -12%
                  </Text>
                </View>
              </View>
              <View style={styles.progressCardChart}>
                <View style={styles.miniChartBar}>
                  {[110, 108, 105, 102, 98].map((value, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.miniChartColumn, 
                        { 
                          height: `${((value - 70) / (180 - 70)) * 100}%`,
                          backgroundColor: value < 100 ? Colors.success : 
                                          value < 125 ? Colors.warning : Colors.error
                        }
                      ]} 
                    />
                  ))}
                </View>
              </View>
            </View>
            
            {/* GI/GL Progress */}
            <View style={styles.progressCard}>
              <View style={styles.progressCardHeader}>
                <View style={[styles.progressIconContainer, { backgroundColor: `${Colors.carbs}15` }]}>
                  <Ionicons name="nutrition-outline" size={24} color={Colors.carbs} />
                </View>
                <View style={styles.progressCardTitleContainer}>
                  <Text style={styles.progressCardTitle}>Glycemic Load</Text>
                  <Text style={styles.progressCardSubtitle}>Last 30 days</Text>
                </View>
                <View style={styles.progressCardValue}>
                  <Text 
                    style={[
                      styles.progressCardValueText, 
                      { color: Colors.success }
                    ]}
                  >
                    -18%
                  </Text>
                </View>
              </View>
              <View style={styles.progressCardChart}>
                <View style={styles.miniChartBar}>
                  {[65, 58, 52, 45, 38].map((value, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.miniChartColumn, 
                        { 
                          height: `${(value / 100) * 100}%`,
                          backgroundColor: value < 50 ? Colors.success : 
                                          value < 70 ? Colors.warning : Colors.error
                        }
                      ]} 
                    />
                  ))}
                </View>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
      
      {/* Add Weight Modal */}
      <Modal
        visible={showAddWeightModal}
        transparent={true}
        animationType="slide"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Weight Measurement</Text>
              <TouchableOpacity onPress={() => setShowAddWeightModal(false)}>
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalContent}>
              <Text style={styles.inputLabel}>Weight (kg)</Text>
              <View style={styles.weightInputContainer}>
                <TextInput
                  style={styles.weightInput}
                  value={newWeight}
                  onChangeText={setNewWeight}
                  keyboardType="numeric"
                  placeholder="Enter weight"
                />
                <Text style={styles.weightInputUnit}>kg</Text>
              </View>
              
              <Text style={styles.modalNote}>
                Your BMI will be automatically calculated based on your height ({userProfile.height} cm).
              </Text>
              
              <TouchableOpacity 
                style={styles.saveButton}
                onPress={handleAddWeight}
              >
                <Text style={styles.saveButtonText}>Save Measurement</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default WeightTrackingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tab: {
    paddingVertical: 12,
    marginRight: 24,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  activeTabText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  weightSummaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  currentWeightContainer: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginRight: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  currentWeightLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  currentWeightValue: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  weightValue: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.text,
  },
  weightUnit: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  weightChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  weightChangeText: {
    fontSize: 14,
    marginLeft: 4,
  },
  goalContainer: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginLeft: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  goalLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  goalValue: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
    marginLeft: 8,
    width: 40,
    textAlign: 'right',
  },
  chartContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  chartPeriodSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.cardAlt,
    borderRadius: 20,
    padding: 4,
  },
  periodButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  activePeriod: {
    backgroundColor: Colors.primary,
  },
  periodText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  activePeriodText: {
    fontSize: 12,
    color: Colors.white,
    fontWeight: '600',
  },
  addWeightButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    marginBottom: 24,
  },
  addWeightButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  modalContent: {
    paddingBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.text,
    marginBottom: 8,
  },
  weightInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  weightInput: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 12,
  },
  weightInputUnit: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  modalNote: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 24,
    lineHeight: 20,
  },
  saveButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
  },
  progressTabContainer: {
    paddingBottom: 24,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  progressCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  progressCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  progressCardTitleContainer: {
    flex: 1,
  },
  progressCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  progressCardSubtitle: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  progressCardValue: {
    
  },
  progressCardValueText: {
    fontSize: 16,
    fontWeight: '700',
  },
  progressCardChart: {
    height: 100,
    marginTop: 8,
  },
  miniChartBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: '100%',
  },
  miniChartColumn: {
    width: 16,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});
