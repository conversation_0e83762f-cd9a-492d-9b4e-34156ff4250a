import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import FoodSuggestions from '../../components/food/FoodSuggestions';
import GlycemicIndicator from '../../components/food/GlycemicIndicator';
import MealList from '../../components/food/MealList';
import NutritionSummary from '../../components/food/NutritionSummary';
import Colors from '../../constants/colors';

// Dummy data for today's meals
const todaysMeals = [
  {
    id: '1',
    name: 'Breakfast',
    time: '7:30 AM',
    foods: [
      { id: 'f1', name: 'Oatmeal with berries', quantity: '1 bowl', calories: 220, gi: 55, gl: 12 },
      { id: 'f2', name: 'Greek yogurt', quantity: '100g', calories: 100, gi: 35, gl: 4 },
    ],
    totalCalories: 320,
    totalCarbs: 45,
    totalProtein: 15,
    totalFat: 8,
    averageGI: 45,
    totalGL: 16,
  },
  {
    id: '2',
    name: 'Lunch',
    time: '12:30 PM',
    foods: [
      { id: 'f3', name: 'Grilled chicken salad', quantity: '1 plate', calories: 350, gi: 30, gl: 5 },
      { id: 'f4', name: 'Whole grain bread', quantity: '1 slice', calories: 80, gi: 50, gl: 10 },
    ],
    totalCalories: 430,
    totalCarbs: 30,
    totalProtein: 35,
    totalFat: 15,
    averageGI: 40,
    totalGL: 15,
  },
  {
    id: '3',
    name: 'Snack',
    time: '3:30 PM',
    foods: [
      { id: 'f5', name: 'Apple', quantity: '1 medium', calories: 95, gi: 38, gl: 6 },
      { id: 'f6', name: 'Almonds', quantity: '23 nuts', calories: 160, gi: 15, gl: 1 },
    ],
    totalCalories: 255,
    totalCarbs: 25,
    totalProtein: 6,
    totalFat: 14,
    averageGI: 27,
    totalGL: 7,
  },
];

// Daily nutrition totals
const dailyNutrition = {
  calories: {
    consumed: 1005,
    goal: 1800,
  },
  carbs: {
    consumed: 100,
    goal: 200,
    percentage: 50,
  },
  protein: {
    consumed: 56,
    goal: 90,
    percentage: 62,
  },
  fat: {
    consumed: 37,
    goal: 60,
    percentage: 62,
  },
  glycemicLoad: {
    value: 38,
    status: 'good', // 'good', 'moderate', 'high'
  },
};

// Food suggestions based on user's profile
const foodSuggestions = [
  {
    id: 's1',
    category: 'Low GI Breakfast',
    foods: [
      { id: 'sf1', name: 'Steel-cut oats with cinnamon', gi: 42, benefits: 'Steady energy release' },
      { id: 'sf2', name: 'Vegetable omelette', gi: 15, benefits: 'High protein, minimal impact on blood sugar' },
    ],
  },
  {
    id: 's2',
    category: 'Balanced Snacks',
    foods: [
      { id: 'sf3', name: 'Greek yogurt with berries', gi: 35, benefits: 'Protein + antioxidants' },
      { id: 'sf4', name: 'Hummus with cucumber', gi: 15, benefits: 'Fiber + healthy fats' },
    ],
  },
];

const FoodTrackingScreen = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('today');
  
  const handleAddMeal = () => {
    router.push('/food/add-meal');
  };
  
  const handleScanBarcode = () => {
    router.push('/food/barcode-scanner');
  };
  
  const handleViewHistory = () => {
    setActiveTab('history');
  };
  
  const handleViewToday = () => {
    setActiveTab('today');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Food Tracking</Text>
        <TouchableOpacity>
          <Ionicons name="settings-outline" size={24} color={Colors.text} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabBar}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'today' && styles.activeTab]}
          onPress={handleViewToday}
        >
          <Text style={[styles.tabText, activeTab === 'today' && styles.activeTabText]}>Today</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={handleViewHistory}
        >
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>History</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {activeTab === 'today' ? (
          <>
            <View style={styles.dateContainer}>
              <Text style={styles.date}>Saturday, May 10, 2025</Text>
            </View>
            
            <NutritionSummary nutrition={dailyNutrition} />
            
            <GlycemicIndicator glycemicLoad={dailyNutrition.glycemicLoad} />
            
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Today's Meals</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>See All</Text>
              </TouchableOpacity>
            </View>
            
            <MealList meals={todaysMeals} />
            
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Suggested for You</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>Refresh</Text>
              </TouchableOpacity>
            </View>
            
            <FoodSuggestions suggestions={foodSuggestions} />
          </>
        ) : (
          <View style={styles.historyContainer}>
            <Text style={styles.historyTitle}>Food History</Text>
            {/* Food history component would go here */}
            <Text style={styles.comingSoon}>Coming soon...</Text>
          </View>
        )}
      </ScrollView>
      
      <View style={styles.actionBar}>
        <TouchableOpacity style={styles.actionButton} onPress={handleAddMeal}>
          <Ionicons name="add-circle" size={24} color={Colors.primary} />
          <Text style={styles.actionText}>Add Meal</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleScanBarcode}>
          <Ionicons name="barcode-outline" size={24} color={Colors.primary} />
          <Text style={styles.actionText}>Scan Barcode</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default FoodTrackingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tab: {
    paddingVertical: 12,
    marginRight: 24,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  activeTabText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  dateContainer: {
    marginVertical: 16,
    alignItems: 'center',
  },
  date: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  sectionAction: {
    fontSize: 14,
    color: Colors.primary,
  },
  historyContainer: {
    paddingVertical: 24,
    alignItems: 'center',
  },
  historyTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 16,
  },
  comingSoon: {
    fontSize: 16,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.card,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  actionText: {
    fontSize: 16,
    color: Colors.primary,
    marginLeft: 8,
  },
});
