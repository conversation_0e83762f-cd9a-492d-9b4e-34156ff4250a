import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  SafeAreaView,
  Switch,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Colors from '../../constants/colors';

// Meal type options
const mealTypes = [
  { id: 'breakfast', name: 'Breakfast' },
  { id: 'lunch', name: 'Lunch' },
  { id: 'dinner', name: 'Dinner' },
  { id: 'snack', name: 'Snack' },
];

// Common food suggestions based on meal type
const commonFoods = {
  breakfast: [
    { id: 'b1', name: 'Oatmeal', calories: 150, carbs: 27, protein: 5, fat: 3, gi: 55 },
    { id: 'b2', name: 'Greek Yogurt', calories: 100, carbs: 6, protein: 17, fat: 0, gi: 35 },
    { id: 'b3', name: 'Whole Grain Toast', calories: 80, carbs: 15, protein: 3, fat: 1, gi: 50 },
    { id: 'b4', name: 'Eggs', calories: 140, carbs: 0, protein: 12, fat: 10, gi: 0 },
  ],
  lunch: [
    { id: 'l1', name: 'Grilled Chicken Salad', calories: 350, carbs: 10, protein: 35, fat: 15, gi: 30 },
    { id: 'l2', name: 'Turkey Sandwich', calories: 320, carbs: 40, protein: 20, fat: 8, gi: 45 },
    { id: 'l3', name: 'Quinoa Bowl', calories: 400, carbs: 60, protein: 15, fat: 10, gi: 53 },
  ],
  dinner: [
    { id: 'd1', name: 'Salmon with Vegetables', calories: 450, carbs: 20, protein: 40, fat: 22, gi: 25 },
    { id: 'd2', name: 'Whole Wheat Pasta', calories: 380, carbs: 70, protein: 15, fat: 3, gi: 45 },
    { id: 'd3', name: 'Lentil Soup', calories: 250, carbs: 40, protein: 15, fat: 2, gi: 30 },
  ],
  snack: [
    { id: 's1', name: 'Apple', calories: 95, carbs: 25, protein: 0, fat: 0, gi: 38 },
    { id: 's2', name: 'Almonds', calories: 160, carbs: 6, protein: 6, fat: 14, gi: 15 },
    { id: 's3', name: 'Protein Bar', calories: 200, carbs: 20, protein: 15, fat: 8, gi: 40 },
  ],
};

const AddMealScreen = () => {
  const router = useRouter();
  const [selectedMealType, setSelectedMealType] = useState('breakfast');
  const [mealTime, setMealTime] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFoods, setSelectedFoods] = useState<any[]>([]);
  const [showScanner, setShowScanner] = useState(false);
  
  // Handle selecting a food item
  const handleSelectFood = (food: any) => {
    // Check if food is already selected
    const existingIndex = selectedFoods.findIndex(item => item.id === food.id);
    
    if (existingIndex >= 0) {
      // Update quantity if already selected
      const updatedFoods = [...selectedFoods];
      updatedFoods[existingIndex] = {
        ...updatedFoods[existingIndex],
        quantity: (updatedFoods[existingIndex].quantity || 1) + 1
      };
      setSelectedFoods(updatedFoods);
    } else {
      // Add new food with quantity 1
      setSelectedFoods([...selectedFoods, { ...food, quantity: 1 }]);
    }
  };
  
  // Handle removing a food item
  const handleRemoveFood = (foodId: string) => {
    setSelectedFoods(selectedFoods.filter(food => food.id !== foodId));
  };
  
  // Handle quantity change
  const handleQuantityChange = (foodId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveFood(foodId);
      return;
    }
    
    const updatedFoods = selectedFoods.map(food => {
      if (food.id === foodId) {
        return { ...food, quantity: newQuantity };
      }
      return food;
    });
    
    setSelectedFoods(updatedFoods);
  };
  
  // Calculate total nutrition
  const calculateTotals = () => {
    return selectedFoods.reduce((totals, food) => {
      const quantity = food.quantity || 1;
      return {
        calories: totals.calories + (food.calories * quantity),
        carbs: totals.carbs + (food.carbs * quantity),
        protein: totals.protein + (food.protein * quantity),
        fat: totals.fat + (food.fat * quantity),
      };
    }, { calories: 0, carbs: 0, protein: 0, fat: 0 });
  };
  
  const totals = calculateTotals();
  
  // Toggle barcode scanner
  const toggleScanner = () => {
    setShowScanner(!showScanner);
  };
  
  // Save meal and return to food tracking
  const handleSaveMeal = () => {
    // Here you would save the meal data
    // For now, just navigate back
    router.back();
  };
  
  // Filter foods based on search query
  const getFilteredFoods = () => {
    const foods = commonFoods[selectedMealType as keyof typeof commonFoods] || [];
    
    if (!searchQuery.trim()) {
      return foods;
    }
    
    const query = searchQuery.toLowerCase();
    return foods.filter(food => 
      food.name.toLowerCase().includes(query)
    );
  };
  
  const filteredFoods = getFilteredFoods();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Add Meal</Text>
        <TouchableOpacity onPress={handleSaveMeal}>
          <Text style={styles.saveButton}>Save</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Meal Type Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meal Type</Text>
          <View style={styles.mealTypeContainer}>
            {mealTypes.map(type => (
              <TouchableOpacity
                key={type.id}
                style={[
                  styles.mealTypeButton,
                  selectedMealType === type.id && styles.selectedMealType
                ]}
                onPress={() => setSelectedMealType(type.id)}
              >
                <Text 
                  style={[
                    styles.mealTypeText,
                    selectedMealType === type.id && styles.selectedMealTypeText
                  ]}
                >
                  {type.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Meal Time */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Time</Text>
          <TextInput
            style={styles.timeInput}
            placeholder="Enter time (e.g., 7:30 AM)"
            value={mealTime}
            onChangeText={setMealTime}
          />
        </View>
        
        {/* Food Search */}
        <View style={styles.section}>
          <View style={styles.searchHeader}>
            <Text style={styles.sectionTitle}>Add Foods</Text>
            <TouchableOpacity 
              style={styles.scanButton}
              onPress={toggleScanner}
            >
              <Ionicons name="barcode-outline" size={18} color={Colors.primary} />
              <Text style={styles.scanButtonText}>Scan</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color={Colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for foods..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          
          {showScanner && (
            <View style={styles.scannerContainer}>
              <View style={styles.mockScanner}>
                <Ionicons name="scan-outline" size={60} color={Colors.primary} />
                <Text style={styles.scannerText}>Position barcode within frame</Text>
                <View style={styles.scannerOverlay}>
                  <View style={styles.scannerCorner1} />
                  <View style={styles.scannerCorner2} />
                  <View style={styles.scannerCorner3} />
                  <View style={styles.scannerCorner4} />
                </View>
              </View>
              <TouchableOpacity 
                style={styles.cancelScanButton}
                onPress={toggleScanner}
              >
                <Text style={styles.cancelScanText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          )}
          
          {!showScanner && (
            <>
              {/* Common Foods */}
              <View style={styles.foodsContainer}>
                {filteredFoods.map(food => (
                  <TouchableOpacity
                    key={food.id}
                    style={styles.foodItem}
                    onPress={() => handleSelectFood(food)}
                  >
                    <View style={styles.foodInfo}>
                      <Text style={styles.foodName}>{food.name}</Text>
                      <Text style={styles.foodCalories}>{food.calories} cal</Text>
                    </View>
                    <View style={styles.foodMacros}>
                      <Text style={styles.foodMacro}>C: {food.carbs}g</Text>
                      <Text style={styles.foodMacro}>P: {food.protein}g</Text>
                      <Text style={styles.foodMacro}>F: {food.fat}g</Text>
                    </View>
                    <View style={styles.foodGI}>
                      <Text 
                        style={[
                          styles.foodGIValue,
                          { color: food.gi <= 55 ? Colors.success : food.gi <= 69 ? Colors.warning : Colors.error }
                        ]}
                      >
                        GI: {food.gi}
                      </Text>
                    </View>
                    <Ionicons name="add-circle" size={24} color={Colors.primary} />
                  </TouchableOpacity>
                ))}
              </View>
              
              {/* Selected Foods */}
              {selectedFoods.length > 0 && (
                <View style={styles.selectedFoodsSection}>
                  <Text style={styles.sectionTitle}>Selected Foods</Text>
                  <View style={styles.selectedFoodsContainer}>
                    {selectedFoods.map(food => (
                      <View key={food.id} style={styles.selectedFoodItem}>
                        <View style={styles.selectedFoodInfo}>
                          <Text style={styles.selectedFoodName}>{food.name}</Text>
                          <Text style={styles.selectedFoodCalories}>
                            {food.calories * (food.quantity || 1)} cal
                          </Text>
                        </View>
                        <View style={styles.quantityControl}>
                          <TouchableOpacity
                            onPress={() => handleQuantityChange(food.id, (food.quantity || 1) - 1)}
                          >
                            <Ionicons name="remove-circle" size={24} color={Colors.primary} />
                          </TouchableOpacity>
                          <Text style={styles.quantityText}>{food.quantity || 1}</Text>
                          <TouchableOpacity
                            onPress={() => handleQuantityChange(food.id, (food.quantity || 1) + 1)}
                          >
                            <Ionicons name="add-circle" size={24} color={Colors.primary} />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))}
                  </View>
                  
                  {/* Nutrition Summary */}
                  <View style={styles.nutritionSummary}>
                    <Text style={styles.summaryTitle}>Meal Totals</Text>
                    <View style={styles.summaryRow}>
                      <Text style={styles.summaryLabel}>Calories:</Text>
                      <Text style={styles.summaryValue}>{totals.calories}</Text>
                    </View>
                    <View style={styles.summaryRow}>
                      <Text style={styles.summaryLabel}>Carbs:</Text>
                      <Text style={styles.summaryValue}>{totals.carbs}g</Text>
                    </View>
                    <View style={styles.summaryRow}>
                      <Text style={styles.summaryLabel}>Protein:</Text>
                      <Text style={styles.summaryValue}>{totals.protein}g</Text>
                    </View>
                    <View style={styles.summaryRow}>
                      <Text style={styles.summaryLabel}>Fat:</Text>
                      <Text style={styles.summaryValue}>{totals.fat}g</Text>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>
      
      {selectedFoods.length > 0 && !showScanner && (
        <View style={styles.saveButtonContainer}>
          <TouchableOpacity 
            style={styles.saveButtonLarge}
            onPress={handleSaveMeal}
          >
            <Text style={styles.saveButtonText}>Save Meal</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

export default AddMealScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  mealTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  mealTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.cardAlt,
    marginRight: 8,
  },
  selectedMealType: {
    backgroundColor: Colors.primary,
  },
  mealTypeText: {
    fontSize: 14,
    color: Colors.text,
  },
  selectedMealTypeText: {
    color: Colors.white,
  },
  timeInput: {
    backgroundColor: Colors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  searchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${Colors.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  scanButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary,
    marginLeft: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 10,
    fontSize: 16,
  },
  foodsContainer: {
    gap: 12,
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  foodInfo: {
    flex: 1,
  },
  foodName: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.text,
  },
  foodCalories: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  foodMacros: {
    flexDirection: 'row',
    gap: 8,
    marginRight: 12,
  },
  foodMacro: {
    fontSize: 13,
    color: Colors.textSecondary,
  },
  foodGI: {
    marginRight: 12,
  },
  foodGIValue: {
    fontSize: 13,
    fontWeight: '500',
  },
  selectedFoodsSection: {
    marginTop: 24,
    marginBottom: 80,
  },
  selectedFoodsContainer: {
    gap: 12,
  },
  selectedFoodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 12,
  },
  selectedFoodInfo: {
    flex: 1,
  },
  selectedFoodName: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.text,
  },
  selectedFoodCalories: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  quantityControl: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    width: 30,
    textAlign: 'center',
  },
  nutritionSummary: {
    backgroundColor: Colors.cardAlt,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  summaryLabel: {
    fontSize: 15,
    color: Colors.text,
  },
  summaryValue: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
  },
  saveButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: Colors.background,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  saveButtonLarge: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.white,
  },
  scannerContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  mockScanner: {
    width: '100%',
    height: 250,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    position: 'relative',
    overflow: 'hidden',
  },
  scannerText: {
    color: Colors.white,
    marginTop: 12,
    fontSize: 14,
  },
  scannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scannerCorner1: {
    position: 'absolute',
    top: 20,
    left: 20,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner2: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner3: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner4: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: Colors.primary,
  },
  cancelScanButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 24,
    borderRadius: 20,
    backgroundColor: Colors.cardAlt,
  },
  cancelScanText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
  },
});
