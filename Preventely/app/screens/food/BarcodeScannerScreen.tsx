import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
  ActivityIndicator,
  Alert,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Camera } from 'expo-camera';

import Colors from '../../constants/colors';
import apiService from '../../services/api.service';

function BarcodeScannerScreen() {
  const router = useRouter();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanning, setScanning] = useState(true);
  const [scannedProduct, setScannedProduct] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Request camera permissions
  useEffect(() => {
    const getCameraPermissions = async () => {
      try {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
      } catch (error) {
        console.error('Error requesting camera permissions:', error);
        // Fallback to simulated permissions if there's an error
        setHasPermission(true);
      }
    };

    getCameraPermissions();
  }, []);

  // Handle barcode scanning
  const handleBarCodeScanned = async ({ type, data }: { type: string; data: string }) => {
    if (!scanning || loading) return;
    
    setScanning(false);
    setLoading(true);

    try {
      console.log(`Barcode scanned: ${data}`);

      // Call the backend API to get food information by barcode
      const response = await apiService.scanFoodByBarcode(data);

      if (response.success) {
        // Map the API response to the format expected by the UI
        setScannedProduct({
          name: response.data.name,
          brand: response.data.brand || 'Unknown Brand',
          servingSize: response.data.servingSize || '100g',
          calories: response.data.calories || 0,
          carbs: response.data.carbs || 0,
          protein: response.data.protein || 0,
          fat: response.data.fat || 0,
          gi: response.data.glycemicIndex || 0,
          gl: response.data.glycemicLoad || 0,
          barcode: response.data.barcode,
          image: response.data.imageUrl || null
        });
      } else {
        Alert.alert(
          'Food Not Found',
          'We couldn\'t find this food in our database. Would you like to scan again?',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: handleCancel,
            },
            {
              text: 'Scan Again',
              onPress: handleScanAgain,
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error scanning food:', error);
      Alert.alert(
        'Error',
        'There was an error scanning this food. Please try again.',
        [
          {
            text: 'OK',
            onPress: handleScanAgain,
          },
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleScanAgain = () => {
    setScannedProduct(null);
    setScanning(true);
  };

  const handleAddToMeal = () => {
    // In a real app, you would pass the product data back to the add meal screen
    router.back();
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel}>
          <Ionicons name="close" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Scan Barcode</Text>
        <View style={{ width: 24 }} />
      </View>

      {scanning && hasPermission === true && (
        <View style={styles.scannerContainer}>
          {/* Real camera-based barcode scanner */}
          <View style={styles.cameraContainer}>
            <Camera
              style={StyleSheet.absoluteFillObject}
              onBarCodeScanned={scanning ? handleBarCodeScanned : undefined}
              ratio="16:9"
            >
              <View style={styles.scannerOverlay}>
                <View style={styles.scannerCorner1} />
                <View style={styles.scannerCorner2} />
                <View style={styles.scannerCorner3} />
                <View style={styles.scannerCorner4} />
              </View>
              <View style={styles.scanLine} />
              <Text style={styles.scannerText}>Position barcode within frame</Text>
            </Camera>
          </View>
          <Text style={styles.instructionText}>
            Hold your phone so the barcode is clearly visible within the frame
          </Text>
        </View>
      )}
      
      {hasPermission === false && (
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-outline" size={60} color={Colors.error} />
          <Text style={styles.permissionText}>Camera permission is required to scan barcodes</Text>
          <TouchableOpacity 
            style={styles.primaryButton} 
            onPress={async () => {
              try {
                const { status } = await Camera.requestCameraPermissionsAsync();
                setHasPermission(status === 'granted');
              } catch (error) {
                console.error('Error requesting camera permissions:', error);
                // Fallback for testing
                setHasPermission(true);
              }
            }}
          >
            <Text style={styles.buttonText}>Request Permission</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {hasPermission === null && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Requesting camera permission...</Text>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Fetching product information...</Text>
        </View>
      )}

      {scannedProduct && (
        <View style={styles.productContainer}>
          <View style={styles.productHeader}>
            <Text style={styles.scanSuccessText}>Barcode Scanned Successfully</Text>
            <Text style={styles.barcodeText}>Barcode: {scannedProduct.barcode}</Text>
          </View>

          <View style={styles.productCard}>
            <View style={styles.productImageContainer}>
              {scannedProduct.image ? (
                <Image
                  source={{ uri: scannedProduct.image }}
                  style={styles.productImage}
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <Ionicons name="nutrition-outline" size={40} color={Colors.textSecondary} />
                </View>
              )}
            </View>

            <View style={styles.productInfo}>
              <Text style={styles.productName}>{scannedProduct.name}</Text>
              <Text style={styles.productBrand}>{scannedProduct.brand}</Text>
              <Text style={styles.productServing}>Serving: {scannedProduct.servingSize}</Text>
            </View>
          </View>

          <View style={styles.nutritionCard}>
            <Text style={styles.nutritionTitle}>Nutrition Facts</Text>

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Calories</Text>
              <Text style={styles.nutritionValue}>{scannedProduct.calories}</Text>
            </View>

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Carbs</Text>
              <Text style={styles.nutritionValue}>{scannedProduct.carbs}g</Text>
            </View>

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Protein</Text>
              <Text style={styles.nutritionValue}>{scannedProduct.protein}g</Text>
            </View>

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Fat</Text>
              <Text style={styles.nutritionValue}>{scannedProduct.fat}g</Text>
            </View>

            <View style={styles.divider} />

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Glycemic Index (GI)</Text>
              <Text
                style={[
                  styles.nutritionValue,
                  { color: scannedProduct.gi <= 55 ? Colors.success :
                          scannedProduct.gi <= 69 ? Colors.warning : Colors.error }
                ]}
              >
                {scannedProduct.gi}
              </Text>
            </View>

            <View style={styles.nutritionRow}>
              <Text style={styles.nutritionLabel}>Glycemic Load (GL)</Text>
              <Text
                style={[
                  styles.nutritionValue,
                  { color: scannedProduct.gl <= 10 ? Colors.success :
                          scannedProduct.gl <= 19 ? Colors.warning : Colors.error }
                ]}
              >
                {scannedProduct.gl}
              </Text>
            </View>
          </View>

          <View style={styles.giInfoCard}>
            <Ionicons name="information-circle-outline" size={20} color={Colors.primary} />
            <Text style={styles.giInfoText}>
              This product has a {scannedProduct.gi <= 55 ? 'low' :
                                  scannedProduct.gi <= 69 ? 'medium' : 'high'} glycemic index,
              which means it will have a {scannedProduct.gi <= 55 ? 'minimal' :
                                         scannedProduct.gi <= 69 ? 'moderate' : 'significant'} impact
              on your blood sugar levels.
            </Text>
          </View>
        </View>
      )}

      <View style={styles.actionBar}>
        {scannedProduct ? (
          <>
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={handleScanAgain}
            >
              <Ionicons name="scan-outline" size={20} color={Colors.primary} />
              <Text style={styles.secondaryButtonText}>Scan Again</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.primaryButton]}
              onPress={handleAddToMeal}
            >
              <Ionicons name="add-circle-outline" size={20} color={Colors.white} />
              <Text style={styles.buttonText}>Add to Meal</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton, { flex: 1 }]}
            onPress={handleCancel}
          >
            <Text style={styles.secondaryButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  cameraContainer: {
    width: '100%',
    height: 350,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  barcodeIcon: {
    opacity: 0.5,
    marginTop: 20,
  },
  testButtonsContainer: {
    marginTop: 20,
    width: '100%',
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: Colors.primary,
    marginTop: 10,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionText: {
    fontSize: 16,
    color: Colors.text,
    textAlign: 'center',
    marginVertical: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  scannerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  scanLine: {
    position: 'absolute',
    height: 2,
    width: '80%',
    backgroundColor: Colors.primary,
    top: '50%',
    left: '10%',
    opacity: 0.8,
  },
  scannerText: {
    color: Colors.white,
    marginTop: 12,
    fontSize: 14,
  },
  scannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  scannerCorner1: {
    position: 'absolute',
    top: 40,
    left: 40,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner2: {
    position: 'absolute',
    top: 40,
    right: 40,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner3: {
    position: 'absolute',
    bottom: 40,
    left: 40,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: Colors.primary,
  },
  scannerCorner4: {
    position: 'absolute',
    bottom: 40,
    right: 40,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: Colors.primary,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 24,
    paddingHorizontal: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.text,
    marginTop: 16,
  },
  productContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  productHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  scanSuccessText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.success,
    marginBottom: 4,
  },
  barcodeText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  productCard: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  productImageContainer: {
    width: 80,
    height: 80,
    marginRight: 16,
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    backgroundColor: Colors.cardAlt,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  productServing: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  nutritionCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  nutritionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  nutritionLabel: {
    fontSize: 15,
    color: Colors.text,
  },
  nutritionValue: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 8,
  },
  giInfoCard: {
    flexDirection: 'row',
    backgroundColor: `${Colors.primary}10`,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  giInfoText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.background,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    flex: 1,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: Colors.lightGray,
    marginRight: 8,
  },
  button: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  buttonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.primary,
    marginLeft: 8,
  },
});

export default BarcodeScannerScreen;
