const Colors = {
  // Primary colors
  primary: '#6366F1', // Indigo/purple from the inspiration
  secondary: '#FFCC33', // Warm yellow/gold from the inspiration
  tertiary: '#6495ED', // Soft blue
  
  // Background colors
  background: '#F1EFFF', // Light purple background from the inspiration
  card: '#FFFFFF',
  cardAlt: '#F0F2F5', // Slightly darker card for contrast
  purpleCard: '#6366F1', // Purple card background
  yellowCard: '#FFF8E6', // Light yellow card background
  
  // Text colors
  text: '#333333',
  textSecondary: '#666666',
  textLight: '#999999',
  textPrimary: '#333333', // Same as text, added for consistency
  textPurple: '#6366F1', // Purple text
  textYellow: '#FFCC33', // Yellow/gold text
  
  // UI elements
  border: '#EEEEEE',
  shadow: 'rgba(0, 0, 0, 0.05)',
  white: '#FFFFFF',
  lightBackground: '#F0F2F5', // Light background for inputs
  lightGray: '#CCCCCC', // Light gray for disabled elements
  
  // Status colors
  success: '#4BB543',
  warning: '#FFCC33', // Using the gold color for warnings
  error: '#FF6B6B',
  info: '#3498DB', // Blue for informational elements
  
  // Nutrition tracking
  carbs: '#FFCC33', // Yellow/gold for carbs
  protein: '#6366F1', // Purple for protein
  fat: '#38B000', // Green for fat
};


export default Colors;
