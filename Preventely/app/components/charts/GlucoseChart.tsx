import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../../constants/colors';
import { LinearGradient } from 'expo-linear-gradient';

// Dummy CGM data (glucose readings in mg/dL over time)
const glucoseData = [
  { time: '6:00 AM', value: 95, meal: false },
  { time: '7:30 AM', value: 120, meal: true, mealName: 'Breakfast' },
  { time: '10:00 AM', value: 105, meal: false },
  { time: '12:30 PM', value: 135, meal: true, mealName: 'Lunch' },
  { time: '2:00 PM', value: 110, meal: false },
  { time: '4:00 PM', value: 98, meal: true, mealName: 'Snack' },
  { time: '6:00 PM', value: 125, meal: true, mealName: 'Dinner' },
  { time: '8:00 PM', value: 115, meal: false },
];

// Time periods for filtering
const timePeriods = [
  { id: 'today', label: 'Today' },
  { id: 'week', label: 'Week' },
  { id: 'month', label: 'Month' },
];

// Target range
const targetMin = 70;
const targetMax = 140;

// Chart dimensions
const CHART_WIDTH = Dimensions.get('window').width - 48;
const CHART_HEIGHT = 150;
const PADDING = 20;
const GRAPH_HEIGHT = CHART_HEIGHT - (PADDING * 2);
const GRAPH_WIDTH = CHART_WIDTH - (PADDING * 2);

const GlucoseChart = () => {
  const [activePeriod, setActivePeriod] = React.useState('today');
  // Calculate the min and max values for scaling
  const maxValue = Math.max(...glucoseData.map(d => d.value), targetMax);
  const minValue = Math.min(...glucoseData.map(d => d.value), targetMin);
  const valueRange = maxValue - minValue;

  // Calculate the current glucose value (latest reading)
  const currentGlucose = glucoseData[glucoseData.length - 1].value;
  
  // Determine status based on current glucose
  let status = 'Normal';
  let statusColor = Colors.success;
  
  if (currentGlucose > targetMax) {
    status = 'High';
    statusColor = Colors.error;
  } else if (currentGlucose < targetMin) {
    status = 'Low';
    statusColor = Colors.warning;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Glucose</Text>
          <Text style={styles.subtitle}>Real-time monitoring</Text>
        </View>
        <View style={styles.periodSelector}>
          {timePeriods.map(period => (
            <TouchableOpacity 
              key={period.id}
              style={[styles.periodButton, activePeriod === period.id && styles.activePeriodButton]}
              onPress={() => setActivePeriod(period.id)}
            >
              <Text 
                style={[styles.periodButtonText, activePeriod === period.id && styles.activePeriodButtonText]}
              >
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <View style={styles.glucoseInfoContainer}>
        <View style={styles.currentReadingContainer}>
          <View style={styles.readingValue}>
            <Text style={styles.currentValue}>{currentGlucose}</Text>
            <Text style={styles.unit}>mg/dL</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
            <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
            <Text style={[styles.statusText, { color: statusColor }]}>{status}</Text>
          </View>
        </View>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>118</Text>
            <Text style={styles.statLabel}>Average</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>142</Text>
            <Text style={styles.statLabel}>Peak</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>92</Text>
            <Text style={styles.statLabel}>Low</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.chartContainer}>
        {/* Target range zone */}
        <View 
          style={[
            styles.targetZone, 
            {
              top: PADDING + GRAPH_HEIGHT - ((targetMax - minValue) / valueRange) * GRAPH_HEIGHT,
              height: ((targetMax - targetMin) / valueRange) * GRAPH_HEIGHT
            }
          ]} 
        />
        
        {/* Time labels */}
        <View style={styles.timeLabels}>
          {glucoseData.map((data, index) => (
            <Text 
              key={`time-${index}`} 
              style={[
                styles.timeLabel,
                { left: (index / (glucoseData.length - 1)) * GRAPH_WIDTH }
              ]}
            >
              {index % 2 === 0 ? data.time.split(' ')[0] : ''}
            </Text>
          ))}
        </View>
        
        {/* Value labels */}
        <View style={styles.valueLabels}>
          <Text style={styles.valueLabel}>{maxValue}</Text>
          <Text style={styles.valueLabel}>{targetMax}</Text>
          <Text style={styles.valueLabel}>{targetMin}</Text>
          <Text style={styles.valueLabel}>{minValue}</Text>
        </View>
        
        {/* Area chart with gradient */}
        <View style={styles.areaChart}>
          <LinearGradient
            colors={[`${Colors.primary}30`, 'transparent']}
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              borderRadius: 12,
            }}
          />
          
          {/* Line chart with curved path */}
          <View style={styles.lineChart}>
            {glucoseData.map((data, index) => {
              if (index === 0) return null;
              
              const prevData = glucoseData[index - 1];
              const prevX = ((index - 1) / (glucoseData.length - 1)) * GRAPH_WIDTH;
              const prevY = GRAPH_HEIGHT - ((prevData.value - minValue) / valueRange) * GRAPH_HEIGHT;
              const currX = (index / (glucoseData.length - 1)) * GRAPH_WIDTH;
              const currY = GRAPH_HEIGHT - ((data.value - minValue) / valueRange) * GRAPH_HEIGHT;
              
              return (
                <View 
                  key={`line-${index}`}
                  style={[
                    styles.line,
                    {
                      left: prevX,
                      top: prevY,
                      width: Math.sqrt(Math.pow(currX - prevX, 2) + Math.pow(currY - prevY, 2)),
                      transform: [
                        { 
                          rotate: `${Math.atan2(currY - prevY, currX - prevX) * (180 / Math.PI)}deg` 
                        },
                        { translateY: (currY - prevY) / 2 }
                      ]
                    }
                  ]}
                />
              );
            })}
          </View>
          
          {/* Data points */}
          {glucoseData.map((data, index) => {
            const x = (index / (glucoseData.length - 1)) * GRAPH_WIDTH;
            const y = GRAPH_HEIGHT - ((data.value - minValue) / valueRange) * GRAPH_HEIGHT;
            
            return (
              <React.Fragment key={`point-group-${index}`}>
                {/* Meal indicators */}
                {data.meal && (
                  <View 
                    style={[
                      styles.mealIndicator,
                      {
                        left: x,
                        top: y - 30,
                      }
                    ]}
                  >
                    <Ionicons name="restaurant-outline" size={12} color={Colors.textSecondary} />
                    <Text style={styles.mealText}>{data.mealName}</Text>
                  </View>
                )}
                
                {/* Outer glow effect */}
                <View 
                  style={[
                    styles.dataPointGlow,
                    {
                      left: x - 8,
                      top: y - 8,
                      backgroundColor: 
                        data.value > targetMax ? `${Colors.error}30` :
                        data.value < targetMin ? `${Colors.warning}30` :
                        `${Colors.success}30`
                    }
                  ]}
                />
                
                {/* Inner point */}
                <View 
                  style={[
                    styles.dataPoint,
                    {
                      left: x - 4,
                      top: y - 4,
                      backgroundColor: 
                        data.value > targetMax ? Colors.error :
                        data.value < targetMin ? Colors.warning :
                        Colors.success,
                      borderWidth: data.meal ? 2 : 0,
                    }
                  ]}
                />
              </React.Fragment>
            );
          })}
        </View>
      </View>
      
      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: Colors.success }]} />
          <Text style={styles.legendText}>Normal</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: Colors.warning }]} />
          <Text style={styles.legendText}>Low</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: Colors.error }]} />
          <Text style={styles.legendText}>High</Text>
        </View>
        <View style={styles.legendItem}>
          <Ionicons name="restaurant-outline" size={12} color={Colors.textSecondary} />
          <Text style={styles.legendText}>Meal</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: `${Colors.primary}10`,
    borderRadius: 20,
    padding: 2,
  },
  periodButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 18,
  },
  activePeriodButton: {
    backgroundColor: Colors.primary,
  },
  periodButtonText: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  activePeriodButtonText: {
    color: '#FFFFFF',
  },
  glucoseInfoContainer: {
    marginBottom: 20,
  },
  currentReadingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  readingValue: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  currentValue: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.text,
  },
  unit: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 4,
    marginBottom: 6,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: `${Colors.primary}08`,
    borderRadius: 12,
    padding: 12,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: `${Colors.border}80`,
    marginHorizontal: 8,
  },
  chartContainer: {
    height: CHART_HEIGHT,
    width: '100%',
    position: 'relative',
    marginBottom: 16,
  },
  targetZone: {
    position: 'absolute',
    left: PADDING,
    width: GRAPH_WIDTH,
    backgroundColor: 'rgba(74, 222, 128, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(74, 222, 128, 0.3)',
    borderStyle: 'dashed',
    borderRadius: 4,
  },
  timeLabels: {
    position: 'absolute',
    bottom: 0,
    left: PADDING,
    right: PADDING,
    height: 20,
  },
  timeLabel: {
    position: 'absolute',
    fontSize: 10,
    color: Colors.textLight,
    textAlign: 'center',
    transform: [{ translateX: -15 }],
  },
  valueLabels: {
    position: 'absolute',
    top: PADDING,
    bottom: PADDING,
    left: 0,
    width: PADDING,
    justifyContent: 'space-between',
  },
  valueLabel: {
    fontSize: 10,
    color: Colors.textLight,
    textAlign: 'right',
  },
  areaChart: {
    position: 'absolute',
    top: PADDING,
    left: PADDING,
    width: GRAPH_WIDTH,
    height: GRAPH_HEIGHT,
    borderRadius: 12,
    overflow: 'hidden',
  },
  lineChart: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  line: {
    position: 'absolute',
    height: 3,
    backgroundColor: Colors.primary,
    borderRadius: 1.5,
  },
  dataPointGlow: {
    position: 'absolute',
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderColor: Colors.card,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
  mealIndicator: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    transform: [{ translateX: -20 }],
  },
  mealText: {
    fontSize: 9,
    color: Colors.textSecondary,
    marginLeft: 2,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: Colors.textSecondary,
  },
});


export default GlucoseChart;
