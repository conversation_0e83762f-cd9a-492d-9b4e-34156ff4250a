import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Colors  from '../../constants/colors';
import { Ionicons } from '@expo/vector-icons';

// Dummy weight data (in kg)
const weightData = [
  { date: 'Apr 10', value: 78.2, bmi: 24.1 },
  { date: 'Apr 17', value: 77.8, bmi: 24.0 },
  { date: 'Apr 24', value: 77.5, bmi: 23.9 },
  { date: 'May 1', value: 76.9, bmi: 23.7 },
  { date: 'May 8', value: 76.5, bmi: 23.6 },
];

// Chart dimensions
const CHART_WIDTH = Dimensions.get('window').width - 48;
const CHART_HEIGHT = 150;
const PADDING = 20;
const GRAPH_HEIGHT = CHART_HEIGHT - (PADDING * 2);
const GRAPH_WIDTH = CHART_WIDTH - (PADDING * 2);
const BAR_WIDTH = 30;

type WeightChartProps = {
  onViewDetails?: () => void;
};

const WeightChart: React.FC<WeightChartProps> = ({ onViewDetails }) => {
  // Calculate the min and max values for scaling
  const maxValue = Math.max(...weightData.map(d => d.value)) + 1;
  const minValue = Math.min(...weightData.map(d => d.value)) - 1;
  const valueRange = maxValue - minValue;

  // Calculate the current weight and BMI (latest reading)
  const currentWeight = weightData[weightData.length - 1].value;
  const currentBMI = weightData[weightData.length - 1].bmi;
  
  // Determine BMI category
  let bmiCategory = '';
  let bmiColor = '';
  
  if (currentBMI < 18.5) {
    bmiCategory = 'Underweight';
    bmiColor = Colors.warning;
  } else if (currentBMI >= 18.5 && currentBMI < 25) {
    bmiCategory = 'Healthy';
    bmiColor = Colors.success;
  } else if (currentBMI >= 25 && currentBMI < 30) {
    bmiCategory = 'Overweight';
    bmiColor = Colors.warning;
  } else {
    bmiCategory = 'Obese';
    bmiColor = Colors.error;
  }

  // Calculate weight change
  const weightChange = currentWeight - weightData[0].value;
  const weightChangeText = weightChange.toFixed(1);
  const weightChangeSymbol = weightChange < 0 ? '↓' : '↑';
  const weightChangeColor = weightChange < 0 ? Colors.success : Colors.error;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Weight & BMI</Text>
          <Text style={styles.subtitle}>Last 30 days</Text>
        </View>
        <TouchableOpacity 
          style={styles.viewDetailsButton}
          onPress={onViewDetails}
        >
          <Text style={styles.viewDetailsText}>Details</Text>
          <Ionicons name="chevron-forward" size={16} color={Colors.primary} />
        </TouchableOpacity>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{currentWeight.toFixed(1)}</Text>
            <Text style={styles.statUnit}>kg</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{currentBMI.toFixed(1)}</Text>
            <Text style={styles.statUnit}>BMI</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <Text style={[styles.infoValue, { color: bmiColor }]}>{bmiCategory}</Text>
          <Text style={styles.infoLabel}>BMI Category</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={[styles.infoValue, { color: weightChangeColor }]}>
            {weightChangeSymbol} {Math.abs(weightChange).toFixed(1)} kg
          </Text>
          <Text style={styles.infoLabel}>30-Day Change</Text>
        </View>
      </View>
      
      <View style={styles.chartContainer}>
        {/* Date labels */}
        <View style={styles.dateLabels}>
          {weightData.map((data, index) => (
            <Text 
              key={`date-${index}`} 
              style={[
                styles.dateLabel,
                { 
                  left: (index / (weightData.length - 1)) * GRAPH_WIDTH,
                  transform: [{ translateX: -15 }]
                }
              ]}
            >
              {data.date}
            </Text>
          ))}
        </View>
        
        {/* Value labels */}
        <View style={styles.valueLabels}>
          <Text style={styles.valueLabel}>{maxValue.toFixed(1)}</Text>
          <Text style={styles.valueLabel}>{((maxValue + minValue) / 2).toFixed(1)}</Text>
          <Text style={styles.valueLabel}>{minValue.toFixed(1)}</Text>
        </View>
        
        {/* Bar chart */}
        <View style={styles.barChart}>
          {weightData.map((data, index) => {
            const x = (index / (weightData.length - 1)) * GRAPH_WIDTH;
            const height = ((data.value - minValue) / valueRange) * GRAPH_HEIGHT;
            const y = GRAPH_HEIGHT - height;
            
            return (
              <View 
                key={`bar-${index}`}
                style={[
                  styles.bar,
                  {
                    left: x - (BAR_WIDTH / 2),
                    top: y,
                    height: height,
                  }
                ]}
              />
            );
          })}
        </View>
        
        {/* Line for trend */}
        <View style={styles.trendLine}>
          {weightData.map((data, index) => {
            if (index === 0) return null;
            
            const prevData = weightData[index - 1];
            const prevX = ((index - 1) / (weightData.length - 1)) * GRAPH_WIDTH;
            const prevY = GRAPH_HEIGHT - ((prevData.value - minValue) / valueRange) * GRAPH_HEIGHT;
            const currX = (index / (weightData.length - 1)) * GRAPH_WIDTH;
            const currY = GRAPH_HEIGHT - ((data.value - minValue) / valueRange) * GRAPH_HEIGHT;
            
            return (
              <View 
                key={`line-${index}`}
                style={[
                  styles.line,
                  {
                    left: prevX,
                    top: prevY,
                    width: Math.sqrt(Math.pow(currX - prevX, 2) + Math.pow(currY - prevY, 2)),
                    transform: [
                      { 
                        rotate: `${Math.atan2(currY - prevY, currX - prevX) * (180 / Math.PI)}deg` 
                      },
                      { translateY: (currY - prevY) / 2 }
                    ]
                  }
                ]}
              />
            );
          })}
          
          {/* Data points */}
          {weightData.map((data, index) => {
            const x = (index / (weightData.length - 1)) * GRAPH_WIDTH;
            const y = GRAPH_HEIGHT - ((data.value - minValue) / valueRange) * GRAPH_HEIGHT;
            
            return (
              <View 
                key={`point-${index}`}
                style={[
                  styles.dataPoint,
                  {
                    left: x - 4,
                    top: y - 4,
                  }
                ]}
              />
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  viewDetailsButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: `${Colors.primary}10`,
  },
  viewDetailsText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.primary,
    marginRight: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  statUnit: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 4,
    marginBottom: 2,
  },
  statDivider: {
    width: 1,
    height: 20,
    backgroundColor: Colors.border,
    marginHorizontal: 12,
  },
  infoContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  chartContainer: {
    height: CHART_HEIGHT,
    width: '100%',
    position: 'relative',
  },
  dateLabels: {
    position: 'absolute',
    bottom: 0,
    left: PADDING,
    right: PADDING,
    height: 20,
  },
  dateLabel: {
    position: 'absolute',
    fontSize: 10,
    color: Colors.textLight,
    textAlign: 'center',
  },
  valueLabels: {
    position: 'absolute',
    top: PADDING,
    bottom: PADDING,
    left: 0,
    width: PADDING,
    justifyContent: 'space-between',
  },
  valueLabel: {
    fontSize: 10,
    color: Colors.textLight,
    textAlign: 'right',
  },
  barChart: {
    position: 'absolute',
    top: PADDING,
    left: PADDING,
    width: GRAPH_WIDTH,
    height: GRAPH_HEIGHT,
  },
  bar: {
    position: 'absolute',
    width: BAR_WIDTH,
    backgroundColor: `${Colors.secondary}40`,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  trendLine: {
    position: 'absolute',
    top: PADDING,
    left: PADDING,
    width: GRAPH_WIDTH,
    height: GRAPH_HEIGHT,
  },
  line: {
    position: 'absolute',
    height: 2,
    backgroundColor: Colors.secondary,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.secondary,
    borderWidth: 1,
    borderColor: Colors.card,
  },
});


export default WeightChart;
