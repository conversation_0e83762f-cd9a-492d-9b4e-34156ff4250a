import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../constants/colors';

interface Reminder {
  id: string;
  title: string;
  time: string;
  type: 'medication' | 'measurement' | 'activity' | 'appointment';
  completed: boolean;
}

interface ReminderCardProps {
  reminders: <PERSON>minder[];
  onToggleReminder: (id: string) => void;
  onViewAll: () => void;
}

const ReminderCard = ({ reminders, onToggleReminder, onViewAll }: ReminderCardProps) => {
  const getIconForType = (type: Reminder['type']) => {
    switch (type) {
      case 'medication':
        return 'medical-outline';
      case 'measurement':
        return 'analytics-outline';
      case 'activity':
        return 'fitness-outline';
      case 'appointment':
        return 'calendar-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getPendingCount = () => {
    return reminders.filter(reminder => !reminder.completed).length;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Today's Reminders</Text>
          <Text style={styles.subtitle}>{getPendingCount()} pending</Text>
        </View>
        <TouchableOpacity style={styles.viewAllButton} onPress={onViewAll}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.remindersContainer}>
        {reminders.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="checkmark-circle-outline" size={40} color={Colors.success} />
            <Text style={styles.emptyText}>All caught up for today!</Text>
          </View>
        ) : (
          reminders.map((reminder) => (
            <View key={reminder.id} style={styles.reminderItem}>
              <TouchableOpacity 
                style={[
                  styles.checkbox, 
                  reminder.completed && styles.checkboxCompleted
                ]}
                onPress={() => onToggleReminder(reminder.id)}
              >
                {reminder.completed && (
                  <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>
              
              <View style={styles.reminderIconContainer}>
                <Ionicons 
                  name={getIconForType(reminder.type)} 
                  size={20} 
                  color={Colors.primary} 
                />
              </View>
              
              <View style={styles.reminderContent}>
                <Text 
                  style={[
                    styles.reminderTitle,
                    reminder.completed && styles.reminderTitleCompleted
                  ]}
                >
                  {reminder.title}
                </Text>
                <Text style={styles.reminderTime}>{reminder.time}</Text>
              </View>
            </View>
          ))
        )}
      </View>
      
      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={20} color="#FFFFFF" />
        <Text style={styles.addButtonText}>Add Reminder</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  viewAllButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  remindersContainer: {
    marginBottom: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 8,
  },
  reminderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.primary,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxCompleted: {
    backgroundColor: Colors.primary,
  },
  reminderIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  reminderContent: {
    flex: 1,
  },
  reminderTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  reminderTitleCompleted: {
    textDecorationLine: 'line-through',
    color: Colors.textLight,
  },
  reminderTime: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 12,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    marginLeft: 8,
  },
});


export default ReminderCard;
