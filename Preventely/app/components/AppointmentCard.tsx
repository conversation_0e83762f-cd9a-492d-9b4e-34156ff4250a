import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import Colors  from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';

interface AppointmentCardProps {
  doctor: {
    name: string;
    specialty: string;
    image: any;
  };
  date: string;
  time: string;
  onPress?: () => void;
}

const AppointmentCard = ({ 
  doctor, 
  date, 
  time, 
  onPress 
}: AppointmentCardProps) => {
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {doctor.image ? (
          <Image source={doctor.image} style={styles.image} />
        ) : (
          <View style={[styles.image, styles.placeholderImage]}>
            <Ionicons name="person" size={20} color="#FFFFFF" />
          </View>
        )}
        <View style={styles.details}>
          <Text style={styles.name}>{doctor.name}</Text>
          <Text style={styles.specialty}>{doctor.specialty}</Text>
          <View style={styles.dateTimeContainer}>
            <View style={styles.iconTextContainer}>
              <Ionicons name="calendar-outline" size={14} color={Colors.textSecondary} />
              <Text style={styles.dateTime}>{date}</Text>
            </View>
            <View style={styles.iconTextContainer}>
              <Ionicons name="time-outline" size={14} color={Colors.textSecondary} />
              <Text style={styles.dateTime}>{time}</Text>
            </View>
          </View>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors?.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  details: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  specialty: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 6,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  dateTime: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  placeholderImage: {
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


export default AppointmentCard;
