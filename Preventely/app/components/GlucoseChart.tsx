import React, { useState } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Define the CGM reading interface
interface CGMReading {
  time: string;
  value: number;
  trend?: string;
  timestamp?: string; // ISO string for actual date/time (optional)
}

// Define the component props
interface GlucoseChartProps {
  readings: CGMReading[];
  targetMin?: number;
  targetMax?: number;
  latestReading?: {
    value: number;
    trend: string;
    time: string;
  };
  statistics?: {
    averageGlucose: number;
    timeInRange: number;
    timeAboveRange: number;
    timeBelowRange: number;
    standardDeviation?: number;
  };
  period?: 'day' | 'week' | 'month';
  onPeriodChange?: (period: 'day' | 'week' | 'month') => void;
  isLoading?: boolean;
}

const GlucoseChart: React.FC<GlucoseChartProps> = ({
  readings,
  targetMin = 70,
  targetMax = 140,
  latestReading,
  statistics,
  period = 'day',
  onPeriodChange,
  isLoading = false
}) => {
  const [activePeriod, setActivePeriod] = useState<'day' | 'week' | 'month'>(period);
  // Get the highest and lowest values for scaling
  const maxValue = Math.max(...readings.map(r => r.value), targetMax + 20);
  const minValue = Math.min(...readings.map(r => r.value), targetMin - 20);
  const range = maxValue - minValue;

  // Get trend icon
  const getTrendIcon = (trendValue: string = 'stable') => {
    switch (trendValue) {
      case 'rising':
      case 'singleUp':
      case 'doubleUp':
        return <Ionicons name="arrow-up" size={24} color="#F44336" />;
      case 'falling':
      case 'singleDown':
      case 'doubleDown':
        return <Ionicons name="arrow-down" size={24} color="#4CAF50" />;
      case 'slightlyRising':
      case 'slightlyFalling':
        return <Ionicons name="remove" size={24} color="#FFC107" />;
      default:
        return <Ionicons name="remove" size={24} color="#6B5CE5" />;
    }
  };
  
  // Handle period change
  const handlePeriodChange = (newPeriod: 'day' | 'week' | 'month') => {
    setActivePeriod(newPeriod);
    if (onPeriodChange) {
      onPeriodChange(newPeriod);
    }
  };

  // Get color based on glucose value
  const getColor = (value: number) => {
    if (value > targetMax) return '#F44336'; // Red for high
    if (value < targetMin) return '#FFC107'; // Yellow for low
    return '#4CAF50'; // Green for in range
  };

  // Calculate time in range percentage
  const calculateTimeInRange = () => {
    if (!readings.length) return 0;
    const inRangeCount = readings.filter(r => r.value >= targetMin && r.value <= targetMax).length;
    return Math.round((inRangeCount / readings.length) * 100);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Glucose Trend</Text>
        <View style={styles.periodSelector}>
          <TouchableOpacity 
            style={[styles.periodButton, activePeriod === 'day' && styles.activePeriod]} 
            onPress={() => handlePeriodChange('day')}
          >
            <Text style={[styles.periodText, activePeriod === 'day' && styles.activePeriodText]}>Day</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.periodButton, activePeriod === 'week' && styles.activePeriod]} 
            onPress={() => handlePeriodChange('week')}
          >
            <Text style={[styles.periodText, activePeriod === 'week' && styles.activePeriodText]}>Week</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.periodButton, activePeriod === 'month' && styles.activePeriod]} 
            onPress={() => handlePeriodChange('month')}
          >
            <Text style={[styles.periodText, activePeriod === 'month' && styles.activePeriodText]}>Month</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {/* Latest reading display */}
      {latestReading && (
        <View style={styles.latestReadingContainer}>
          <View style={styles.latestReadingValue}>
            <Text style={styles.latestGlucose}>{latestReading.value}</Text>
            <Text style={styles.mgdl}>mg/dL</Text>
          </View>
          <View style={styles.latestReadingInfo}>
            <View style={styles.trendContainer}>
              {getTrendIcon(latestReading.trend)}
              <Text style={styles.trendText}>{latestReading.trend.replace(/([A-Z])/g, ' $1').trim()}</Text>
            </View>
            <Text style={styles.latestReadingTime}>{latestReading.time}</Text>
          </View>
        </View>
      )}
      
      {/* Statistics summary */}
      {statistics && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statistics.averageGlucose}</Text>
            <Text style={styles.statLabel}>Average</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statistics.timeInRange}%</Text>
            <Text style={styles.statLabel}>In Range</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{statistics.standardDeviation || '-'}</Text>
            <Text style={styles.statLabel}>Std Dev</Text>
          </View>
        </View>
      )}

      {readings.length > 0 ? (
        <View style={styles.chartContainer}>
          {/* Target range zone */}
          <View 
            style={[
              styles.targetRange, 
              {
                top: `${100 - ((targetMax - minValue) / range) * 100}%`,
                height: `${((targetMax - targetMin) / range) * 100}%`
              }
            ]} 
          />
          
          {/* Glucose readings */}
          <View style={styles.readingsContainer}>
            {readings.map((reading, index) => {
              const heightPercent = ((reading.value - minValue) / range) * 100;
              return (
                <View key={index} style={styles.readingColumn}>
                  <View 
                    style={[
                      styles.bar, 
                      { 
                        height: `${heightPercent}%`,
                        backgroundColor: getColor(reading.value)
                      }
                    ]} 
                  />
                  <Text style={styles.timeLabel}>{reading.time}</Text>
                </View>
              );
            })}
          </View>
          
          {/* Y-axis labels */}
          <View style={styles.yAxisLabels}>
            <Text style={styles.yAxisLabel}>{maxValue}</Text>
            <Text style={styles.yAxisLabel}>{targetMax}</Text>
            <Text style={styles.yAxisLabel}>{targetMin}</Text>
            <Text style={styles.yAxisLabel}>{minValue}</Text>
          </View>
        </View>
      ) : (
        <View style={styles.noDataContainer}>
          <Ionicons name="analytics-outline" size={48} color="#CCCCCC" />
          <Text style={styles.noDataText}>No glucose data available</Text>
        </View>
      )}
      
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#F44336' }]} />
          <Text style={styles.legendText}>High</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#4CAF50' }]} />
          <Text style={styles.legendText}>Normal</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#FFC107' }]} />
          <Text style={styles.legendText}>Low</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    padding: 15,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    padding: 2,
  },
  periodButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 18,
  },
  activePeriod: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  periodText: {
    fontSize: 12,
    color: '#666666',
  },
  activePeriodText: {
    color: '#333333',
    fontWeight: 'bold',
  },
  latestReadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9F9F9',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  latestReadingValue: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  latestGlucose: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333333',
  },
  mgdl: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 5,
  },
  latestReadingInfo: {
    alignItems: 'flex-end',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 5,
  },
  latestReadingTime: {
    fontSize: 12,
    color: '#999999',
    marginTop: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 5,
  },
  timeInRange: {
    marginLeft: 5,
    fontSize: 14,
    color: '#666666',
  },
  chartContainer: {
    height: 200,
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 10,
    paddingLeft: 30, // Space for y-axis labels
    position: 'relative',
  },
  targetRange: {
    position: 'absolute',
    left: 30,
    right: 0,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  readingsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
  },
  readingColumn: {
    flex: 1,
    alignItems: 'center',
    height: '100%',
    justifyContent: 'flex-end',
  },
  bar: {
    width: 10,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  timeLabel: {
    fontSize: 10,
    color: '#999999',
    marginTop: 5,
    transform: [{ rotate: '-45deg' }],
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingRight: 5,
  },
  yAxisLabel: {
    fontSize: 10,
    color: '#999999',
    textAlign: 'right',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 5,
  },
  legendText: {
    fontSize: 12,
    color: '#666666',
  },
  noDataContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    marginTop: 10,
    color: '#999999',
    fontSize: 14,
  },
});

export default GlucoseChart;
