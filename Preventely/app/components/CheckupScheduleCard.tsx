import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Colors from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';

interface CheckupScheduleCardProps {
  date: {
    month: string;
    day: string;
  };
  type: string;
  doctor: {
    name: string;
    image?: any;
  };
  onPress?: () => void;
}

const CheckupScheduleCard = ({ 
  date, 
  type, 
  doctor,
  onPress 
}: CheckupScheduleCardProps) => {
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.dateContainer}>
        <Ionicons name="calendar-outline" size={20} color={Colors.textSecondary} style={styles.icon} />
        <View>
          <Text style={styles.month}>{date.month}</Text>
          <Text style={styles.day}>{date.day}</Text>
        </View>
      </View>
      <View style={styles.content}>
        <Text style={styles.type}>{type}</Text>
        <View style={styles.doctorInfo}>
          <Ionicons name="person-outline" size={16} color={Colors.textSecondary} />
          <Text style={styles.doctorName}>Dr. {doctor.name}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    width: 160,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  icon: {
    marginRight: 8,
  },
  month: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  day: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  content: {
    flex: 1,
  },
  type: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorName: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
});


export default CheckupScheduleCard;
