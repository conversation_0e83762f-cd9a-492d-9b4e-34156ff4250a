import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Colors  from '../../constants/colors';

// Define weight data point type
type WeightDataPoint = {
  date: string;
  weight: number;
  bmi?: number;
};

// Define weight chart props
type WeightChartProps = {
  data: WeightDataPoint[];
  period: 'day' | 'week' | 'month';
  goalWeight?: number;
};

// Dummy data for weight readings
const dummyWeekData: WeightDataPoint[] = [
  { date: 'Mon', weight: 77.2, bmi: 23.8 },
  { date: 'Tue', weight: 77.0, bmi: 23.7 },
  { date: 'Wed', weight: 76.8, bmi: 23.7 },
  { date: 'Thu', weight: 76.8, bmi: 23.7 },
  { date: 'Fri', weight: 76.6, bmi: 23.6 },
  { date: 'Sat', weight: 76.5, bmi: 23.6 },
  { date: 'Sun', weight: 76.5, bmi: 23.6 },
];

const dummyMonthData: WeightDataPoint[] = [
  { date: 'Week 1', weight: 78.5, bmi: 24.2 },
  { date: 'Week 2', weight: 77.8, bmi: 24.0 },
  { date: 'Week 3', weight: 77.2, bmi: 23.8 },
  { date: 'Week 4', weight: 76.5, bmi: 23.6 },
];

const WeightChart: React.FC<WeightChartProps> = ({ 
  data = [], 
  period = 'week',
  goalWeight = 75
}) => {
  // Use dummy data if no data is provided
  const chartData = data.length > 0 ? data : 
    period === 'week' ? dummyWeekData : dummyMonthData;
  
  // Chart dimensions
  const CHART_WIDTH = Dimensions.get('window').width - 64;
  const CHART_HEIGHT = 150;
  const BAR_WIDTH = 20;
  
  // Find min and max weights to determine y-axis scale
  const weights = chartData.map(d => d.weight);
  const minWeight = Math.min(...weights, goalWeight) - 1;
  const maxWeight = Math.max(...weights) + 1;
  const weightRange = maxWeight - minWeight;
  
  return (
    <View style={styles.container}>
      {/* Y-axis labels */}
      <View style={styles.yAxisLabels}>
        <Text style={styles.yAxisLabel}>{maxWeight.toFixed(1)}</Text>
        <Text style={styles.yAxisLabel}>{((maxWeight + minWeight) / 2).toFixed(1)}</Text>
        <Text style={styles.yAxisLabel}>{minWeight.toFixed(1)}</Text>
      </View>
      
      <View style={styles.chartContainer}>
        {/* Goal weight line */}
        <View 
          style={[
            styles.goalLine,
            { 
              top: CHART_HEIGHT * (1 - (goalWeight - minWeight) / weightRange),
            }
          ]}
        >
          <Text style={styles.goalLabel}>Goal</Text>
        </View>
        
        {/* Horizontal grid lines */}
        <View style={[styles.gridLine, { top: 0 }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT / 2 }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT - 1 }]} />
        
        {/* Weight bars */}
        <View style={styles.barsContainer}>
          {chartData.map((item, index) => {
            const barHeight = CHART_HEIGHT * ((item.weight - minWeight) / weightRange);
            return (
              <View key={index} style={styles.barWrapper}>
                <View 
                  style={[
                    styles.bar,
                    { 
                      height: barHeight,
                      backgroundColor: item.weight <= goalWeight ? Colors.success : Colors.primary 
                    }
                  ]}
                />
                <View 
                  style={[
                    styles.dataPoint,
                    { 
                      bottom: barHeight - 4,
                    }
                  ]}
                />
                <Text style={styles.weightLabel}>{item.weight.toFixed(1)}</Text>
                <Text style={styles.dateLabel}>{item.date}</Text>
              </View>
            );
          })}
        </View>
        
        {/* Connect dots with lines */}
        <View style={styles.linesContainer}>
          {chartData.map((item, index) => {
            if (index === 0) return null;
            
            const prevItem = chartData[index - 1];
            const prevHeight = CHART_HEIGHT * ((prevItem.weight - minWeight) / weightRange);
            const currHeight = CHART_HEIGHT * ((item.weight - minWeight) / weightRange);
            
            // Calculate line properties
            const width = CHART_WIDTH / (chartData.length - 1);
            const x1 = (index - 1) * width + (BAR_WIDTH / 2);
            const y1 = CHART_HEIGHT - prevHeight;
            const x2 = index * width + (BAR_WIDTH / 2);
            const y2 = CHART_HEIGHT - currHeight;
            
            // Calculate angle and length
            const angle = Math.atan2(y2 - y1, x2 - x1) * (180 / Math.PI);
            const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            
            return (
              <View 
                key={`line-${index}`}
                style={[
                  styles.line,
                  {
                    width: length,
                    left: x1,
                    top: y1,
                    transform: [{ rotate: `${angle}deg` }]
                  }
                ]}
              />
            );
          })}
        </View>
      </View>
      
      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.primary }]} />
          <Text style={styles.legendText}>Weight</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.success }]} />
          <Text style={styles.legendText}>Goal Weight</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingVertical: 10,
    width: 35,
  },
  yAxisLabel: {
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'right',
    paddingRight: 5,
  },
  chartContainer: {
    marginLeft: 35,
    height: 150,
    position: 'relative',
  },
  goalLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: Colors.success,
    borderStyle: 'dashed',
    zIndex: 5,
  },
  goalLabel: {
    position: 'absolute',
    right: 0,
    top: -10,
    fontSize: 10,
    color: Colors.success,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: Colors.border,
    zIndex: 1,
  },
  barsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: '100%',
    paddingBottom: 20,
    zIndex: 2,
  },
  barWrapper: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.white,
    borderWidth: 2,
    borderColor: Colors.primary,
    zIndex: 3,
  },
  weightLabel: {
    position: 'absolute',
    bottom: -18,
    fontSize: 10,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  dateLabel: {
    position: 'absolute',
    bottom: -32,
    fontSize: 10,
    color: Colors.textSecondary,
  },
  linesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
  line: {
    position: 'absolute',
    height: 2,
    backgroundColor: Colors.primary,
    transformOrigin: 'left center',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
});


export default WeightChart;
