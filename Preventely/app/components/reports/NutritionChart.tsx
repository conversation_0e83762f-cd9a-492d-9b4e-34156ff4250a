import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Colors  from '../../constants/colors';

// Define nutrition data point type
type NutritionDataPoint = {
  date: string;
  gi: number;
  gl: number;
  calories?: number;
};

// Define nutrition chart props
type NutritionChartProps = {
  data: NutritionDataPoint[];
  period: 'day' | 'week' | 'month';
};

// Dummy data for nutrition
const dummyWeekData: NutritionDataPoint[] = [
  { date: 'Mon', gi: 52, gl: 14, calories: 1850 },
  { date: 'Tue', gi: 48, gl: 12, calories: 1780 },
  { date: 'Wed', gi: 45, gl: 11, calories: 1820 },
  { date: 'Thu', gi: 50, gl: 13, calories: 1900 },
  { date: 'Fri', gi: 42, gl: 10, calories: 1750 },
  { date: 'Sat', gi: 40, gl: 9, calories: 1700 },
  { date: 'Sun', gi: 43, gl: 10, calories: 1780 },
];

const dummyMonthData: NutritionDataPoint[] = [
  { date: 'Week 1', gi: 50, gl: 13, calories: 1850 },
  { date: 'Week 2', gi: 48, gl: 12, calories: 1820 },
  { date: 'Week 3', gi: 45, gl: 11, calories: 1780 },
  { date: 'Week 4', gi: 42, gl: 10, calories: 1750 },
];

const NutritionChart: React.FC<NutritionChartProps> = ({ 
  data = [], 
  period = 'week'
}) => {
  // Use dummy data if no data is provided
  const chartData = data.length > 0 ? data : 
    period === 'week' ? dummyWeekData : dummyMonthData;
  
  // Chart dimensions
  const CHART_WIDTH = Dimensions.get('window').width - 64;
  const CHART_HEIGHT = 150;
  const BAR_WIDTH = 12;
  const BAR_GAP = 4;
  const GROUP_WIDTH = (BAR_WIDTH * 2) + BAR_GAP;
  
  // Find min and max values to determine y-axis scale
  const giValues = chartData.map(d => d.gi);
  const glValues = chartData.map(d => d.gl);
  const maxGI = Math.max(...giValues) + 5;
  const maxGL = Math.max(...glValues) + 5;
  
  // GI thresholds
  const LOW_GI = 55;
  const MEDIUM_GI = 70;
  
  // GL thresholds
  const LOW_GL = 10;
  const MEDIUM_GL = 20;
  
  return (
    <View style={styles.container}>
      {/* Y-axis labels */}
      <View style={styles.yAxisLabels}>
        <Text style={styles.yAxisLabel}>{maxGI}</Text>
        <Text style={styles.yAxisLabel}>{Math.round(maxGI / 2)}</Text>
        <Text style={styles.yAxisLabel}>0</Text>
      </View>
      
      <View style={styles.chartContainer}>
        {/* GI threshold lines */}
        <View 
          style={[
            styles.thresholdLine,
            { 
              top: CHART_HEIGHT * (1 - (LOW_GI / maxGI)),
              borderColor: Colors.success,
            }
          ]}
        >
          <Text style={[styles.thresholdLabel, { color: Colors.success }]}>Low GI</Text>
        </View>
        <View 
          style={[
            styles.thresholdLine,
            { 
              top: CHART_HEIGHT * (1 - (MEDIUM_GI / maxGI)),
              borderColor: Colors.warning,
            }
          ]}
        >
          <Text style={[styles.thresholdLabel, { color: Colors.warning }]}>Medium GI</Text>
        </View>
        
        {/* Horizontal grid lines */}
        <View style={[styles.gridLine, { top: 0 }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT / 2 }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT - 1 }]} />
        
        {/* Bars */}
        <View style={styles.barsContainer}>
          {chartData.map((item, index) => {
            const giBarHeight = CHART_HEIGHT * (item.gi / maxGI);
            const glBarHeight = CHART_HEIGHT * (item.gl / maxGL);
            
            // Determine GI bar color based on value
            let giColor = Colors.error;
            if (item.gi <= LOW_GI) {
              giColor = Colors.success;
            } else if (item.gi <= MEDIUM_GI) {
              giColor = Colors.warning;
            }
            
            // Determine GL bar color based on value
            let glColor = Colors.error;
            if (item.gl <= LOW_GL) {
              glColor = Colors.success;
            } else if (item.gl <= MEDIUM_GL) {
              glColor = Colors.warning;
            }
            
            return (
              <View key={index} style={styles.barGroup}>
                {/* GI Bar */}
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar,
                      { 
                        height: giBarHeight,
                        backgroundColor: giColor,
                      }
                    ]}
                  />
                  <Text style={styles.barValue}>{item.gi}</Text>
                </View>
                
                {/* GL Bar */}
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar,
                      { 
                        height: glBarHeight,
                        backgroundColor: glColor,
                      }
                    ]}
                  />
                  <Text style={styles.barValue}>{item.gl}</Text>
                </View>
                
                <Text style={styles.dateLabel}>{item.date}</Text>
              </View>
            );
          })}
        </View>
      </View>
      
      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.success }]} />
          <Text style={styles.legendText}>Low</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.warning }]} />
          <Text style={styles.legendText}>Medium</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.error }]} />
          <Text style={styles.legendText}>High</Text>
        </View>
      </View>
      
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendBar, { backgroundColor: Colors.primary }]} />
          <Text style={styles.legendText}>GI</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendBar, { backgroundColor: Colors.secondary }]} />
          <Text style={styles.legendText}>GL</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingVertical: 10,
    width: 30,
  },
  yAxisLabel: {
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'right',
    paddingRight: 5,
  },
  chartContainer: {
    marginLeft: 30,
    height: 150,
    position: 'relative',
  },
  thresholdLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    borderWidth: 1,
    borderStyle: 'dashed',
    zIndex: 5,
  },
  thresholdLabel: {
    position: 'absolute',
    right: 0,
    top: -10,
    fontSize: 10,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: Colors.border,
    zIndex: 1,
  },
  barsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: '100%',
    paddingBottom: 30,
    zIndex: 2,
  },
  barGroup: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  barContainer: {
    alignItems: 'center',
    marginHorizontal: 2,
  },
  bar: {
    width: 12,
    borderRadius: 4,
  },
  barValue: {
    fontSize: 9,
    color: Colors.textPrimary,
    marginTop: 2,
  },
  dateLabel: {
    position: 'absolute',
    bottom: -20,
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendBar: {
    width: 8,
    height: 12,
    borderRadius: 2,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
});


export default NutritionChart;
