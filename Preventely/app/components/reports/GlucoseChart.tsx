import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Colors  from '../../constants/colors';

// Define glucose data point type
type GlucoseDataPoint = {
  time: string;
  value: number;
  meal?: string;
};

// Define glucose chart props
type GlucoseChartProps = {
  data: GlucoseDataPoint[];
  period: 'day' | 'week' | 'month';
};

// Dummy data for glucose readings
const dummyDayData: GlucoseDataPoint[] = [
  { time: '6:00', value: 110, meal: 'Breakfast' },
  { time: '9:00', value: 135 },
  { time: '12:00', value: 125, meal: 'Lunch' },
  { time: '15:00', value: 105 },
  { time: '18:00', value: 118, meal: 'Dinner' },
  { time: '21:00', value: 108 },
];

const dummyWeekData: GlucoseDataPoint[] = [
  { time: 'Mon', value: 115 },
  { time: 'Tue', value: 122 },
  { time: 'Wed', value: 118 },
  { time: 'Thu', value: 125 },
  { time: 'Fri', value: 115 },
  { time: 'Sat', value: 110 },
  { time: 'Sun', value: 112 },
];

const dummyMonthData: GlucoseDataPoint[] = [
  { time: 'Week 1', value: 118 },
  { time: 'Week 2', value: 120 },
  { time: 'Week 3', value: 115 },
  { time: 'Week 4', value: 112 },
];

const GlucoseChart: React.FC<GlucoseChartProps> = ({ 
  data = [], 
  period = 'week'
}) => {
  // Use dummy data if no data is provided
  const chartData = data.length > 0 ? data : 
    period === 'day' ? dummyDayData : 
    period === 'week' ? dummyWeekData : dummyMonthData;
  
  // Chart dimensions
  const CHART_WIDTH = Dimensions.get('window').width - 64;
  const CHART_HEIGHT = 150;
  const PADDING = 20;
  const CHART_INNER_WIDTH = CHART_WIDTH - (PADDING * 2);
  const CHART_INNER_HEIGHT = CHART_HEIGHT - (PADDING * 2);
  
  // Find min and max glucose values to determine y-axis scale
  const values = chartData.map(d => d.value);
  const minValue = Math.min(...values) - 10;
  const maxValue = Math.max(...values) + 10;
  const valueRange = maxValue - minValue;
  
  // Target ranges
  const TARGET_LOW = 70;
  const TARGET_HIGH = 140;
  
  // Calculate target range positions
  const targetLowY = CHART_HEIGHT - (((TARGET_LOW - minValue) / valueRange) * CHART_INNER_HEIGHT + PADDING);
  const targetHighY = CHART_HEIGHT - (((TARGET_HIGH - minValue) / valueRange) * CHART_INNER_HEIGHT + PADDING);
  
  return (
    <View style={styles.container}>
      {/* Y-axis labels */}
      <View style={styles.yAxisLabels}>
        <Text style={styles.yAxisLabel}>{maxValue}</Text>
        <Text style={styles.yAxisLabel}>{Math.round((maxValue + minValue) / 2)}</Text>
        <Text style={styles.yAxisLabel}>{minValue}</Text>
      </View>
      
      <View style={styles.chartContainer}>
        {/* Target range */}
        <View 
          style={[
            styles.targetRange,
            {
              top: targetHighY,
              height: targetLowY - targetHighY,
            }
          ]}
        />
        
        {/* Horizontal grid lines */}
        <View style={[styles.gridLine, { top: PADDING }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT / 2 }]} />
        <View style={[styles.gridLine, { top: CHART_HEIGHT - PADDING }]} />
        
        {/* Data points and lines */}
        <View style={styles.dataContainer}>
          {chartData.map((point, index) => {
            // Calculate point position
            const x = (index / (chartData.length - 1)) * CHART_INNER_WIDTH + PADDING;
            const y = CHART_HEIGHT - (((point.value - minValue) / valueRange) * CHART_INNER_HEIGHT + PADDING);
            
            // Calculate line to next point if not the last point
            let lineTo = null;
            if (index < chartData.length - 1) {
              const nextPoint = chartData[index + 1];
              const nextX = ((index + 1) / (chartData.length - 1)) * CHART_INNER_WIDTH + PADDING;
              const nextY = CHART_HEIGHT - (((nextPoint.value - minValue) / valueRange) * CHART_INNER_HEIGHT + PADDING);
              
              // Calculate line properties
              const angle = Math.atan2(nextY - y, nextX - x) * (180 / Math.PI);
              const length = Math.sqrt(Math.pow(nextX - x, 2) + Math.pow(nextY - y, 2));
              
              lineTo = (
                <View 
                  style={[
                    styles.line,
                    {
                      width: length,
                      left: x,
                      top: y,
                      transform: [{ rotate: `${angle}deg` }]
                    }
                  ]}
                />
              );
            }
            
            return (
              <React.Fragment key={index}>
                {lineTo}
                <View 
                  style={[
                    styles.dataPoint,
                    { 
                      left: x - 4, 
                      top: y - 4,
                      backgroundColor: point.meal ? Colors.warning : Colors.primary
                    }
                  ]}
                />
                {point.meal && (
                  <View 
                    style={[
                      styles.mealMarker,
                      { left: x - 4, top: y - 20 }
                    ]}
                  >
                    <Text style={styles.mealMarkerText}>{point.meal}</Text>
                  </View>
                )}
                <Text 
                  style={[
                    styles.timeLabel,
                    { left: x - 15, top: CHART_HEIGHT - 15 }
                  ]}
                >
                  {point.time}
                </Text>
              </React.Fragment>
            );
          })}
        </View>
      </View>
      
      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.primary }]} />
          <Text style={styles.legendText}>Glucose</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: Colors.success + '40' }]} />
          <Text style={styles.legendText}>Target Range</Text>
        </View>
        {period === 'day' && (
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: Colors.warning }]} />
            <Text style={styles.legendText}>Meals</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingVertical: 20,
    width: 30,
  },
  yAxisLabel: {
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'right',
  },
  chartContainer: {
    marginLeft: 30,
    height: 150,
    position: 'relative',
  },
  targetRange: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: Colors.success + '20',
    zIndex: 1,
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: Colors.border,
    zIndex: 1,
  },
  dataContainer: {
    flex: 1,
    position: 'relative',
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.white,
    zIndex: 3,
  },
  line: {
    position: 'absolute',
    height: 2,
    backgroundColor: Colors.primary,
    transformOrigin: 'left center',
    zIndex: 2,
  },
  mealMarker: {
    position: 'absolute',
    backgroundColor: Colors.warning + '30',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    zIndex: 4,
  },
  mealMarkerText: {
    fontSize: 8,
    color: Colors.warning,
  },
  timeLabel: {
    position: 'absolute',
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
});


export default GlucoseChart;
