import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Colors  from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';

interface CategoryButtonProps {
  title: string;
  icon: any;
  color?: string;
  onPress?: () => void;
}

const CategoryButton = ({ 
  title, 
  icon, 
  color = Colors.primary,
  onPress 
}: CategoryButtonProps) => {
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
        <Ionicons name={icon} size={24} color={color} />
      </View>
      <Text style={styles.title}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginHorizontal: 8,
    width: 80,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 12,
    color: Colors.text,
    textAlign: 'center',
  },
});


export default CategoryButton;
