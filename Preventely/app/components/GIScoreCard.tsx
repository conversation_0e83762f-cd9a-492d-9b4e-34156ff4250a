import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../constants/colors';

interface GIScoreCardProps {
  score: number; // Score from 0-100
  meals: {
    name: string;
    time: string;
    giScore: number;
    glScore: number;
  }[];
}

const GIScoreCard = ({ score, meals }: GIScoreCardProps) => {
  // Determine score color and status
  let scoreColor = Colors.success;
  let scoreStatus = 'Excellent';
  
  if (score > 70) {
    scoreColor = Colors.error;
    scoreStatus = 'High';
  } else if (score > 50) {
    scoreColor = Colors.warning;
    scoreStatus = 'Moderate';
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>GI/GL Daily Score</Text>
          <Text style={styles.subtitle}>Today's meals impact</Text>
        </View>
        <View style={[styles.scoreContainer, { backgroundColor: `${scoreColor}15` }]}>
          <Text style={[styles.score, { color: scoreColor }]}>{score}</Text>
        </View>
      </View>
      
      <View style={styles.statusContainer}>
        <View style={[styles.statusIndicator, { backgroundColor: scoreColor }]} />
        <Text style={styles.statusText}>{scoreStatus} glycemic impact today</Text>
      </View>
      
      <View style={styles.mealsContainer}>
        {meals.map((meal, index) => (
          <View key={index} style={styles.mealItem}>
            <View style={styles.mealTimeContainer}>
              <Text style={styles.mealTime}>{meal.time}</Text>
            </View>
            <View style={styles.mealDetails}>
              <Text style={styles.mealName}>{meal.name}</Text>
              <View style={styles.mealScores}>
                <View style={styles.mealScore}>
                  <Text style={styles.mealScoreLabel}>GI</Text>
                  <View style={[
                    styles.mealScoreValue, 
                    { 
                      backgroundColor: 
                        meal.giScore > 70 ? `${Colors.error}15` :
                        meal.giScore > 55 ? `${Colors.warning}15` :
                        `${Colors.success}15` 
                    }
                  ]}>
                    <Text style={[
                      styles.mealScoreValueText,
                      { 
                        color: 
                          meal.giScore > 70 ? Colors.error :
                          meal.giScore > 55 ? Colors.warning :
                          Colors.success 
                      }
                    ]}>
                      {meal.giScore}
                    </Text>
                  </View>
                </View>
                <View style={styles.mealScore}>
                  <Text style={styles.mealScoreLabel}>GL</Text>
                  <View style={[
                    styles.mealScoreValue, 
                    { 
                      backgroundColor: 
                        meal.glScore > 20 ? `${Colors.error}15` :
                        meal.glScore > 10 ? `${Colors.warning}15` :
                        `${Colors.success}15` 
                    }
                  ]}>
                    <Text style={[
                      styles.mealScoreValueText,
                      { 
                        color: 
                          meal.glScore > 20 ? Colors.error :
                          meal.glScore > 10 ? Colors.warning :
                          Colors.success 
                      }
                    ]}>
                      {meal.glScore}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>
      
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={16} color={Colors.textSecondary} />
        <Text style={styles.infoText}>
          Lower GI foods help maintain stable blood glucose levels
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  scoreContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  score: {
    fontSize: 20,
    fontWeight: '700',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  mealsContainer: {
    marginBottom: 12,
  },
  mealItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  mealTimeContainer: {
    width: 50,
    marginRight: 12,
  },
  mealTime: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  mealDetails: {
    flex: 1,
    borderLeftWidth: 1,
    borderLeftColor: Colors.border,
    paddingLeft: 12,
  },
  mealName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  mealScores: {
    flexDirection: 'row',
  },
  mealScore: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  mealScoreLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginRight: 4,
  },
  mealScoreValue: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  mealScoreValueText: {
    fontSize: 12,
    fontWeight: '600',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: `${Colors.primary}10`,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
});


export default GIScoreCard;
