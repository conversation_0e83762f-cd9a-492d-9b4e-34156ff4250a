import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Colors  from '../../constants/colors';

type NutritionData = {
  calories: {
    consumed: number;
    goal: number;
  };
  carbs: {
    consumed: number;
    goal: number;
    percentage: number;
  };
  protein: {
    consumed: number;
    goal: number;
    percentage: number;
  };
  fat: {
    consumed: number;
    goal: number;
    percentage: number;
  };
  glycemicLoad: {
    value: number;
    status: 'good' | 'moderate' | 'high';
  };
};

type NutritionSummaryProps = {
  nutrition: NutritionData;
};

const NutritionSummary: React.FC<NutritionSummaryProps> = ({ nutrition }) => {
  const { calories, carbs, protein, fat } = nutrition;
  
  // Calculate calories percentage
  const caloriesPercentage = Math.min(Math.round((calories.consumed / calories.goal) * 100), 100);
  
  return (
    <View style={styles.container}>
      <View style={styles.caloriesContainer}>
        <View style={styles.caloriesHeader}>
          <Text style={styles.caloriesTitle}>Calories</Text>
          <Text style={styles.caloriesRemaining}>
            {calories.goal - calories.consumed} remaining
          </Text>
        </View>
        
        <View style={styles.caloriesValues}>
          <Text style={styles.caloriesConsumed}>{calories.consumed}</Text>
          <Text style={styles.caloriesDivider}>/</Text>
          <Text style={styles.caloriesGoal}>{calories.goal}</Text>
        </View>
        
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View 
              style={[
                styles.progressBarFill, 
                { width: `${caloriesPercentage}%` },
                caloriesPercentage > 90 ? styles.progressBarWarning : {}
              ]} 
            />
          </View>
          <Text style={styles.progressBarText}>{caloriesPercentage}%</Text>
        </View>
      </View>
      
      <View style={styles.macrosContainer}>
        <View style={styles.macroItem}>
          <View style={styles.macroHeader}>
            <Text style={styles.macroTitle}>Carbs</Text>
            <Text style={styles.macroPercentage}>{carbs.percentage}%</Text>
          </View>
          <View style={styles.macroValues}>
            <Text style={styles.macroConsumed}>{carbs.consumed}g</Text>
            <Text style={styles.macroDivider}>/</Text>
            <Text style={styles.macroGoal}>{carbs.goal}g</Text>
          </View>
          <View style={styles.macroProgressContainer}>
            <View style={styles.macroProgressBackground}>
              <View 
                style={[
                  styles.macroProgressFill, 
                  { width: `${carbs.percentage}%`, backgroundColor: Colors.carbs }
                ]} 
              />
            </View>
          </View>
        </View>
        
        <View style={styles.macroItem}>
          <View style={styles.macroHeader}>
            <Text style={styles.macroTitle}>Protein</Text>
            <Text style={styles.macroPercentage}>{protein.percentage}%</Text>
          </View>
          <View style={styles.macroValues}>
            <Text style={styles.macroConsumed}>{protein.consumed}g</Text>
            <Text style={styles.macroDivider}>/</Text>
            <Text style={styles.macroGoal}>{protein.goal}g</Text>
          </View>
          <View style={styles.macroProgressContainer}>
            <View style={styles.macroProgressBackground}>
              <View 
                style={[
                  styles.macroProgressFill, 
                  { width: `${protein.percentage}%`, backgroundColor: Colors.protein }
                ]} 
              />
            </View>
          </View>
        </View>
        
        <View style={styles.macroItem}>
          <View style={styles.macroHeader}>
            <Text style={styles.macroTitle}>Fat</Text>
            <Text style={styles.macroPercentage}>{fat.percentage}%</Text>
          </View>
          <View style={styles.macroValues}>
            <Text style={styles.macroConsumed}>{fat.consumed}g</Text>
            <Text style={styles.macroDivider}>/</Text>
            <Text style={styles.macroGoal}>{fat.goal}g</Text>
          </View>
          <View style={styles.macroProgressContainer}>
            <View style={styles.macroProgressBackground}>
              <View 
                style={[
                  styles.macroProgressFill, 
                  { width: `${fat.percentage}%`, backgroundColor: Colors.fat }
                ]} 
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  caloriesContainer: {
    marginBottom: 16,
  },
  caloriesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  caloriesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  caloriesRemaining: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  caloriesValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  caloriesConsumed: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
  },
  caloriesDivider: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginHorizontal: 4,
  },
  caloriesGoal: {
    fontSize: 18,
    color: Colors.textSecondary,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressBarWarning: {
    backgroundColor: Colors.warning,
  },
  progressBarText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginLeft: 8,
    width: 40,
    textAlign: 'right',
  },
  macrosContainer: {
    gap: 12,
  },
  macroItem: {
  },
  macroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  macroTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
  },
  macroPercentage: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  macroValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginVertical: 2,
  },
  macroConsumed: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  macroDivider: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginHorizontal: 4,
  },
  macroGoal: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  macroProgressContainer: {
    marginTop: 4,
  },
  macroProgressBackground: {
    height: 6,
    backgroundColor: Colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  macroProgressFill: {
    height: '100%',
    borderRadius: 3,
  },
});


export default NutritionSummary;
