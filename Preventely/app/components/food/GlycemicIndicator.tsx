import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../../constants/colors';

type GlycemicLoadData = {
  value: number;
  status: 'good' | 'moderate' | 'high';
};

type GlycemicIndicatorProps = {
  glycemicLoad: GlycemicLoadData;
};

const GlycemicIndicator: React.FC<GlycemicIndicatorProps> = ({ glycemicLoad }) => {
  const { value, status } = glycemicLoad;
  
  // Determine color and message based on status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return Colors.success;
      case 'moderate':
        return Colors.warning;
      case 'high':
        return Colors.error;
      default:
        return Colors.textSecondary;
    }
  };
  
  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'good':
        return 'Your daily glycemic load is in a healthy range';
      case 'moderate':
        return 'Your glycemic load is moderate - consider lower GI foods';
      case 'high':
        return 'Your glycemic load is high - try to reduce high GI foods';
      default:
        return '';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return 'checkmark-circle';
      case 'moderate':
        return 'alert-circle';
      case 'high':
        return 'warning';
      default:
        return 'information-circle';
    }
  };
  
  const statusColor = getStatusColor(status);
  const statusMessage = getStatusMessage(status);
  const statusIcon = getStatusIcon(status);
  
  // Determine position on the gauge based on value
  // Assuming 0-20 is good, 21-60 is moderate, 61+ is high
  const getGaugePosition = (value: number) => {
    if (value <= 20) {
      return Math.min(value / 20 * 33, 33); // First third of gauge
    } else if (value <= 60) {
      return 33 + Math.min((value - 20) / 40 * 33, 33); // Middle third
    } else {
      return 66 + Math.min((value - 60) / 40 * 34, 34); // Last third
    }
  };
  
  const gaugePosition = getGaugePosition(value);
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Glycemic Load</Text>
        <View style={styles.valueContainer}>
          <Text style={[styles.value, { color: statusColor }]}>{value}</Text>
          <Text style={styles.unit}>GL</Text>
        </View>
      </View>
      
      <View style={styles.gaugeContainer}>
        <View style={styles.gauge}>
          <View style={styles.gaugeBackground}>
            <View style={styles.gaugeSection1} />
            <View style={styles.gaugeSection2} />
            <View style={styles.gaugeSection3} />
          </View>
          <View 
            style={[
              styles.gaugeIndicator,
              { left: `${gaugePosition}%` }
            ]}
          />
        </View>
        <View style={styles.gaugeLabels}>
          <Text style={styles.gaugeLabel}>Low</Text>
          <Text style={styles.gaugeLabel}>Moderate</Text>
          <Text style={styles.gaugeLabel}>High</Text>
        </View>
      </View>
      
      <View style={[styles.statusContainer, { backgroundColor: `${statusColor}10` }]}>
        <Ionicons name={statusIcon as any} size={20} color={statusColor} />
        <Text style={[styles.statusMessage, { color: statusColor }]}>{statusMessage}</Text>
      </View>
      
      <Text style={styles.infoText}>
        Glycemic Load (GL) measures the impact of carbohydrates on blood sugar levels.
        Lower GL foods help maintain stable glucose levels.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  value: {
    fontSize: 20,
    fontWeight: '700',
  },
  unit: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  gaugeContainer: {
    marginBottom: 16,
  },
  gauge: {
    height: 12,
    position: 'relative',
    marginBottom: 4,
  },
  gaugeBackground: {
    flexDirection: 'row',
    height: '100%',
    borderRadius: 6,
    overflow: 'hidden',
  },
  gaugeSection1: {
    flex: 1,
    backgroundColor: Colors.success,
  },
  gaugeSection2: {
    flex: 1,
    backgroundColor: Colors.warning,
  },
  gaugeSection3: {
    flex: 1,
    backgroundColor: Colors.error,
  },
  gaugeIndicator: {
    position: 'absolute',
    top: -4,
    width: 4,
    height: 20,
    backgroundColor: Colors.text,
    borderRadius: 2,
    transform: [{ translateX: -2 }],
  },
  gaugeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  gaugeLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  statusMessage: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 12,
    color: Colors.textSecondary,
    lineHeight: 18,
  },
});


export default GlycemicIndicator;
