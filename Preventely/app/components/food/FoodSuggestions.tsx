import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  FlatList,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../../constants/colors';

type SuggestedFood = {
  id: string;
  name: string;
  gi: number;
  benefits: string;
};

type FoodCategory = {
  id: string;
  category: string;
  foods: SuggestedFood[];
};

type FoodSuggestionsProps = {
  suggestions: FoodCategory[];
  onFoodPress?: (food: SuggestedFood) => void;
};

const FoodSuggestions: React.FC<FoodSuggestionsProps> = ({ 
  suggestions,
  onFoodPress
}) => {
  // Get GI color based on value
  const getGIColor = (gi: number) => {
    if (gi <= 55) return Colors.success;
    if (gi <= 69) return Colors.warning;
    return Colors.error;
  };

  const renderFoodItem = (food: SuggestedFood) => {
    const giColor = getGIColor(food.gi);
    
    return (
      <TouchableOpacity 
        key={food.id}
        style={styles.foodItem}
        onPress={() => onFoodPress && onFoodPress(food)}
      >
        <View style={styles.foodHeader}>
          <Text style={styles.foodName}>{food.name}</Text>
          <View style={styles.giContainer}>
            <Text style={[styles.giValue, { color: giColor }]}>{food.gi}</Text>
            <Text style={styles.giLabel}>GI</Text>
          </View>
        </View>
        <Text style={styles.foodBenefits}>{food.benefits}</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={16} color={Colors.primary} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const renderCategory = (category: FoodCategory) => {
    return (
      <View key={category.id} style={styles.categoryContainer}>
        <Text style={styles.categoryTitle}>{category.category}</Text>
        <View style={styles.foodsContainer}>
          {category.foods.map(renderFoodItem)}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {suggestions.map(renderCategory)}
      
      <TouchableOpacity style={styles.aiSuggestionButton}>
        <Ionicons name="sparkles-outline" size={18} color={Colors.primary} />
        <Text style={styles.aiSuggestionText}>Get AI-powered meal suggestions</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  categoryContainer: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  foodsContainer: {
    gap: 12,
  },
  foodItem: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  foodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  foodName: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    paddingRight: 8,
  },
  giContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  giValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  giLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 2,
  },
  foodBenefits: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  addButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.cardAlt,
    justifyContent: 'center',
    alignItems: 'center',
  },
  aiSuggestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: `${Colors.primary}15`,
    borderRadius: 12,
    padding: 14,
    marginTop: 8,
  },
  aiSuggestionText: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.primary,
    marginLeft: 8,
  },
});


export default FoodSuggestions;
