import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../../constants/colors';

type Food = {
  id: string;
  name: string;
  quantity: string;
  calories: number;
  gi: number;
  gl: number;
};

type Meal = {
  id: string;
  name: string;
  time: string;
  foods: Food[];
  totalCalories: number;
  totalCarbs: number;
  totalProtein: number;
  totalFat: number;
  averageGI: number;
  totalGL: number;
};

type MealListProps = {
  meals: Meal[];
  onMealPress?: (meal: Meal) => void;
};

const MealList: React.FC<MealListProps> = ({ meals, onMealPress }) => {
  const renderMealItem = ({ item }: { item: Meal }) => {
    // Determine GI color based on average GI value
    const getGIColor = (gi: number) => {
      if (gi <= 55) return Colors.success;
      if (gi <= 69) return Colors.warning;
      return Colors.error;
    };

    const giColor = getGIColor(item.averageGI);

    return (
      <TouchableOpacity 
        style={styles.mealCard}
        onPress={() => onMealPress && onMealPress(item)}
      >
        <View style={styles.mealHeader}>
          <View>
            <Text style={styles.mealName}>{item.name}</Text>
            <Text style={styles.mealTime}>{item.time}</Text>
          </View>
          <View style={styles.mealCalories}>
            <Text style={styles.caloriesValue}>{item.totalCalories}</Text>
            <Text style={styles.caloriesLabel}>cal</Text>
          </View>
        </View>
        
        <View style={styles.foodList}>
          {item.foods.map((food) => (
            <View key={food.id} style={styles.foodItem}>
              <Text style={styles.foodName}>{food.name}</Text>
              <Text style={styles.foodQuantity}>{food.quantity}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.mealFooter}>
          <View style={styles.macroContainer}>
            <View style={styles.macroItem}>
              <Text style={styles.macroValue}>{item.totalCarbs}g</Text>
              <Text style={styles.macroLabel}>Carbs</Text>
            </View>
            <View style={styles.macroItem}>
              <Text style={styles.macroValue}>{item.totalProtein}g</Text>
              <Text style={styles.macroLabel}>Protein</Text>
            </View>
            <View style={styles.macroItem}>
              <Text style={styles.macroValue}>{item.totalFat}g</Text>
              <Text style={styles.macroLabel}>Fat</Text>
            </View>
          </View>
          
          <View style={styles.giContainer}>
            <View style={styles.giItem}>
              <Text style={[styles.giValue, { color: giColor }]}>{item.averageGI}</Text>
              <Text style={styles.giLabel}>GI</Text>
            </View>
            <View style={styles.giItem}>
              <Text style={[styles.giValue, { color: giColor }]}>{item.totalGL}</Text>
              <Text style={styles.giLabel}>GL</Text>
            </View>
          </View>
        </View>
        
        <TouchableOpacity style={styles.editButton}>
          <Ionicons name="pencil" size={16} color={Colors.primary} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={meals}
        renderItem={renderMealItem}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  listContent: {
    gap: 16,
  },
  mealCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  mealName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  mealTime: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  mealCalories: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  caloriesValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  caloriesLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 2,
  },
  foodList: {
    marginBottom: 12,
  },
  foodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  foodName: {
    fontSize: 15,
    color: Colors.text,
    flex: 1,
  },
  foodQuantity: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  mealFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 12,
  },
  macroContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  macroItem: {
    alignItems: 'center',
  },
  macroValue: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
  },
  macroLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  giContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  giItem: {
    alignItems: 'center',
  },
  giValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  giLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  editButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.cardAlt,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


export default MealList;
