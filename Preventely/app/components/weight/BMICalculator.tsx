import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity,
  Animated
} from 'react-native';
import Colors from '../../constants/colors';
import { Ionicons } from '@expo/vector-icons';

type BMICalculatorProps = {
  currentWeight: number;
  height: number;
  currentBMI: number;
};

type BMICategory = {
  name: string;
  range: string;
  color: string;
  description: string;
  healthRisks: string;
};

const BMI_CATEGORIES: BMICategory[] = [
  {
    name: 'Underweight',
    range: 'Below 18.5',
    color: Colors.warning,
    description: 'You may need to gain weight for optimal health.',
    healthRisks: 'Increased risk of nutritional deficiencies, weakened immune system, and osteoporosis.',
  },
  {
    name: 'Normal Weight',
    range: '18.5 - 24.9',
    color: Colors.success,
    description: 'Your weight is within a healthy range for your height.',
    healthRisks: 'Lowest risk of weight-related health problems.',
  },
  {
    name: 'Overweight',
    range: '25.0 - 29.9',
    color: Colors.warning,
    description: 'You may benefit from losing some weight for better health.',
    healthRisks: 'Increased risk of heart disease, high blood pressure, type 2 diabetes, and stroke.',
  },
  {
    name: 'Obesity',
    range: '30.0 and above',
    color: Colors.error,
    description: 'Weight loss is recommended to reduce health risks.',
    healthRisks: 'High risk of heart disease, high blood pressure, type 2 diabetes, sleep apnea, and certain cancers.',
  },
];

const BMICalculator: React.FC<BMICalculatorProps> = ({ 
  currentWeight,
  height,
  currentBMI
}) => {
  const [weight, setWeight] = useState(currentWeight.toString());
  const [heightValue, setHeightValue] = useState(height.toString());
  const [bmi, setBmi] = useState(currentBMI);
  const [bmiCategory, setBmiCategory] = useState<BMICategory | null>(null);
  const [showInfo, setShowInfo] = useState(false);
  
  // Animation for the BMI gauge
  const gaugeAnimation = new Animated.Value(0);
  const gaugePosition = gaugeAnimation.interpolate({
    inputRange: [0, 50],
    outputRange: ['0%', '100%'],
  });
  
  // Calculate BMI and determine category
  const calculateBMI = (weightKg: number, heightCm: number) => {
    if (weightKg > 0 && heightCm > 0) {
      const heightM = heightCm / 100;
      const bmiValue = weightKg / (heightM * heightM);
      setBmi(parseFloat(bmiValue.toFixed(1)));
      
      // Determine BMI category
      let category = null;
      if (bmiValue < 18.5) {
        category = BMI_CATEGORIES[0];
      } else if (bmiValue < 25) {
        category = BMI_CATEGORIES[1];
      } else if (bmiValue < 30) {
        category = BMI_CATEGORIES[2];
      } else {
        category = BMI_CATEGORIES[3];
      }
      
      setBmiCategory(category);
      
      // Animate the gauge
      Animated.timing(gaugeAnimation, {
        toValue: Math.min(bmiValue, 50),
        duration: 500,
        useNativeDriver: false,
      }).start();
    }
  };
  
  // Calculate BMI when weight or height changes
  useEffect(() => {
    const weightNum = parseFloat(weight);
    const heightNum = parseFloat(heightValue);
    calculateBMI(weightNum, heightNum);
  }, [weight, heightValue]);
  
  // Initialize with current values
  useEffect(() => {
    calculateBMI(currentWeight, height);
  }, [currentWeight, height]);
  
  return (
    <View style={styles.container}>
      <View style={styles.bmiResultContainer}>
        <View style={styles.bmiHeader}>
          <Text style={styles.bmiTitle}>Your BMI</Text>
          <TouchableOpacity 
            style={styles.infoButton}
            onPress={() => setShowInfo(!showInfo)}
          >
            <Ionicons 
              name={showInfo ? "close-circle-outline" : "information-circle-outline"} 
              size={24} 
              color={Colors.primary} 
            />
          </TouchableOpacity>
        </View>
        
        <View style={styles.bmiValueContainer}>
          <Text style={styles.bmiValue}>{bmi}</Text>
          <View 
            style={[
              styles.bmiCategoryBadge, 
              { backgroundColor: bmiCategory?.color }
            ]}
          >
            <Text style={styles.bmiCategoryText}>{bmiCategory?.name}</Text>
          </View>
        </View>
        
        <View style={styles.bmiGaugeContainer}>
          <View style={styles.bmiGauge}>
            <View style={styles.bmiGaugeBackground}>
              <View style={styles.bmiGaugeSection1} />
              <View style={styles.bmiGaugeSection2} />
              <View style={styles.bmiGaugeSection3} />
              <View style={styles.bmiGaugeSection4} />
            </View>
            <Animated.View 
              style={[
                styles.bmiGaugeIndicator,
                { left: gaugePosition }
              ]}
            />
          </View>
          <View style={styles.bmiGaugeLabels}>
            <Text style={styles.bmiGaugeLabel}>15</Text>
            <Text style={styles.bmiGaugeLabel}>18.5</Text>
            <Text style={styles.bmiGaugeLabel}>25</Text>
            <Text style={styles.bmiGaugeLabel}>30</Text>
            <Text style={styles.bmiGaugeLabel}>40+</Text>
          </View>
        </View>
        
        {showInfo && (
          <View style={styles.bmiInfoContainer}>
            <Text style={styles.bmiInfoTitle}>What is BMI?</Text>
            <Text style={styles.bmiInfoText}>
              Body Mass Index (BMI) is a measure of body fat based on height and weight that applies to adult men and women.
            </Text>
            <Text style={styles.bmiInfoText}>
              BMI is an indicator of potential health risks but doesn't diagnose body fatness or health.
              Other factors like muscle mass, bone density, and distribution of fat are not considered.
            </Text>
            
            <Text style={styles.bmiCategoriesTitle}>BMI Categories</Text>
            {BMI_CATEGORIES.map((category, index) => (
              <View key={index} style={styles.bmiCategoryInfo}>
                <View style={styles.bmiCategoryHeader}>
                  <View 
                    style={[
                      styles.bmiCategoryDot, 
                      { backgroundColor: category.color }
                    ]} 
                  />
                  <Text style={styles.bmiCategoryName}>{category.name}</Text>
                  <Text style={styles.bmiCategoryRange}>({category.range})</Text>
                </View>
                <Text style={styles.bmiCategoryDescription}>{category.description}</Text>
                <Text style={styles.bmiCategoryRisks}>{category.healthRisks}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
      
      <View style={styles.calculatorContainer}>
        <Text style={styles.calculatorTitle}>Calculate Your BMI</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Weight (kg)</Text>
          <View style={styles.textInputContainer}>
            <TextInput
              style={styles.textInput}
              value={weight}
              onChangeText={setWeight}
              keyboardType="numeric"
              placeholder="Enter weight"
            />
            <Text style={styles.inputUnit}>kg</Text>
          </View>
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Height (cm)</Text>
          <View style={styles.textInputContainer}>
            <TextInput
              style={styles.textInput}
              value={heightValue}
              onChangeText={setHeightValue}
              keyboardType="numeric"
              placeholder="Enter height"
            />
            <Text style={styles.inputUnit}>cm</Text>
          </View>
        </View>
        
        <View style={styles.healthyWeightContainer}>
          <Text style={styles.healthyWeightTitle}>Your Healthy Weight Range</Text>
          <Text style={styles.healthyWeightValue}>
            {((18.5 * (height / 100) * (height / 100)).toFixed(1))} - {((24.9 * (height / 100) * (height / 100)).toFixed(1))} kg
          </Text>
          <Text style={styles.healthyWeightNote}>
            Based on the BMI healthy range of 18.5 - 24.9
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 24,
  },
  bmiResultContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  bmiHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  bmiTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  infoButton: {
    padding: 4,
  },
  bmiValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  bmiValue: {
    fontSize: 36,
    fontWeight: '700',
    color: Colors.text,
    marginRight: 12,
  },
  bmiCategoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  bmiCategoryText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
  },
  bmiGaugeContainer: {
    marginBottom: 16,
  },
  bmiGauge: {
    height: 12,
    position: 'relative',
    marginBottom: 4,
  },
  bmiGaugeBackground: {
    flexDirection: 'row',
    height: '100%',
    borderRadius: 6,
    overflow: 'hidden',
  },
  bmiGaugeSection1: {
    flex: 7,
    backgroundColor: Colors.warning,
  },
  bmiGaugeSection2: {
    flex: 13,
    backgroundColor: Colors.success,
  },
  bmiGaugeSection3: {
    flex: 10,
    backgroundColor: Colors.warning,
  },
  bmiGaugeSection4: {
    flex: 20,
    backgroundColor: Colors.error,
  },
  bmiGaugeIndicator: {
    position: 'absolute',
    top: -4,
    width: 4,
    height: 20,
    backgroundColor: Colors.text,
    borderRadius: 2,
    transform: [{ translateX: -2 }],
  },
  bmiGaugeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bmiGaugeLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  bmiInfoContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  bmiInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  bmiInfoText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  bmiCategoriesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 16,
    marginBottom: 12,
  },
  bmiCategoryInfo: {
    marginBottom: 16,
  },
  bmiCategoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  bmiCategoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  bmiCategoryName: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
    marginRight: 8,
  },
  bmiCategoryRange: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  bmiCategoryDescription: {
    fontSize: 14,
    color: Colors.text,
    marginBottom: 4,
    paddingLeft: 20,
  },
  bmiCategoryRisks: {
    fontSize: 13,
    color: Colors.textSecondary,
    fontStyle: 'italic',
    paddingLeft: 20,
  },
  calculatorContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  calculatorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 15,
    color: Colors.text,
    marginBottom: 8,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
  },
  inputUnit: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  healthyWeightContainer: {
    backgroundColor: `${Colors.success}10`,
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  healthyWeightTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  healthyWeightValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.success,
    marginBottom: 4,
  },
  healthyWeightNote: {
    fontSize: 13,
    color: Colors.textSecondary,
  },
});


export default BMICalculator;
