import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  Image
} from 'react-native';
import Colors from '../../constants/colors';
import { Ionicons } from '@expo/vector-icons';

type ScaleSyncProps = {};

const ScaleSync: React.FC<ScaleSyncProps> = () => {
  const [syncStatus, setSyncStatus] = useState<'idle' | 'scanning' | 'connecting' | 'syncing' | 'success' | 'error'>('idle');
  const [connectedDevice, setConnectedDevice] = useState<string | null>(null);
  
  const handleStartSync = () => {
    setSyncStatus('scanning');
    
    // Simulate scanning for devices
    setTimeout(() => {
      setSyncStatus('connecting');
      
      // Simulate connecting to device
      setTimeout(() => {
        setConnectedDevice('Smart Scale Pro');
        setSyncStatus('syncing');
        
        // Simulate syncing data
        setTimeout(() => {
          setSyncStatus('success');
          
          // Reset after 3 seconds
          setTimeout(() => {
            setSyncStatus('idle');
          }, 3000);
        }, 2000);
      }, 1500);
    }, 1500);
  };
  
  const renderSyncStatus = () => {
    switch (syncStatus) {
      case 'scanning':
        return (
          <View style={styles.syncStatusContainer}>
            <ActivityIndicator size="small" color={Colors.primary} />
            <Text style={styles.syncStatusText}>Scanning for devices...</Text>
          </View>
        );
      
      case 'connecting':
        return (
          <View style={styles.syncStatusContainer}>
            <ActivityIndicator size="small" color={Colors.primary} />
            <Text style={styles.syncStatusText}>Connecting to scale...</Text>
          </View>
        );
      
      case 'syncing':
        return (
          <View style={styles.syncStatusContainer}>
            <ActivityIndicator size="small" color={Colors.primary} />
            <Text style={styles.syncStatusText}>Syncing data from {connectedDevice}...</Text>
          </View>
        );
      
      case 'success':
        return (
          <View style={styles.syncStatusContainer}>
            <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
            <Text style={[styles.syncStatusText, { color: Colors.success }]}>
              Successfully synced with {connectedDevice}
            </Text>
          </View>
        );
      
      case 'error':
        return (
          <View style={styles.syncStatusContainer}>
            <Ionicons name="alert-circle" size={20} color={Colors.error} />
            <Text style={[styles.syncStatusText, { color: Colors.error }]}>
              Error syncing with device. Please try again.
            </Text>
          </View>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Smart Scale Sync</Text>
        <TouchableOpacity style={styles.infoButton}>
          <Ionicons name="information-circle-outline" size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <View style={styles.scaleImageContainer}>
          <Image 
            source={{ uri: 'https://placehold.co/200x100/png?text=Smart+Scale' }}
            style={styles.scaleImage}
            resizeMode="contain"
          />
        </View>
        
        <Text style={styles.description}>
          Connect to your smart scale to automatically sync your weight measurements.
        </Text>
        
        {renderSyncStatus()}
        
        {syncStatus === 'idle' && (
          <TouchableOpacity 
            style={styles.syncButton}
            onPress={handleStartSync}
          >
            <Ionicons name="bluetooth" size={18} color={Colors.white} />
            <Text style={styles.syncButtonText}>Sync with Scale</Text>
          </TouchableOpacity>
        )}
        
        {(syncStatus === 'scanning' || syncStatus === 'connecting' || syncStatus === 'syncing') && (
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={() => setSyncStatus('idle')}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
        
        {syncStatus === 'success' && (
          <View style={styles.syncResultContainer}>
            <View style={styles.syncResultItem}>
              <Text style={styles.syncResultLabel}>Weight</Text>
              <Text style={styles.syncResultValue}>76.2 kg</Text>
            </View>
            <View style={styles.syncResultItem}>
              <Text style={styles.syncResultLabel}>Body Fat</Text>
              <Text style={styles.syncResultValue}>18.5%</Text>
            </View>
            <View style={styles.syncResultItem}>
              <Text style={styles.syncResultLabel}>Muscle Mass</Text>
              <Text style={styles.syncResultValue}>58.4 kg</Text>
            </View>
            <View style={styles.syncResultItem}>
              <Text style={styles.syncResultLabel}>Water</Text>
              <Text style={styles.syncResultValue}>55.2%</Text>
            </View>
          </View>
        )}
        
        {syncStatus === 'error' && (
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={handleStartSync}
          >
            <Ionicons name="refresh" size={18} color={Colors.white} />
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.deviceListContainer}>
        <Text style={styles.deviceListTitle}>Paired Devices</Text>
        
        <View style={styles.deviceItem}>
          <View style={styles.deviceInfo}>
            <Text style={styles.deviceName}>Smart Scale Pro</Text>
            <Text style={styles.deviceLastSync}>Last synced: Today, 8:30 AM</Text>
          </View>
          <TouchableOpacity style={styles.deviceAction}>
            <Ionicons name="ellipsis-horizontal" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity style={styles.addDeviceButton}>
          <Ionicons name="add-circle-outline" size={18} color={Colors.primary} />
          <Text style={styles.addDeviceText}>Add New Device</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  infoButton: {
    padding: 4,
  },
  content: {
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  scaleImageContainer: {
    width: 120,
    height: 80,
    marginBottom: 16,
  },
  scaleImage: {
    width: '100%',
    height: '100%',
  },
  description: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  syncStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  syncStatusText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  syncButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
    marginLeft: 8,
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  cancelButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  syncResultContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
    marginBottom: 16,
  },
  syncResultItem: {
    width: '48%',
    backgroundColor: Colors.cardAlt,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  syncResultLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  syncResultValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.white,
    marginLeft: 8,
  },
  deviceListContainer: {
    paddingTop: 16,
  },
  deviceListTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  deviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  deviceLastSync: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  deviceAction: {
    padding: 4,
  },
  addDeviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  addDeviceText: {
    fontSize: 14,
    color: Colors.primary,
    marginLeft: 8,
  },
});


export default ScaleSync;
