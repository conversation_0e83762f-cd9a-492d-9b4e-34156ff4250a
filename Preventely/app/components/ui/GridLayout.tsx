import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';

interface GridLayoutProps {
  children: ReactNode;
  columns?: number;
  spacing?: number;
  style?: ViewStyle;
}

const GridLayout: React.FC<GridLayoutProps> = ({
  children,
  columns = 2,
  spacing = 12,
  style,
}) => {
  // Convert children to array to handle them
  const childrenArray = React.Children.toArray(children);
  
  // Calculate row count
  const rowCount = Math.ceil(childrenArray.length / columns);
  
  // Create rows and columns
  const rows = [];
  
  for (let i = 0; i < rowCount; i++) {
    const rowItems = [];
    
    for (let j = 0; j < columns; j++) {
      const index = i * columns + j;
      
      if (index < childrenArray.length) {
        rowItems.push(
          <View 
            key={`item-${index}`} 
            style={[
              styles.item, 
              { 
                flex: 1,
                marginRight: j < columns - 1 ? spacing : 0,
              }
            ]}
          >
            {childrenArray[index]}
          </View>
        );
      } else {
        // Add empty item to maintain grid structure
        rowItems.push(
          <View 
            key={`empty-${index}`} 
            style={[
              styles.item, 
              { 
                flex: 1,
                marginRight: j < columns - 1 ? spacing : 0,
              }
            ]} 
          />
        );
      }
    }
    
    rows.push(
      <View 
        key={`row-${i}`} 
        style={[
          styles.row, 
          { 
            marginBottom: i < rowCount - 1 ? spacing : 0 
          }
        ]}
      >
        {rowItems}
      </View>
    );
  }
  
  return (
    <View style={[styles.container, style]}>
      {rows}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  row: {
    flexDirection: 'row',
    width: '100%',
  },
  item: {
    // Individual item styling handled in the component
  },
});


export default GridLayout;
