import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import Colors from '../../constants/colors';

interface StyledCardProps {
  children: ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'primary' | 'secondary';
}

const StyledCard = ({ children, style, variant = 'default' }: StyledCardProps) => {
  const cardStyle = {
    default: styles.defaultCard,
    primary: styles.primaryCard,
    secondary: styles.secondaryCard,
  }[variant];

  return (
    <View style={[styles.card, cardStyle, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  defaultCard: {
    backgroundColor: Colors.card,
  },
  primaryCard: {
    backgroundColor: Colors.purpleCard,
  },
  secondaryCard: {
    backgroundColor: Colors.yellowCard,
  },
});

export default StyledCard;