import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../constants/colors';

interface DoctorCardProps {
  doctor: {
    name: string;
    specialty: string;
    experience: string;
    image: any;
  };
  onPress?: () => void;
}

const DoctorCard = ({ doctor, onPress }: DoctorCardProps) => {
  return (
    <View style={styles.container}>
      {doctor.image ? (
        <Image source={doctor.image} style={styles.image} />
      ) : (
        <View style={[styles.image, styles.placeholderImage]}>
          <Ionicons name="person" size={30} color="#FFFFFF" />
        </View>
      )}
      <View style={styles.details}>
        <Text style={styles.name}>{doctor.name}</Text>
        <Text style={styles.specialty}>{doctor.specialty}</Text>
        <Text style={styles.experience}>{doctor.experience}</Text>
      </View>
      <View style={styles.actions}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={onPress}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>Visit Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 4,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    width: 160,
  },
  image: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginBottom: 12,
  },
  details: {
    marginBottom: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  specialty: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  experience: {
    fontSize: 12,
    color: Colors.textLight,
  },
  actions: {
    alignItems: 'center',
  },
  button: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  placeholderImage: {
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


export default DoctorCard;
