import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Colors from '../constants/colors';

interface BadgeProps {
  label: string;
  color?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Badge = ({ 
  label, 
  color = Colors.primary, 
  style, 
  textStyle 
}: BadgeProps) => {
  return (
    <View style={[styles.badge, { backgroundColor: color }, style]}>
      <Text style={[styles.text, textStyle]}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  text: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});


export default Badge;
