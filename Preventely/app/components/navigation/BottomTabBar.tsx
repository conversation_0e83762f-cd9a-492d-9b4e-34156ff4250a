import { Ionicons } from '@expo/vector-icons';
import { usePathname, useRouter } from 'expo-router';
import React from 'react';
import { Animated, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Colors  from '../../constants/colors';

type TabRoute = {
  name: string;
  path: string;
  icon: keyof typeof Ionicons.glyphMap;
};

const tabs: TabRoute[] = [
  { name: 'Home', path: '/home', icon: 'home' },
  { name: 'Food', path: '/food-tracking', icon: 'nutrition-outline' },
  { name: 'Assistant', path: '/ai-assistant', icon: 'chatbubble-ellipses-outline' },
  { name: 'Reports', path: '/reports', icon: 'stats-chart-outline' },
  { name: 'Profile', path: '/profile', icon: 'person-outline' },
];

const BottomTabBar = () => {
  const router = useRouter();
  const pathname = usePathname();
  const insets = useSafeAreaInsets();
  
  // Function to determine if a tab is active
  const isActive = (path: string) => {
    // Special case for home
    if (path === '/home' && (pathname === '/screens/NewHomeScreen' || pathname === '/')) return true;
    return pathname === path || pathname.startsWith(path + '/');
  };
  
  // Function to navigate to a route
  const navigateTo = (path: string) => {
    router.push(path as any);
  };
  
  return (
    <View style={[styles.container, { paddingBottom: 12}]}>
      <View style={styles.tabBarWrapper}>
        <View style={styles.tabBar}>
          {tabs.map((tab, index) => {
            const active = isActive(tab.path);
            return (
              <TouchableOpacity 
                key={tab.name}
                style={styles.tabItem}
                onPress={() => navigateTo(tab.path)}
                activeOpacity={0.6}
              >
                <View style={styles.tabContent}>
                  {active && (
                    <Animated.View style={styles.activeIndicator} />
                  )}
                  <View style={[styles.iconContainer, active && styles.activeIconContainer]}>
                    <Ionicons 
                      name={active ? tab.icon.replace('-outline', '') as any : tab.icon} 
                      size={18} 
                      color={active ? Colors.white : Colors.textLight} 
                    />
                  </View>
                  <Text 
                    style={active ? styles.activeTabText : styles.tabText}
                    numberOfLines={1}
                  >
                    {tab.name}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors?.background,
    
    
   
  },
  tabBarWrapper: {
    paddingHorizontal: 12,
    paddingBottom: 6,
    
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: Colors?.white,
    borderRadius: 24,
    paddingVertical: 5,
    paddingHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    width: '100%',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 3,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  activeIconContainer: {
    backgroundColor: Colors?.primary,
    shadowColor: Colors?.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  activeIndicator: {
    position: 'absolute',
    top: -6,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 9,
    color: Colors.textSecondary,
    marginTop: 3,
    textAlign: 'center',
    maxWidth: 50,
  },
  activeTabText: {
    fontSize: 9,
    color: Colors.primary,
    fontWeight: '600',
    marginTop: 3,
    textAlign: 'center',
    maxWidth: 50,
  },
});


export default BottomTabBar;
