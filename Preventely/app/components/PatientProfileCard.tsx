import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors  from '../constants/colors';
import Badge from './Badge';

interface PatientProfileCardProps {
  patient: {
    name: string;
    gender: string;
    age: number;
    height: string;
    image: any;
    treatmentPlans: number;
  };
}

const PatientProfileCard = ({ patient }: PatientProfileCardProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.details}>
          <Text style={styles.name}>{patient.name}</Text>
          <Text style={styles.info}>{patient.gender}, {patient.age}y.o</Text>
          <Text style={styles.info}>Height: {patient.height}</Text>
          <View style={styles.badgeContainer}>
            <Badge 
              label={`${patient.treatmentPlans} Treatment Plans`} 
              color={Colors.primary} 
            />
          </View>
        </View>
        {patient.image ? (
          <Image source={patient.image} style={styles.image} />
        ) : (
          <View style={[styles.image, styles.placeholderImage]}>
            <Ionicons name="person" size={30} color="#FFFFFF" />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 16,
    marginVertical: 8,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  details: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  info: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  badgeContainer: {
    marginTop: 8,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 12,
  },
  placeholderImage: {
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


export default PatientProfileCard;
