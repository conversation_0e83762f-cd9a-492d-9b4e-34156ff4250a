#!/bin/bash

# Clean and regenerate native code
echo "Cleaning and regenerating native code..."
npx expo prebuild --clean

# Verify Android directory was created
if [ -d "./android" ]; then
  echo "Android directory exists"
  
  # Verify gradlew exists and is executable
  if [ -f "./android/gradlew" ]; then
    echo "gradlew exists"
    chmod +x ./android/gradlew
    echo "Made gradlew executable"
  else
    echo "gradlew not found after prebuild!"
    exit 1
  fi
else
  echo "Android directory not found after prebuild!"
  exit 1
fi

echo "Prebuild completed successfully"
