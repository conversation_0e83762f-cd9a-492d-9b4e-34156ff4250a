#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Run prebuild command
console.log('Running prebuild hook...');
try {
  console.log('Cleaning and running expo prebuild...');
  execSync('npx expo prebuild --clean', { stdio: 'inherit' });
  
  // Verify the Android directory was created
  const androidDir = path.join(process.cwd(), 'android');
  if (fs.existsSync(androidDir)) {
    console.log('Android directory exists after prebuild');
    
    // Verify gradlew exists and is executable
    const gradlewPath = path.join(androidDir, 'gradlew');
    if (fs.existsSync(gradlewPath)) {
      console.log('gradlew exists');
      fs.chmodSync(gradlewPath, '755');
      console.log('Made gradlew executable');
    } else {
      console.error('gradlew not found after prebuild!');
      process.exit(1);
    }
  } else {
    console.error('Android directory not found after prebuild!');
    process.exit(1);
  }
  
  console.log('Prebuild hook completed successfully');
} catch (error) {
  console.error('Error in prebuild hook:', error);
  process.exit(1);
}
