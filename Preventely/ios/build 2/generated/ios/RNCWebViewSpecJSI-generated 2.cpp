/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNCWebViewSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeRNCWebViewModuleCxxSpecJSI_isFileUploadSupported(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeRNCWebViewModuleCxxSpecJSI *>(&turboModule)->isFileUploadSupported(
    rt
  );
}
static jsi::Value __hostFunction_NativeRNCWebViewModuleCxxSpecJSI_shouldStartLoadWithLockIdentifier(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRNCWebViewModuleCxxSpecJSI *>(&turboModule)->shouldStartLoadWithLockIdentifier(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asBool(),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asNumber()
  );
  return jsi::Value::undefined();
}

NativeRNCWebViewModuleCxxSpecJSI::NativeRNCWebViewModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RNCWebViewModule", jsInvoker) {
  methodMap_["isFileUploadSupported"] = MethodMetadata {0, __hostFunction_NativeRNCWebViewModuleCxxSpecJSI_isFileUploadSupported};
  methodMap_["shouldStartLoadWithLockIdentifier"] = MethodMetadata {2, __hostFunction_NativeRNCWebViewModuleCxxSpecJSI_shouldStartLoadWithLockIdentifier};
}


} // namespace facebook::react
