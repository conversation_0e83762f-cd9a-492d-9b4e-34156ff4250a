/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleObjCpp
 *
 * We create an umbrella header (and corresponding implementation) here since
 * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
 * must have a single output. More files => more genrule()s => slower builds.
 */

#ifndef __cplusplus
#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
#endif

// Avoid multiple includes of safeareacontext symbols
#ifndef safeareacontext_H
#define safeareacontext_H

#import <Foundation/Foundation.h>
#import <RCTRequired/RCTRequired.h>
#import <RCTTypeSafety/RCTConvertHelpers.h>
#import <RCTTypeSafety/RCTTypedModuleConstants.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTCxxConvert.h>
#import <React/RCTManagedPointer.h>
#import <ReactCommon/RCTTurboModule.h>
#import <optional>
#import <vector>


NS_ASSUME_NONNULL_BEGIN
namespace JS {
  namespace NativeSafeAreaContext {
    struct ConstantsInitialWindowMetricsInsets {

      struct Builder {
        struct Input {
          RCTRequired<double> top;
          RCTRequired<double> right;
          RCTRequired<double> bottom;
          RCTRequired<double> left;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing ConstantsInitialWindowMetricsInsets */
        Builder(ConstantsInitialWindowMetricsInsets i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static ConstantsInitialWindowMetricsInsets fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      ConstantsInitialWindowMetricsInsets(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeSafeAreaContext {
    struct ConstantsInitialWindowMetricsFrame {

      struct Builder {
        struct Input {
          RCTRequired<double> x;
          RCTRequired<double> y;
          RCTRequired<double> width;
          RCTRequired<double> height;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing ConstantsInitialWindowMetricsFrame */
        Builder(ConstantsInitialWindowMetricsFrame i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static ConstantsInitialWindowMetricsFrame fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      ConstantsInitialWindowMetricsFrame(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeSafeAreaContext {
    struct ConstantsInitialWindowMetrics {

      struct Builder {
        struct Input {
          RCTRequired<JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsInsets::Builder> insets;
          RCTRequired<JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsFrame::Builder> frame;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing ConstantsInitialWindowMetrics */
        Builder(ConstantsInitialWindowMetrics i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static ConstantsInitialWindowMetrics fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      ConstantsInitialWindowMetrics(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
namespace JS {
  namespace NativeSafeAreaContext {
    struct Constants {

      struct Builder {
        struct Input {
          std::optional<JS::NativeSafeAreaContext::ConstantsInitialWindowMetrics::Builder> initialWindowMetrics;
        };

        /** Initialize with a set of values */
        Builder(const Input i);
        /** Initialize with an existing Constants */
        Builder(Constants i);
        /** Builds the object. Generally used only by the infrastructure. */
        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
      private:
        NSDictionary *(^_factory)(void);
      };

      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
      NSDictionary *unsafeRawValue() const { return _v; }
    private:
      Constants(NSDictionary *const v) : _v(v) {}
      NSDictionary *_v;
    };
  }
}
@protocol NativeSafeAreaContextSpec <RCTBridgeModule, RCTTurboModule>

- (facebook::react::ModuleConstants<JS::NativeSafeAreaContext::Constants::Builder>)constantsToExport;
- (facebook::react::ModuleConstants<JS::NativeSafeAreaContext::Constants::Builder>)getConstants;

@end

@interface NativeSafeAreaContextSpecBase : NSObject {
@protected
facebook::react::EventEmitterCallback _eventEmitterCallback;
}
- (void)setEventEmitterCallback:(EventEmitterCallbackWrapper *)eventEmitterCallbackWrapper;


@end

namespace facebook::react {
  /**
   * ObjC++ class for module 'NativeSafeAreaContext'
   */
  class JSI_EXPORT NativeSafeAreaContextSpecJSI : public ObjCTurboModule {
  public:
    NativeSafeAreaContextSpecJSI(const ObjCTurboModule::InitParams &params);
  };
} // namespace facebook::react
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsInsets::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto top = i.top.get();
  d[@"top"] = @(top);
  auto right = i.right.get();
  d[@"right"] = @(right);
  auto bottom = i.bottom.get();
  d[@"bottom"] = @(bottom);
  auto left = i.left.get();
  d[@"left"] = @(left);
  return d;
}) {}
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsInsets::Builder::Builder(ConstantsInitialWindowMetricsInsets i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsFrame::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto x = i.x.get();
  d[@"x"] = @(x);
  auto y = i.y.get();
  d[@"y"] = @(y);
  auto width = i.width.get();
  d[@"width"] = @(width);
  auto height = i.height.get();
  d[@"height"] = @(height);
  return d;
}) {}
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetricsFrame::Builder::Builder(ConstantsInitialWindowMetricsFrame i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetrics::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto insets = i.insets.get();
  d[@"insets"] = insets.buildUnsafeRawValue();
  auto frame = i.frame.get();
  d[@"frame"] = frame.buildUnsafeRawValue();
  return d;
}) {}
inline JS::NativeSafeAreaContext::ConstantsInitialWindowMetrics::Builder::Builder(ConstantsInitialWindowMetrics i) : _factory(^{
  return i.unsafeRawValue();
}) {}
inline JS::NativeSafeAreaContext::Constants::Builder::Builder(const Input i) : _factory(^{
  NSMutableDictionary *d = [NSMutableDictionary new];
  auto initialWindowMetrics = i.initialWindowMetrics;
  d[@"initialWindowMetrics"] = initialWindowMetrics.has_value() ? initialWindowMetrics.value().buildUnsafeRawValue() : nil;
  return d;
}) {}
inline JS::NativeSafeAreaContext::Constants::Builder::Builder(Constants i) : _factory(^{
  return i.unsafeRawValue();
}) {}
NS_ASSUME_NONNULL_END
#endif // safeareacontext_H
