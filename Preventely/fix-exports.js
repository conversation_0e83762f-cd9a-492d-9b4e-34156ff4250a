const fs = require('fs');
const path = require('path');

// List of directories to scan for components
const directories = [
  './app/components',
  './app/components/charts',
  './app/components/food',
  './app/components/navigation',
  './app/components/reports',
  './app/components/ui',
  './app/components/weight',
  './app/screens',
  './app/screens/food',
  './app/screens/onboarding',
  './app/screens/devices',
  './app/screens/weight',
  './app/constants'
];

// Function to add default export to a file if it doesn't already have one
function addDefaultExport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file already has a default export
    if (content.includes('export default')) {
      console.log(`✓ ${filePath} already has default export`);
      return;
    }
    
    // Find the component/function name
    const namedExportMatch = content.match(/export\s+(const|function)\s+([A-Za-z0-9_]+)/);
    const constMatch = content.match(/const\s+([A-Za-z0-9_]+)\s+=\s+/);
    
    let componentName = null;
    
    if (namedExportMatch) {
      componentName = namedExportMatch[2];
      // Convert named export to regular declaration
      const newContent = content.replace(
        `export ${namedExportMatch[1]} ${componentName}`,
        `${namedExportMatch[1]} ${componentName}`
      );
      
      // Add default export at the end
      const updatedContent = newContent + `\n\nexport default ${componentName};\n`;
      fs.writeFileSync(filePath, updatedContent);
      console.log(`✓ Added default export for ${componentName} in ${filePath}`);
    } else if (constMatch) {
      componentName = constMatch[1];
      // Add default export at the end
      const updatedContent = content + `\n\nexport default ${componentName};\n`;
      fs.writeFileSync(filePath, updatedContent);
      console.log(`✓ Added default export for ${componentName} in ${filePath}`);
    } else {
      console.log(`✗ Could not find component name in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// Process all TypeScript/JavaScript files in the directories
directories.forEach(dir => {
  try {
    const fullDir = path.resolve(dir);
    if (!fs.existsSync(fullDir)) {
      console.log(`Directory ${fullDir} does not exist, skipping`);
      return;
    }
    
    const files = fs.readdirSync(fullDir);
    files.forEach(file => {
      if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
        const filePath = path.join(fullDir, file);
        addDefaultExport(filePath);
      }
    });
  } catch (error) {
    console.error(`Error processing directory ${dir}:`, error);
  }
});

console.log('Done processing files.');
