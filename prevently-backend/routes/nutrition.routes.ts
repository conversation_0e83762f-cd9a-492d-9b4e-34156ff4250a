import { Router } from 'express';
import { protect } from '../middlewares/auth.middleware';
import {
  getDailyNutrition,
  getDailyNutritionRange,
  updateDailyNutrition,
  getNutritionSummary,
  getCurrentUserDailyLog
} from '../controllers/nutrition.controller';

const router = Router();

/**
 * @swagger
 * /api/nutrition/daily-log:
 *   get:
 *     summary: Get daily nutrition log for authenticated user
 *     description: Retrieves the nutrition log for the current authenticated user, with optional date filtering
 *     tags: [Nutrition]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Optional date in YYYY-MM-DD format. Defaults to current date if not provided.
 *     responses:
 *       200:
 *         description: Daily nutrition log
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     date:
 *                       type: string
 *                       format: date
 *                     userId:
 *                       type: string
 *                     totalCalories:
 *                       type: number
 *                     totalCarbs:
 *                       type: number
 *                     totalProtein:
 *                       type: number
 *                     totalFat:
 *                       type: number
 *                     averageGI:
 *                       type: number
 *                     totalGlycemicLoad:
 *                       type: number
 *                     glycemicLoadStatus:
 *                       type: string
 *                       enum: [GOOD, MODERATE, HIGH]
 *                     isEstimated:
 *                       type: boolean
 *       401:
 *         description: User not authenticated
 *       500:
 *         description: Server error
 */
router.get('/daily-log', protect, getCurrentUserDailyLog);

/**
 * @swagger
 * tags:
 *   name: Nutrition
 *   description: Nutrition tracking endpoints
 */

/**
 * @swagger
 * /api/nutrition/daily/{userId}/{date}:
 *   get:
 *     summary: Get daily nutrition log
 *     description: Retrieve nutrition log for a specific user on a specific date
 *     tags: [Nutrition]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: path
 *         name: date
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date in YYYY-MM-DD format
 *     responses:
 *       200:
 *         description: Daily nutrition log
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/DailyNutritionLog'
 *       404:
 *         description: Daily nutrition log not found
 */
router.get('/daily/:userId/:date', getDailyNutrition);

/**
 * @swagger
 * /api/nutrition/daily/{userId}:
 *   get:
 *     summary: Get daily nutrition logs for date range
 *     description: Retrieve nutrition logs for a specific user within a date range
 *     tags: [Nutrition]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for date range filter (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for date range filter (YYYY-MM-DD)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Maximum number of logs to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *     responses:
 *       200:
 *         description: A list of daily nutrition logs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 totalCount:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 currentPage:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DailyNutritionLog'
 */
router.get('/daily/:userId', getDailyNutritionRange);

/**
 * @swagger
 * /api/nutrition/daily/{userId}/{date}:
 *   put:
 *     summary: Update daily nutrition log
 *     description: Create or update daily nutrition log for a specific user on a specific date
 *     tags: [Nutrition]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: path
 *         name: date
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date in YYYY-MM-DD format
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               caloriesGoal:
 *                 type: integer
 *                 description: Daily calorie goal
 *               carbsGoal:
 *                 type: number
 *                 description: Daily carbohydrates goal (in grams)
 *               proteinGoal:
 *                 type: number
 *                 description: Daily protein goal (in grams)
 *               fatGoal:
 *                 type: number
 *                 description: Daily fat goal (in grams)
 *     responses:
 *       200:
 *         description: Daily nutrition log updated successfully
 *       500:
 *         description: Error updating daily nutrition log
 */
router.put('/daily/:userId/:date', updateDailyNutrition);

/**
 * @swagger
 * /api/nutrition/summary/{userId}:
 *   get:
 *     summary: Get nutrition summary
 *     description: Retrieve nutrition summary for a specific user within a date range
 *     tags: [Nutrition]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for date range (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for date range (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Nutrition summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalDays:
 *                       type: integer
 *                     averageCalories:
 *                       type: integer
 *                     averageCarbs:
 *                       type: number
 *                     averageProtein:
 *                       type: number
 *                     averageFat:
 *                       type: number
 *                     averageGlycemicLoad:
 *                       type: number
 *                     caloriesGoalAchievement:
 *                       type: number
 *                     carbsGoalAchievement:
 *                       type: number
 *                     proteinGoalAchievement:
 *                       type: number
 *                     fatGoalAchievement:
 *                       type: number
 *                     glycemicLoadDistribution:
 *                       type: object
 *                       properties:
 *                         good:
 *                           type: number
 *                         moderate:
 *                           type: number
 *                         high:
 *                           type: number
 *                     dailyData:
 *                       type: array
 *                       items:
 *                         type: object
 *       404:
 *         description: No nutrition logs found for this date range
 */
router.get('/summary/:userId', getNutritionSummary);

export default router;
