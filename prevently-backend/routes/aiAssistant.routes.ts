import { Router } from 'express';
import {
  getAIResponse,
  getConversationHistory,
  clearConversationHistory
} from '../controllers/aiAssistant.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: AIAssistant
 *   description: AI assistant endpoints using ChatGPT
 */

/**
 * @swagger
 * /api/ai-assistant/chat:
 *   post:
 *     summary: Get AI response
 *     description: Get a personalized AI response based on user data and message
 *     tags: [AIAssistant]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - message
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user
 *               message:
 *                 type: string
 *                 description: User's message
 *               conversationHistory:
 *                 type: array
 *                 description: Previous messages in the conversation
 *                 items:
 *                   type: object
 *                   properties:
 *                     role:
 *                       type: string
 *                       enum: [user, assistant]
 *                     content:
 *                       type: string
 *     responses:
 *       200:
 *         description: AI response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: string
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: User not found
 */
router.post('/chat', getAIResponse);

/**
 * @swagger
 * /api/ai-assistant/history/{userId}:
 *   get:
 *     summary: Get conversation history
 *     description: Get the AI conversation history for a user
 *     tags: [AIAssistant]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: Conversation history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     messages:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           content:
 *                             type: string
 *                           role:
 *                             type: string
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 */
router.get('/history/:userId', getConversationHistory);

/**
 * @swagger
 * /api/ai-assistant/history/{userId}:
 *   delete:
 *     summary: Clear conversation history
 *     description: Clear the AI conversation history for a user
 *     tags: [AIAssistant]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: Conversation history cleared
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
router.delete('/history/:userId', clearConversationHistory);

export default router;
