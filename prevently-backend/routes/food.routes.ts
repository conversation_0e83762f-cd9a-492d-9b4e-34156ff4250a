import { Router } from 'express';
import {
  getAllFoods,
  getFoodById,
  getFoodByBarcode,
  createFood,
  updateFood,
  deleteFood,
  getFoodCategories,
  createFoodCategory
} from '../controllers/food.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Foods
 *   description: Food management endpoints
 */

/**
 * @swagger
 * /api/foods:
 *   get:
 *     summary: Get all foods
 *     description: Retrieve a list of all foods with optional filtering
 *     tags: [Foods]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for food name
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by food category
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Maximum number of foods to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: name
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: A list of foods
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 totalCount:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 currentPage:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Food'
 */
router.get('/', getAllFoods);

/**
 * @swagger
 * /api/foods/{id}:
 *   get:
 *     summary: Get a food by ID
 *     description: Retrieve a specific food by its ID
 *     tags: [Foods]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Food ID
 *     responses:
 *       200:
 *         description: Food details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Food'
 *       404:
 *         description: Food not found
 */
router.get('/:id', getFoodById);

/**
 * @swagger
 * /api/foods/barcode/{barcode}:
 *   get:
 *     summary: Get a food by barcode
 *     description: Retrieve a specific food by its barcode
 *     tags: [Foods]
 *     parameters:
 *       - in: path
 *         name: barcode
 *         required: true
 *         schema:
 *           type: string
 *         description: Food barcode
 *     responses:
 *       200:
 *         description: Food details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Food'
 *       404:
 *         description: Food not found
 */
router.get('/barcode/:barcode', getFoodByBarcode);

/**
 * @swagger
 * /api/foods:
 *   post:
 *     summary: Create a new food
 *     description: Create a new food item
 *     tags: [Foods]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - calories
 *               - carbs
 *               - protein
 *               - fat
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               calories:
 *                 type: integer
 *               carbs:
 *                 type: number
 *               protein:
 *                 type: number
 *               fat:
 *                 type: number
 *               glycemicIndex:
 *                 type: integer
 *               glycemicLoad:
 *                 type: number
 *               servingSize:
 *                 type: string
 *               servingSizeUnit:
 *                 type: string
 *               barcode:
 *                 type: string
 *               imageUrl:
 *                 type: string
 *               foodCategoryId:
 *                 type: string
 *               isCustom:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Food created successfully
 *       400:
 *         description: Invalid request
 */
router.post('/', createFood);

/**
 * @swagger
 * /api/foods/{id}:
 *   put:
 *     summary: Update a food
 *     description: Update an existing food item
 *     tags: [Foods]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Food ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               calories:
 *                 type: integer
 *               carbs:
 *                 type: number
 *               protein:
 *                 type: number
 *               fat:
 *                 type: number
 *               glycemicIndex:
 *                 type: integer
 *               glycemicLoad:
 *                 type: number
 *               servingSize:
 *                 type: string
 *               servingSizeUnit:
 *                 type: string
 *               barcode:
 *                 type: string
 *               imageUrl:
 *                 type: string
 *               foodCategoryId:
 *                 type: string
 *               isVerified:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Food updated successfully
 *       404:
 *         description: Food not found
 */
router.put('/:id', updateFood);

/**
 * @swagger
 * /api/foods/{id}:
 *   delete:
 *     summary: Delete a food
 *     description: Delete a food item
 *     tags: [Foods]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Food ID
 *     responses:
 *       200:
 *         description: Food deleted successfully
 *       404:
 *         description: Food not found
 *       400:
 *         description: Cannot delete food that is used in meals
 */
router.delete('/:id', deleteFood);

/**
 * @swagger
 * /api/foods/categories:
 *   get:
 *     summary: Get all food categories
 *     description: Retrieve a list of all food categories
 *     tags: [Foods]
 *     responses:
 *       200:
 *         description: A list of food categories
 */
router.get('/categories', getFoodCategories);

/**
 * @swagger
 * /api/foods/categories:
 *   post:
 *     summary: Create a new food category
 *     description: Create a new food category
 *     tags: [Foods]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Food category created successfully
 *       400:
 *         description: Invalid request
 */
router.post('/categories', createFoodCategory);

export default router;
