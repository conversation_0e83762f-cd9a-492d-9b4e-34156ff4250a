import { Router } from 'express';
import {
  createAssessment,
  getAssessments,
  getPatients,
  sendInvitation,
  getRiskGroupStats,
  getSummaryReport,
  getSettings,
  updateSettings,
  getPatientCGMData,
  getCGMOverview,
} from '../controllers/admin.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Admin
 *   description: Admin dashboard endpoints for patient management and assessments
 */

// Assessment routes
router.post('/assessments', createAssessment);
router.get('/assessments', getAssessments);

// Patient routes
router.get('/patients', getPatients);

// Invitation routes
router.post('/send-invitation', sendInvitation);

// Risk group routes
router.get('/risk-groups/stats', getRiskGroupStats);

// Report routes
router.get('/reports/summary', getSummaryReport);

// Settings routes
router.get('/settings', getSettings);
router.put('/settings', updateSettings);

// CGM routes
router.get('/patients/:patientId/cgm-data', getPatientCGMData);
router.get('/cgm-overview', getCGMOverview);

export default router;
