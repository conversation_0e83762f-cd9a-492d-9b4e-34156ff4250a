import { Router } from 'express';
import {
  authenticateDexcom,
  getDexcomAuthUrl,
  getGlucoseReadings,
  getLatestGlucoseReading,
  getGlucoseStatistics,
  getDexcomDevices,
  storeGlucoseReading,
  syncGlucoseReadings
} from '../controllers/cgm.controller';
import { protect } from '../middlewares/auth.middleware';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: CGM
 *   description: Continuous Glucose Monitoring endpoints
 */

/**
 * @swagger
 * /api/cgm/auth-url:
 *   get:
 *     summary: Get Dexcom authorization URL
 *     description: Returns the URL to redirect the user for Dexcom OAuth authentication
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: redirectUri
 *         required: true
 *         schema:
 *           type: string
 *         description: The URI to redirect to after authentication
 *     responses:
 *       200:
 *         description: Authorization URL
 *       400:
 *         description: Missing redirect URI
 */
router.get('/auth-url', protect, getDexcomAuthUrl);

/**
 * @swagger
 * /api/cgm/authenticate:
 *   post:
 *     summary: Authenticate with Dexcom
 *     description: Exchange authorization code for access token
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - redirectUri
 *             properties:
 *               code:
 *                 type: string
 *                 description: Authorization code from Dexcom
 *               redirectUri:
 *                 type: string
 *                 description: Redirect URI used in the authorization request
 *     responses:
 *       200:
 *         description: Successfully authenticated
 *       400:
 *         description: Missing required parameters
 *       401:
 *         description: User not authenticated
 */
router.post('/authenticate', protect, authenticateDexcom);

/**
 * @swagger
 * /api/cgm/readings:
 *   get:
 *     summary: Get glucose readings
 *     description: Get glucose readings for a specific date range
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for readings (ISO format)
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for readings (ISO format)
 *     responses:
 *       200:
 *         description: List of glucose readings
 *       400:
 *         description: Missing required parameters
 */
router.get('/readings', protect, getGlucoseReadings);

/**
 * @swagger
 * /api/cgm/latest:
 *   get:
 *     summary: Get latest glucose reading
 *     description: Get the most recent glucose reading
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Latest glucose reading
 */
router.get('/latest', protect, getLatestGlucoseReading);

/**
 * @swagger
 * /api/cgm/statistics:
 *   get:
 *     summary: Get glucose statistics
 *     description: Get statistics for glucose readings in a specific date range
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start date for statistics (ISO format)
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date-time
 *         description: End date for statistics (ISO format)
 *       - in: query
 *         name: targetRangeMin
 *         required: false
 *         schema:
 *           type: integer
 *         description: Minimum target glucose level (default 70 mg/dL)
 *       - in: query
 *         name: targetRangeMax
 *         required: false
 *         schema:
 *           type: integer
 *         description: Maximum target glucose level (default 180 mg/dL)
 *     responses:
 *       200:
 *         description: Glucose statistics
 *       400:
 *         description: Missing required parameters
 */
router.get('/statistics', protect, getGlucoseStatistics);

/**
 * @swagger
 * /api/cgm/devices:
 *   get:
 *     summary: Get Dexcom devices
 *     description: Get information about connected Dexcom devices
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of Dexcom devices
 */
router.get('/devices', protect, getDexcomDevices);

/**
 * @swagger
 * /api/cgm/readings:
 *   post:
 *     summary: Store glucose reading
 *     description: Store a glucose reading in the database
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - value
 *               - timestamp
 *               - userId
 *             properties:
 *               value:
 *                 type: number
 *                 description: Glucose value in mg/dL
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *                 description: Timestamp of the reading
 *               userId:
 *                 type: string
 *                 description: User ID
 *     responses:
 *       201:
 *         description: Glucose reading stored successfully
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: User profile not found
 */
router.post('/readings', protect, storeGlucoseReading);

/**
 * @swagger
 * /api/cgm/sync:
 *   post:
 *     summary: Sync glucose readings
 *     description: Sync glucose readings from Dexcom to the database
 *     tags: [CGM]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: User ID
 *               days:
 *                 type: integer
 *                 description: Number of days to sync (default 1)
 *     responses:
 *       200:
 *         description: Glucose readings synced successfully
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: User profile not found
 */
router.post('/sync', protect, syncGlucoseReadings);

export default router;
