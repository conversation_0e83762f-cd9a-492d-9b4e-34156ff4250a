import { Router } from 'express';
import {
  getUserMeals,
  getMealById,
  createMeal,
  updateMeal,
  deleteMeal
} from '../controllers/meal.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Meals
 *   description: Meal tracking endpoints
 */

/**
 * @swagger
 * /api/meals/user/{userId}:
 *   get:
 *     summary: Get all meals for a user
 *     description: Retrieve a list of all meals for a specific user with optional date filtering
 *     tags: [Meals]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by specific date (YYYY-MM-DD)
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for date range filter (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for date range filter (YYYY-MM-DD)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Maximum number of meals to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *     responses:
 *       200:
 *         description: A list of meals
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 totalCount:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 currentPage:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Meal'
 */
router.get('/user/:userId', getUserMeals);

/**
 * @swagger
 * /api/meals/{id}:
 *   get:
 *     summary: Get a meal by ID
 *     description: Retrieve a specific meal by its ID
 *     tags: [Meals]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meal ID
 *     responses:
 *       200:
 *         description: Meal details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Meal'
 *       404:
 *         description: Meal not found
 */
router.get('/:id', getMealById);

/**
 * @swagger
 * /api/meals:
 *   post:
 *     summary: Create a new meal
 *     description: Create a new meal with food items
 *     tags: [Meals]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - date
 *               - time
 *               - userId
 *               - mealItems
 *             properties:
 *               name:
 *                 type: string
 *                 description: Meal name (e.g., Breakfast, Lunch, Dinner, Snack)
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date of the meal (YYYY-MM-DD)
 *               time:
 *                 type: string
 *                 format: date-time
 *                 description: Time of the meal (YYYY-MM-DDTHH:MM:SS)
 *               userId:
 *                 type: string
 *                 description: ID of the user who created the meal
 *               mealItems:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - foodId
 *                     - quantity
 *                   properties:
 *                     foodId:
 *                       type: string
 *                       description: ID of the food item
 *                     quantity:
 *                       type: number
 *                       description: Quantity of the food item
 *                     servingSize:
 *                       type: number
 *                       description: Serving size of the food item
 *               notes:
 *                 type: string
 *                 description: Additional notes about the meal
 *     responses:
 *       201:
 *         description: Meal created successfully
 *       400:
 *         description: Invalid request
 */
router.post('/', createMeal);

/**
 * @swagger
 * /api/meals/{id}:
 *   put:
 *     summary: Update a meal
 *     description: Update an existing meal
 *     tags: [Meals]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Meal name (e.g., Breakfast, Lunch, Dinner, Snack)
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date of the meal (YYYY-MM-DD)
 *               time:
 *                 type: string
 *                 format: date-time
 *                 description: Time of the meal (YYYY-MM-DDTHH:MM:SS)
 *               mealItems:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - foodId
 *                     - quantity
 *                   properties:
 *                     foodId:
 *                       type: string
 *                       description: ID of the food item
 *                     quantity:
 *                       type: number
 *                       description: Quantity of the food item
 *                     servingSize:
 *                       type: number
 *                       description: Serving size of the food item
 *               notes:
 *                 type: string
 *                 description: Additional notes about the meal
 *     responses:
 *       200:
 *         description: Meal updated successfully
 *       404:
 *         description: Meal not found
 */
router.put('/:id', updateMeal);

/**
 * @swagger
 * /api/meals/{id}:
 *   delete:
 *     summary: Delete a meal
 *     description: Delete a meal and its items
 *     tags: [Meals]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Meal ID
 *     responses:
 *       200:
 *         description: Meal deleted successfully
 *       404:
 *         description: Meal not found
 */
router.delete('/:id', deleteMeal);

export default router;
