import { Router } from 'express';
import {
  scanFoodByBarcode,
  searchOpenFoodFacts,
  importFoodFromOpenFoodFacts
} from '../controllers/foodScan.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: FoodScan
 *   description: Food scanning and Open Food Facts integration
 */

/**
 * @swagger
 * /api/food-scan/barcode/{barcode}:
 *   get:
 *     summary: Scan food by barcode
 *     description: Retrieve food information by barcode, checking local database first, then Open Food Facts
 *     tags: [FoodScan]
 *     parameters:
 *       - in: path
 *         name: barcode
 *         required: true
 *         schema:
 *           type: string
 *         description: Food barcode
 *     responses:
 *       200:
 *         description: Food details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 source:
 *                   type: string
 *                   enum: [database, open_food_facts]
 *                 data:
 *                   $ref: '#/components/schemas/Food'
 *       404:
 *         description: Food not found
 */
router.get('/barcode/:barcode', scanFoodByBarcode);

/**
 * @swagger
 * /api/food-scan/search:
 *   get:
 *     summary: Search foods in Open Food Facts
 *     description: Search for foods in the Open Food Facts database
 *     tags: [FoodScan]
 *     parameters:
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Results per page
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 totalCount:
 *                   type: integer
 *                 currentPage:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Food'
 */
router.get('/search', searchOpenFoodFacts);

/**
 * @swagger
 * /api/food-scan/import:
 *   post:
 *     summary: Import food from Open Food Facts
 *     description: Import a food from Open Food Facts to the local database
 *     tags: [FoodScan]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - barcode
 *             properties:
 *               barcode:
 *                 type: string
 *                 description: Barcode of the food to import
 *     responses:
 *       201:
 *         description: Food imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Food'
 *       400:
 *         description: Food already exists or invalid request
 *       404:
 *         description: Food not found in Open Food Facts
 */
router.post('/import', importFoodFromOpenFoodFacts);

export default router;
