import { Router } from 'express';
import {
  getUserProfile,
  updateProfile,
  getMedications,
  addMedication,
  updateMedication,
  deleteMedication,
  getAllergies,
  addAllergy,
  deleteAllergy,
  getHealthcareProvider,
  updateHealthcareProvider,
  getUserPreferences,
  updateUserPreferences,
  getConnectedDevices,
  addConnectedDevice,
  updateConnectedDevice,
  deleteConnectedDevice
} from '../controllers/profile.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Profile
 *   description: User profile management endpoints
 */

/**
 * @swagger
 * /api/profile/{userId}:
 *   get:
 *     summary: Get user profile
 *     description: Retrieve a user's profile with all related data
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User profile
 *       404:
 *         description: User not found
 */
router.get('/:userId', getUserProfile);

/**
 * @swagger
 * /api/profile/{userId}:
 *   put:
 *     summary: Update user profile
 *     description: Update a user's profile information
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bio:
 *                 type: string
 *               avatar:
 *                 type: string
 *               age:
 *                 type: integer
 *               gender:
 *                 type: string
 *               condition:
 *                 type: string
 *               weight:
 *                 type: number
 *               height:
 *                 type: number
 *               glucoseTargetMin:
 *                 type: integer
 *               glucoseTargetMax:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       404:
 *         description: User not found
 */
router.put('/:userId', updateProfile);

/**
 * @swagger
 * /api/profile/{profileId}/medications:
 *   get:
 *     summary: Get medications
 *     description: Retrieve all medications for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     responses:
 *       200:
 *         description: List of medications
 */
router.get('/:profileId/medications', getMedications);

/**
 * @swagger
 * /api/profile/{profileId}/medications:
 *   post:
 *     summary: Add medication
 *     description: Add a new medication to a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               dosage:
 *                 type: string
 *               frequency:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Medication added successfully
 *       404:
 *         description: Profile not found
 */
router.post('/:profileId/medications', addMedication);

/**
 * @swagger
 * /api/profile/medications/{id}:
 *   put:
 *     summary: Update medication
 *     description: Update an existing medication
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Medication ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               dosage:
 *                 type: string
 *               frequency:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Medication updated successfully
 *       404:
 *         description: Medication not found
 */
router.put('/medications/:id', updateMedication);

/**
 * @swagger
 * /api/profile/medications/{id}:
 *   delete:
 *     summary: Delete medication
 *     description: Delete an existing medication
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Medication ID
 *     responses:
 *       200:
 *         description: Medication deleted successfully
 *       404:
 *         description: Medication not found
 */
router.delete('/medications/:id', deleteMedication);

/**
 * @swagger
 * /api/profile/{profileId}/allergies:
 *   get:
 *     summary: Get allergies
 *     description: Retrieve all allergies for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     responses:
 *       200:
 *         description: List of allergies
 */
router.get('/:profileId/allergies', getAllergies);

/**
 * @swagger
 * /api/profile/{profileId}/allergies:
 *   post:
 *     summary: Add allergy
 *     description: Add a new allergy to a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               severity:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Allergy added successfully
 *       404:
 *         description: Profile not found
 */
router.post('/:profileId/allergies', addAllergy);

/**
 * @swagger
 * /api/profile/allergies/{id}:
 *   delete:
 *     summary: Delete allergy
 *     description: Delete an existing allergy
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Allergy ID
 *     responses:
 *       200:
 *         description: Allergy deleted successfully
 *       404:
 *         description: Allergy not found
 */
router.delete('/allergies/:id', deleteAllergy);

/**
 * @swagger
 * /api/profile/{profileId}/healthcare-provider:
 *   get:
 *     summary: Get healthcare provider
 *     description: Retrieve healthcare provider for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     responses:
 *       200:
 *         description: Healthcare provider details
 *       404:
 *         description: Healthcare provider not found
 */
router.get('/:profileId/healthcare-provider', getHealthcareProvider);

/**
 * @swagger
 * /api/profile/{profileId}/healthcare-provider:
 *   put:
 *     summary: Update healthcare provider
 *     description: Update or create healthcare provider for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               specialty:
 *                 type: string
 *               phone:
 *                 type: string
 *               email:
 *                 type: string
 *               address:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Healthcare provider updated successfully
 *       404:
 *         description: Profile not found
 */
router.put('/:profileId/healthcare-provider', updateHealthcareProvider);

/**
 * @swagger
 * /api/profile/{profileId}/preferences:
 *   get:
 *     summary: Get user preferences
 *     description: Retrieve user preferences for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     responses:
 *       200:
 *         description: User preferences
 */
router.get('/:profileId/preferences', getUserPreferences);

/**
 * @swagger
 * /api/profile/{profileId}/preferences:
 *   put:
 *     summary: Update user preferences
 *     description: Update user preferences for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notifications:
 *                 type: boolean
 *               language:
 *                 type: string
 *               darkMode:
 *                 type: boolean
 *               units:
 *                 type: string
 *     responses:
 *       200:
 *         description: Preferences updated successfully
 *       404:
 *         description: Profile not found
 */
router.put('/:profileId/preferences', updateUserPreferences);

/**
 * @swagger
 * /api/profile/{profileId}/devices:
 *   get:
 *     summary: Get connected devices
 *     description: Retrieve all connected devices for a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     responses:
 *       200:
 *         description: List of connected devices
 */
router.get('/:profileId/devices', getConnectedDevices);

/**
 * @swagger
 * /api/profile/{profileId}/devices:
 *   post:
 *     summary: Add connected device
 *     description: Add a new connected device to a profile
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: profileId
 *         required: true
 *         schema:
 *           type: string
 *         description: Profile ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               deviceType:
 *                 type: string
 *               connected:
 *                 type: boolean
 *               deviceId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Device added successfully
 *       404:
 *         description: Profile not found
 */
router.post('/:profileId/devices', addConnectedDevice);

/**
 * @swagger
 * /api/profile/devices/{id}:
 *   put:
 *     summary: Update connected device
 *     description: Update an existing connected device
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               deviceType:
 *                 type: string
 *               connected:
 *                 type: boolean
 *               deviceId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Device updated successfully
 *       404:
 *         description: Device not found
 */
router.put('/devices/:id', updateConnectedDevice);

/**
 * @swagger
 * /api/profile/devices/{id}:
 *   delete:
 *     summary: Delete connected device
 *     description: Delete an existing connected device
 *     tags: [Profile]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Device ID
 *     responses:
 *       200:
 *         description: Device deleted successfully
 *       404:
 *         description: Device not found
 */
router.delete('/devices/:id', deleteConnectedDevice);

export default router;
