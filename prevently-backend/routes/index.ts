import { Router } from 'express';
import { getHealthCheck } from '../controllers/index.controller';

const router = Router();


/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     description: Returns the API health status
 *     tags: [General]
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/health', getHealthCheck);

// Add a root API endpoint
router.get('/api', (req, res) => {
  res.json({
    message: 'Preventely API is running',
    version: '1.0.0',
    endpoints: [
      '/api/auth',
      '/api/profile',
      '/api/cgm',
      '/api/foods',
      '/api/meals',
      '/api/nutrition',
      '/api/reminders'
    ]
  });
});

export default router;
