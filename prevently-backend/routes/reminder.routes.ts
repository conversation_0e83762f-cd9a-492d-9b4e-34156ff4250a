import { Router } from 'express';
import {
  createR<PERSON>inder,
  getR<PERSON><PERSON><PERSON>,
  getR<PERSON><PERSON><PERSON>yId,
  updateR<PERSON>inder,
  deleteReminder
} from '../controllers/reminder.controller';
import { protect } from '../middlewares/auth.middleware';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Reminders
 *   description: Reminder management endpoints
 */

/**
 * @swagger
 * /api/reminders:
 *   get:
 *     summary: Get all reminders for the authenticated user
 *     description: Retrieves all reminders for the current user, optionally filtered by date
 *     tags: [Reminders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter reminders by date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of reminders
 *       401:
 *         description: Not authenticated
 */
router.get('/', protect, getReminders);

/**
 * @swagger
 * /api/reminders:
 *   post:
 *     summary: Create a new reminder
 *     description: Creates a new reminder for the authenticated user
 *     tags: [Reminders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - time
 *               - type
 *             properties:
 *               title:
 *                 type: string
 *               time:
 *                 type: string
 *                 format: time
 *               date:
 *                 type: string
 *                 format: date
 *               type:
 *                 type: string
 *                 enum: [medication, measurement, activity, appointment]
 *     responses:
 *       201:
 *         description: Reminder created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Not authenticated
 */
router.post('/', protect, createReminder);

/**
 * @swagger
 * /api/reminders/{id}:
 *   get:
 *     summary: Get a reminder by ID
 *     description: Retrieves a specific reminder by its ID
 *     tags: [Reminders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reminder ID
 *     responses:
 *       200:
 *         description: Reminder details
 *       404:
 *         description: Reminder not found
 *       401:
 *         description: Not authenticated
 */
router.get('/:id', protect, getReminderById);

/**
 * @swagger
 * /api/reminders/{id}:
 *   patch:
 *     summary: Update a reminder
 *     description: Updates an existing reminder
 *     tags: [Reminders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reminder ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               time:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date
 *               type:
 *                 type: string
 *                 enum: [medication, measurement, activity, appointment]
 *               completed:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Reminder updated successfully
 *       404:
 *         description: Reminder not found
 *       401:
 *         description: Not authenticated
 */
router.patch('/:id', protect, updateReminder);

/**
 * @swagger
 * /api/reminders/{id}:
 *   delete:
 *     summary: Delete a reminder
 *     description: Deletes a specific reminder by its ID
 *     tags: [Reminders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reminder ID
 *     responses:
 *       200:
 *         description: Reminder deleted successfully
 *       404:
 *         description: Reminder not found
 *       401:
 *         description: Not authenticated
 */
router.delete('/:id', protect, deleteReminder);

export default router;
