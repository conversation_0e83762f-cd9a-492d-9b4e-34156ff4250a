# Prevently Backend

A Node.js backend API for the Prevently application built with Express.js and TypeScript.

## Project Structure

```
prevently-backend/
├── config/         # Configuration files
├── controllers/    # Route controllers
├── middlewares/    # Custom middleware functions
├── models/         # Database models
├── routes/         # API routes
├── services/       # Business logic
├── swagger/        # API documentation
├── .env            # Environment variables
├── server.ts       # Main application entry point
└── package.json    # Project dependencies
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- MongoDB (local or Atlas)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables:

```
PORT=3000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/prevently
```

### Running the Application

Development mode:

```bash
npm run dev
```

Production mode:

```bash
npm run build
npm start
```

### API Documentation

API documentation is available at `/api-docs` when the server is running.

## Features

- Express.js REST API
- TypeScript support
- MongoDB integration with Mongoose
- Swagger API documentation
- Environment variable configuration
- Error handling middleware
- Modular architecture
