import axios from 'axios';
import qs from 'qs';

/**
 * Interface for Dexcom authentication response
 */
interface DexcomAuthResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  refresh_token?: string;
}

/**
 * Interface for Dexcom glucose reading
 */
interface DexcomGlucoseReading {
  systemTime: string;
  displayTime: string;
  value: number;
  status: string;
  trend: string;
  trendRate: number;
}

/**
 * Interface for Dexcom statistics
 */
interface DexcomStatistics {
  averageGlucose: number;
  percentInRange: number;
  percentBelowRange: number;
  percentAboveRange: number;
  timeInRange: number;
  timeAboveRange: number;
  timeBelowRange: number;
  totalReadings: number;
  startDate: string;
  endDate: string;
}

/**
 * Interface for Dexcom device
 */
interface DexcomDevice {
  deviceId: string;
  model: string;
  lastUploadDate: string;
  transmitterId?: string;
  status: string;
}

/**
 * Service for interacting with the Dexcom API
 */
class DexcomService {
  private baseUrl: string;
  private clientId: string;
  private clientSecret: string;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.baseUrl = process.env.DEXCOM_API_URL || 'https://api.dexcom.com';
    this.clientId = process.env.DEXCOM_CLIENT_ID || '';
    this.clientSecret = process.env.DEXCOM_CLIENT_SECRET || '';
    
    // Use sandbox URL if in development mode
    if (process.env.NODE_ENV === 'development' && process.env.DEXCOM_SANDBOX_API_URL) {
      this.baseUrl = process.env.DEXCOM_SANDBOX_API_URL;
    }
  }

  /**
   * Authenticate with the Dexcom API using authorization code
   * @param authorizationCode - The authorization code from Dexcom OAuth flow
   * @param redirectUri - The redirect URI used in the OAuth flow
   */
  async authenticate(authorizationCode: string, redirectUri: string): Promise<DexcomAuthResponse> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v2/oauth2/token`,
        qs.stringify({
          grant_type: 'authorization_code',
          code: authorizationCode,
          redirect_uri: redirectUri,
          client_id: this.clientId,
          client_secret: this.clientSecret
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      this.accessToken = response.data.access_token;
      this.refreshToken = response.data.refresh_token;
      
      // Set token expiry time
      const expiresIn = response.data.expires_in;
      this.tokenExpiry = new Date(Date.now() + expiresIn * 1000);
      
      return response.data;
    } catch (error: any) {
      console.error('Error authenticating with Dexcom API:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with Dexcom API');
    }
  }

  /**
   * Refresh the access token using the refresh token
   */
  async refreshAccessToken(): Promise<DexcomAuthResponse> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/v2/oauth2/token`,
        qs.stringify({
          grant_type: 'refresh_token',
          refresh_token: this.refreshToken,
          client_id: this.clientId,
          client_secret: this.clientSecret
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      this.accessToken = response.data.access_token;
      this.refreshToken = response.data.refresh_token || this.refreshToken;
      
      // Set token expiry time
      const expiresIn = response.data.expires_in;
      this.tokenExpiry = new Date(Date.now() + expiresIn * 1000);
      
      return response.data;
    } catch (error: any) {
      console.error('Error refreshing Dexcom access token:', error.response?.data || error.message);
      throw new Error('Failed to refresh Dexcom access token');
    }
  }

  /**
   * Check if the token is expired and refresh if needed
   */
  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken || !this.tokenExpiry) {
      throw new Error('Not authenticated with Dexcom API');
    }

    // If token is expired or about to expire in the next 5 minutes
    if (this.tokenExpiry.getTime() - Date.now() < 5 * 60 * 1000) {
      await this.refreshAccessToken();
    }
  }

  /**
   * Get glucose readings for a specific date range
   * @param startDate - Start date for glucose readings (ISO format)
   * @param endDate - End date for glucose readings (ISO format)
   */
  async getGlucoseReadings(startDate: string, endDate: string): Promise<DexcomGlucoseReading[]> {
    await this.ensureValidToken();

    try {
      const response = await axios.get(`${this.baseUrl}/v2/users/self/egvs`, {
        params: {
          startDate,
          endDate
        },
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.data.egvs || [];
    } catch (error: any) {
      console.error('Error fetching glucose readings:', error.response?.data || error.message);
      throw new Error('Failed to fetch glucose readings from Dexcom API');
    }
  }

  /**
   * Get the latest glucose reading
   */
  async getLatestGlucoseReading(): Promise<DexcomGlucoseReading> {
    await this.ensureValidToken();

    try {
      // Get readings for the last 24 hours
      const endDate = new Date().toISOString();
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const readings = await this.getGlucoseReadings(startDate, endDate);
      
      if (readings.length === 0) {
        throw new Error('No glucose readings available');
      }
      
      // Return the most recent reading
      return readings[readings.length - 1];
    } catch (error: any) {
      console.error('Error fetching latest glucose reading:', error.response?.data || error.message);
      throw new Error('Failed to fetch latest glucose reading from Dexcom API');
    }
  }

  /**
   * Calculate statistics for glucose readings in a specific date range
   * @param startDate - Start date for statistics (ISO format)
   * @param endDate - End date for statistics (ISO format)
   * @param targetRangeMin - Minimum target glucose level (default: 70 mg/dL)
   * @param targetRangeMax - Maximum target glucose level (default: 180 mg/dL)
   */
  async getStatistics(
    startDate: string, 
    endDate: string, 
    targetRangeMin: number = 70, 
    targetRangeMax: number = 180
  ): Promise<DexcomStatistics> {
    const readings = await this.getGlucoseReadings(startDate, endDate);
    
    if (readings.length === 0) {
      throw new Error('No glucose readings available for the specified date range');
    }
    
    // Calculate statistics
    let totalGlucose = 0;
    let readingsInRange = 0;
    let readingsBelowRange = 0;
    let readingsAboveRange = 0;
    
    for (const reading of readings) {
      totalGlucose += reading.value;
      
      if (reading.value < targetRangeMin) {
        readingsBelowRange++;
      } else if (reading.value > targetRangeMax) {
        readingsAboveRange++;
      } else {
        readingsInRange++;
      }
    }
    
    const totalReadings = readings.length;
    const averageGlucose = totalGlucose / totalReadings;
    const percentInRange = (readingsInRange / totalReadings) * 100;
    const percentBelowRange = (readingsBelowRange / totalReadings) * 100;
    const percentAboveRange = (readingsAboveRange / totalReadings) * 100;
    
    // Assuming readings are approximately 5 minutes apart
    const timeInRange = readingsInRange * 5; // minutes
    const timeBelowRange = readingsBelowRange * 5; // minutes
    const timeAboveRange = readingsAboveRange * 5; // minutes
    
    return {
      averageGlucose: parseFloat(averageGlucose.toFixed(1)),
      percentInRange: parseFloat(percentInRange.toFixed(1)),
      percentBelowRange: parseFloat(percentBelowRange.toFixed(1)),
      percentAboveRange: parseFloat(percentAboveRange.toFixed(1)),
      timeInRange, // minutes
      timeBelowRange, // minutes
      timeAboveRange, // minutes
      totalReadings,
      startDate,
      endDate
    };
  }

  /**
   * Get device information
   */
  async getDevices(): Promise<DexcomDevice[]> {
    await this.ensureValidToken();

    try {
      const response = await axios.get(`${this.baseUrl}/v2/users/self/devices`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.data.devices || [];
    } catch (error: any) {
      console.error('Error fetching devices:', error.response?.data || error.message);
      throw new Error('Failed to fetch devices from Dexcom API');
    }
  }

  /**
   * Generate the authorization URL for Dexcom OAuth flow
   * @param redirectUri - The redirect URI for the OAuth flow
   * @param state - Optional state parameter for security
   */
  getAuthorizationUrl(redirectUri: string, state?: string): string {
    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'offline_access'
    });
    
    if (state) {
      params.append('state', state);
    }
    
    return `${this.baseUrl}/v2/oauth2/login?${params.toString()}`;
  }
}

export default new DexcomService();
export { DexcomGlucoseReading, DexcomStatistics, DexcomDevice, DexcomAuthResponse };
