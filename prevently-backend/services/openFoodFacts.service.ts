import axios from 'axios';

// Define interfaces for Open Food Facts API responses
interface OpenFoodFactsProduct {
  code: string;
  product_name?: string;
  ingredients_text?: string;
  nutriments?: {
    'energy-kcal_100g'?: number;
    carbohydrates_100g?: number;
    proteins_100g?: number;
    fat_100g?: number;
    fiber_100g?: number;
    sugars_100g?: number;
    sodium_100g?: number;
    [key: string]: any;
  };
  serving_size?: string;
  image_url?: string;
  categories_tags?: string[];
  [key: string]: any;
}

interface OpenFoodFactsResponse {
  status: number;
  product?: OpenFoodFactsProduct;
  products?: OpenFoodFactsProduct[];
  count?: number;
  page?: number;
  page_size?: number;
}

// Interface for our mapped food data
interface MappedFood {
  name: string;
  description: string;
  calories: number;
  carbs: number;
  protein: number;
  fat: number;
  glycemicIndex: number | null;
  servingSize: string;
  servingSizeUnit: string;
  barcode: string;
  imageUrl: string | null;
  foodCategory: string | null;
  isVerified: boolean;
  isCustom: boolean;
  fiber: number;
  sugar: number;
  sodium: number;
  dataSource: string;
  sourceUrl: string;
}

/**
 * Service for interacting with the Open Food Facts API
 * Documentation: https://openfoodfacts.github.io/openfoodfacts-server/api/
 */
class OpenFoodFactsService {
  private baseUrl = 'https://world.openfoodfacts.net/api/v2';
  private userAgent = 'Prevently - Food Tracking App - Version 1.0';

  /**
   * Get product by barcode
   * @param barcode Product barcode (EAN, UPC, etc.)
   * @returns Product data or null if not found
   */
  async getProductByBarcode(barcode: string): Promise<OpenFoodFactsProduct | null> {
    try {
      const response = await axios.get(`${this.baseUrl}/product/${barcode}`, {
        headers: {
          'User-Agent': this.userAgent
        }
      });
      
      if (response.data && response.data.status === 1) {
        return response.data.product;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching product from Open Food Facts:', error);
      return null;
    }
  }

  /**
   * Search products by name or other criteria
   * @param query Search query
   * @param page Page number
   * @param pageSize Number of results per page
   * @returns Search results
   */
  async searchProducts(query: string, page: number = 1, pageSize: number = 20): Promise<OpenFoodFactsResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/search`, {
        params: {
          search_terms: query,
          page,
          page_size: pageSize,
          json: true
        },
        headers: {
          'User-Agent': this.userAgent
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching products from Open Food Facts:', error);
      return { status: 0, products: [], count: 0, page: 1, page_size: pageSize };
    }
  }

  /**
   * Map Open Food Facts product data to our Food model
   * @param product Open Food Facts product data
   * @returns Mapped food data
   */
  mapProductToFood(product: OpenFoodFactsProduct | null): MappedFood | null {
    if (!product) return null;

    // Extract nutrients from the nutriments object
    const nutriments = product.nutriments || {};
    
    // Calculate glycemic index if available (this is a rough estimate as OFF doesn't provide GI)
    // A more accurate approach would be to use a separate database of glycemic index values
    let glycemicIndex = null;
    if (nutriments.carbohydrates && nutriments.sugars) {
      // Higher sugar content relative to total carbs generally means higher GI
      // This is a very rough approximation
      const sugarRatio = nutriments.sugars / nutriments.carbohydrates;
      if (sugarRatio > 0.7) glycemicIndex = 70; // High
      else if (sugarRatio > 0.4) glycemicIndex = 55; // Medium
      else glycemicIndex = 40; // Low
    }

    // Map category
    let category = null;
    if (product.categories_tags && product.categories_tags.length > 0) {
      // Get the most specific category (last one)
      const categoryTag = product.categories_tags[0];
      category = categoryTag.replace('en:', '').replace(/-/g, ' ');
    }

    return {
      name: product.product_name || 'Unknown Product',
      description: product.ingredients_text || '',
      calories: Math.round(nutriments['energy-kcal_100g'] || 0),
      carbs: parseFloat((nutriments.carbohydrates_100g || 0).toFixed(1)),
      protein: parseFloat((nutriments.proteins_100g || 0).toFixed(1)),
      fat: parseFloat((nutriments.fat_100g || 0).toFixed(1)),
      glycemicIndex,
      servingSize: product.serving_size || '100',
      servingSizeUnit: 'g',
      barcode: product.code,
      imageUrl: product.image_url || null,
      foodCategory: category,
      isVerified: true,
      isCustom: false,
      // Additional nutritional information
      fiber: parseFloat((nutriments.fiber_100g || 0).toFixed(1)),
      sugar: parseFloat((nutriments.sugars_100g || 0).toFixed(1)),
      sodium: parseFloat((nutriments.sodium_100g || 0).toFixed(1)),
      // Source information
      dataSource: 'Open Food Facts',
      sourceUrl: `https://world.openfoodfacts.net/product/${product.code}`
    };
  }
}

export default new OpenFoodFactsService();
