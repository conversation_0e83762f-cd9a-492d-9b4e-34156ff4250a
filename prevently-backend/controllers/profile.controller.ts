import { Request, Response } from 'express';
import prisma from '../services/prisma.service';

/**
 * Get user profile with all related data
 */
export const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    // Get user and profile with all related data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            medications: true,
            allergies: true,
            healthcareProvider: true,
            preferences: true,
            connectedDevices: true
          }
        }
      }
    });
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }
    
    // If profile doesn't exist, create a default one
    if (!user.profile) {
      const newProfile = await prisma.profile.create({
        data: {
          userId: user.id
        },
        include: {
          medications: true,
          allergies: true,
          healthcareProvider: true,
          preferences: true,
          connectedDevices: true
        }
      });
      
      user.profile = newProfile;
    }
    
    // Calculate BMI if weight and height are available
    if (user.profile.weight && user.profile.height) {
      const heightInMeters = user.profile.height / 100;
      const bmi = user.profile.weight / (heightInMeters * heightInMeters);
      
      // Update BMI if it's different
      if (user.profile.bmi !== bmi) {
        await prisma.profile.update({
          where: { id: user.profile.id },
          data: { bmi: parseFloat(bmi.toFixed(1)) }
        });
        
        user.profile.bmi = parseFloat(bmi.toFixed(1));
      }
    }
    
    // Remove sensitive data
    const { password, ...userData } = user;
    
    res.status(200).json({
      success: true,
      data: userData
    });
  } catch (error: any) {
    console.error('Error getting user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving user profile',
      error: error.message
    });
  }
};

/**
 * Update user profile
 */
export const updateProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { 
      bio, 
      avatar, 
      age, 
      gender, 
      condition, 
      weight, 
      height,
      glucoseTargetMin,
      glucoseTargetMax
    } = req.body;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { profile: true }
    });
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }
    
    // Prepare update data
    const updateData: any = {};
    
    if (bio !== undefined) updateData.bio = bio;
    if (avatar !== undefined) updateData.avatar = avatar;
    if (age !== undefined) updateData.age = age;
    if (gender !== undefined) updateData.gender = gender;
    if (condition !== undefined) updateData.condition = condition;
    if (weight !== undefined) updateData.weight = parseFloat(weight);
    if (height !== undefined) updateData.height = parseFloat(height);
    if (glucoseTargetMin !== undefined) updateData.glucoseTargetMin = parseInt(glucoseTargetMin);
    if (glucoseTargetMax !== undefined) updateData.glucoseTargetMax = parseInt(glucoseTargetMax);
    
    // Calculate BMI if both weight and height are provided or updated
    if ((weight !== undefined && user.profile?.height) || 
        (height !== undefined && user.profile?.weight) || 
        (weight !== undefined && height !== undefined)) {
      
      const weightValue = weight !== undefined ? parseFloat(weight) : user.profile?.weight;
      const heightValue = height !== undefined ? parseFloat(height) : user.profile?.height;
      
      if (weightValue && heightValue) {
        const heightInMeters = heightValue / 100;
        const bmi = weightValue / (heightInMeters * heightInMeters);
        updateData.bmi = parseFloat(bmi.toFixed(1));
      }
    }
    
    // Update or create profile
    let profile;
    
    if (user.profile) {
      profile = await prisma.profile.update({
        where: { id: user.profile.id },
        data: updateData,
        include: {
          medications: true,
          allergies: true,
          healthcareProvider: true,
          preferences: true,
          connectedDevices: true
        }
      });
    } else {
      profile = await prisma.profile.create({
        data: {
          ...updateData,
          userId: user.id
        },
        include: {
          medications: true,
          allergies: true,
          healthcareProvider: true,
          preferences: true,
          connectedDevices: true
        }
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: profile
    });
  } catch (error: any) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating profile',
      error: error.message
    });
  }
};

/**
 * Manage medications
 */
export const getMedications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    const medications = await prisma.medication.findMany({
      where: { profileId }
    });
    
    res.status(200).json({
      success: true,
      data: medications
    });
  } catch (error: any) {
    console.error('Error getting medications:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving medications',
      error: error.message
    });
  }
};

export const addMedication = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    const { name, dosage, frequency, notes } = req.body;
    
    if (!profileId || !name) {
      res.status(400).json({
        success: false,
        message: 'Profile ID and medication name are required'
      });
      return;
    }
    
    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: profileId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
      return;
    }
    
    const medication = await prisma.medication.create({
      data: {
        name,
        dosage,
        frequency,
        notes,
        profileId
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Medication added successfully',
      data: medication
    });
  } catch (error: any) {
    console.error('Error adding medication:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding medication',
      error: error.message
    });
  }
};

export const updateMedication = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, dosage, frequency, notes } = req.body;
    
    if (!id) {
      res.status(400).json({
        success: false,
        message: 'Medication ID is required'
      });
      return;
    }
    
    // Check if medication exists
    const existingMedication = await prisma.medication.findUnique({
      where: { id }
    });
    
    if (!existingMedication) {
      res.status(404).json({
        success: false,
        message: 'Medication not found'
      });
      return;
    }
    
    const medication = await prisma.medication.update({
      where: { id },
      data: {
        name,
        dosage,
        frequency,
        notes
      }
    });
    
    res.status(200).json({
      success: true,
      message: 'Medication updated successfully',
      data: medication
    });
  } catch (error: any) {
    console.error('Error updating medication:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating medication',
      error: error.message
    });
  }
};

export const deleteMedication = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json({
        success: false,
        message: 'Medication ID is required'
      });
      return;
    }
    
    // Check if medication exists
    const existingMedication = await prisma.medication.findUnique({
      where: { id }
    });
    
    if (!existingMedication) {
      res.status(404).json({
        success: false,
        message: 'Medication not found'
      });
      return;
    }
    
    await prisma.medication.delete({
      where: { id }
    });
    
    res.status(200).json({
      success: true,
      message: 'Medication deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting medication:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting medication',
      error: error.message
    });
  }
};

/**
 * Manage allergies
 */
export const getAllergies = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    const allergies = await prisma.allergy.findMany({
      where: { profileId }
    });
    
    res.status(200).json({
      success: true,
      data: allergies
    });
  } catch (error: any) {
    console.error('Error getting allergies:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving allergies',
      error: error.message
    });
  }
};

export const addAllergy = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    const { name, severity, notes } = req.body;
    
    if (!profileId || !name) {
      res.status(400).json({
        success: false,
        message: 'Profile ID and allergy name are required'
      });
      return;
    }
    
    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: profileId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
      return;
    }
    
    const allergy = await prisma.allergy.create({
      data: {
        name,
        severity,
        notes,
        profileId
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Allergy added successfully',
      data: allergy
    });
  } catch (error: any) {
    console.error('Error adding allergy:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding allergy',
      error: error.message
    });
  }
};

export const deleteAllergy = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json({
        success: false,
        message: 'Allergy ID is required'
      });
      return;
    }
    
    // Check if allergy exists
    const existingAllergy = await prisma.allergy.findUnique({
      where: { id }
    });
    
    if (!existingAllergy) {
      res.status(404).json({
        success: false,
        message: 'Allergy not found'
      });
      return;
    }
    
    await prisma.allergy.delete({
      where: { id }
    });
    
    res.status(200).json({
      success: true,
      message: 'Allergy deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting allergy:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting allergy',
      error: error.message
    });
  }
};

/**
 * Manage healthcare provider
 */
export const getHealthcareProvider = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    const healthcareProvider = await prisma.healthcareProvider.findUnique({
      where: { profileId }
    });
    
    if (!healthcareProvider) {
      res.status(404).json({
        success: false,
        message: 'Healthcare provider not found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: healthcareProvider
    });
  } catch (error: any) {
    console.error('Error getting healthcare provider:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving healthcare provider',
      error: error.message
    });
  }
};

export const updateHealthcareProvider = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    const { name, specialty, phone, email, address, notes } = req.body;
    
    if (!profileId || !name) {
      res.status(400).json({
        success: false,
        message: 'Profile ID and provider name are required'
      });
      return;
    }
    
    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: profileId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
      return;
    }
    
    // Check if healthcare provider exists
    const existingProvider = await prisma.healthcareProvider.findUnique({
      where: { profileId }
    });
    
    let provider;
    
    if (existingProvider) {
      provider = await prisma.healthcareProvider.update({
        where: { id: existingProvider.id },
        data: {
          name,
          specialty,
          phone,
          email,
          address,
          notes
        }
      });
    } else {
      provider = await prisma.healthcareProvider.create({
        data: {
          name,
          specialty,
          phone,
          email,
          address,
          notes,
          profileId
        }
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Healthcare provider updated successfully',
      data: provider
    });
  } catch (error: any) {
    console.error('Error updating healthcare provider:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating healthcare provider',
      error: error.message
    });
  }
};

/**
 * Manage user preferences
 */
export const getUserPreferences = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    const preferences = await prisma.userPreference.findUnique({
      where: { profileId }
    });
    
    if (!preferences) {
      // Create default preferences if they don't exist
      const defaultPreferences = await prisma.userPreference.create({
        data: {
          profileId
        }
      });
      
      res.status(200).json({
        success: true,
        data: defaultPreferences
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: preferences
    });
  } catch (error: any) {
    console.error('Error getting user preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving user preferences',
      error: error.message
    });
  }
};

export const updateUserPreferences = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    const { notifications, language, darkMode, units } = req.body;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: profileId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
      return;
    }
    
    // Check if preferences exist
    const existingPreferences = await prisma.userPreference.findUnique({
      where: { profileId }
    });
    
    let preferences;
    
    if (existingPreferences) {
      preferences = await prisma.userPreference.update({
        where: { id: existingPreferences.id },
        data: {
          notifications: notifications !== undefined ? notifications : existingPreferences.notifications,
          language: language || existingPreferences.language,
          darkMode: darkMode !== undefined ? darkMode : existingPreferences.darkMode,
          units: units || existingPreferences.units
        }
      });
    } else {
      preferences = await prisma.userPreference.create({
        data: {
          notifications: notifications !== undefined ? notifications : true,
          language: language || 'EN',
          darkMode: darkMode !== undefined ? darkMode : false,
          units: units || 'metric',
          profileId
        }
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Preferences updated successfully',
      data: preferences
    });
  } catch (error: any) {
    console.error('Error updating preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating preferences',
      error: error.message
    });
  }
};

/**
 * Manage connected devices
 */
export const getConnectedDevices = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      res.status(400).json({
        success: false,
        message: 'Profile ID is required'
      });
      return;
    }
    
    const devices = await prisma.connectedDevice.findMany({
      where: { profileId }
    });
    
    res.status(200).json({
      success: true,
      data: devices
    });
  } catch (error: any) {
    console.error('Error getting connected devices:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving connected devices',
      error: error.message
    });
  }
};

export const addConnectedDevice = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profileId } = req.params;
    const { name, deviceType, connected, deviceId } = req.body;
    
    if (!profileId || !name) {
      res.status(400).json({
        success: false,
        message: 'Profile ID and device name are required'
      });
      return;
    }
    
    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: profileId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
      return;
    }
    
    const device = await prisma.connectedDevice.create({
      data: {
        name,
        deviceType,
        connected: connected !== undefined ? connected : false,
        deviceId,
        lastSyncDate: connected ? new Date() : null,
        profileId
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Device added successfully',
      data: device
    });
  } catch (error: any) {
    console.error('Error adding device:', error);
    res.status(500).json({
      success: false,
      message: 'Error adding device',
      error: error.message
    });
  }
};

export const updateConnectedDevice = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, deviceType, connected, deviceId } = req.body;
    
    if (!id) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }
    
    // Check if device exists
    const existingDevice = await prisma.connectedDevice.findUnique({
      where: { id }
    });
    
    if (!existingDevice) {
      res.status(404).json({
        success: false,
        message: 'Device not found'
      });
      return;
    }
    
    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name;
    if (deviceType !== undefined) updateData.deviceType = deviceType;
    if (deviceId !== undefined) updateData.deviceId = deviceId;
    
    // Update lastSyncDate if device is being connected
    if (connected !== undefined) {
      updateData.connected = connected;
      if (connected && !existingDevice.connected) {
        updateData.lastSyncDate = new Date();
      }
    }
    
    const device = await prisma.connectedDevice.update({
      where: { id },
      data: updateData
    });
    
    res.status(200).json({
      success: true,
      message: 'Device updated successfully',
      data: device
    });
  } catch (error: any) {
    console.error('Error updating device:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating device',
      error: error.message
    });
  }
};

export const deleteConnectedDevice = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json({
        success: false,
        message: 'Device ID is required'
      });
      return;
    }
    
    // Check if device exists
    const existingDevice = await prisma.connectedDevice.findUnique({
      where: { id }
    });
    
    if (!existingDevice) {
      res.status(404).json({
        success: false,
        message: 'Device not found'
      });
      return;
    }
    
    await prisma.connectedDevice.delete({
      where: { id }
    });
    
    res.status(200).json({
      success: true,
      message: 'Device deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting device:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting device',
      error: error.message
    });
  }
};
