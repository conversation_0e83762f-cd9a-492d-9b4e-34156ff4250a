import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import { Prisma } from '@prisma/client';

/**
 * Get all foods with optional filtering
 */
export const getAllFoods = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      search, 
      category, 
      limit = 20, 
      page = 1, 
      sortBy = 'name', 
      sortOrder = 'asc' 
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    
    // Build filter conditions
    const where: Prisma.FoodWhereInput = {};
    
    if (search) {
      where.name = {
        contains: String(search),
        mode: 'insensitive'
      };
    }
    
    if (category) {
      where.foodCategory = {
        name: {
          equals: String(category),
          mode: 'insensitive'
        }
      };
    }
    
    // Get total count for pagination
    const totalCount = await prisma.food.count({ where });
    
    // Get foods with pagination and sorting
    const foods = await prisma.food.findMany({
      where,
      include: {
        foodCategory: true
      },
      skip,
      take: Number(limit),
      orderBy: {
        [String(sortBy)]: sortOrder === 'desc' ? 'desc' : 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      count: foods.length,
      totalCount,
      totalPages: Math.ceil(totalCount / Number(limit)),
      currentPage: Number(page),
      data: foods
    });
  } catch (error: any) {
    console.error('Error getting foods:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving foods',
      error: error.message
    });
  }
};

/**
 * Get food by ID
 */
export const getFoodById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const food = await prisma.food.findUnique({
      where: { id },
      include: {
        foodCategory: true
      }
    });
    
    if (!food) {
      res.status(404).json({
        success: false,
        message: 'Food not found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: food
    });
  } catch (error: any) {
    console.error('Error getting food:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving food',
      error: error.message
    });
  }
};

/**
 * Get food by barcode
 */
export const getFoodByBarcode = async (req: Request, res: Response): Promise<void> => {
  try {
    const { barcode } = req.params;
    
    const food = await prisma.food.findUnique({
      where: { barcode },
      include: {
        foodCategory: true
      }
    });
    
    if (!food) {
      res.status(404).json({
        success: false,
        message: 'Food not found with this barcode'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: food
    });
  } catch (error: any) {
    console.error('Error getting food by barcode:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving food by barcode',
      error: error.message
    });
  }
};

/**
 * Create new food
 */
export const createFood = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      name, 
      description, 
      calories, 
      carbs, 
      protein, 
      fat, 
      glycemicIndex, 
      glycemicLoad,
      servingSize,
      servingSizeUnit,
      barcode,
      imageUrl,
      foodCategoryId,
      isCustom = true
    } = req.body;
    
    // Validate required fields
    if (!name || calories === undefined || carbs === undefined || protein === undefined || fat === undefined) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: name, calories, carbs, protein, fat'
      });
      return;
    }
    
    // Check if barcode already exists
    if (barcode) {
      const existingFood = await prisma.food.findUnique({
        where: { barcode }
      });
      
      if (existingFood) {
        res.status(400).json({
          success: false,
          message: 'Food with this barcode already exists'
        });
        return;
      }
    }
    
    // Create food
    const food = await prisma.food.create({
      data: {
        name,
        description,
        calories: Number(calories),
        carbs: Number(carbs),
        protein: Number(protein),
        fat: Number(fat),
        glycemicIndex: glycemicIndex ? Number(glycemicIndex) : null,
        glycemicLoad: glycemicLoad ? Number(glycemicLoad) : null,
        servingSize: servingSize || '1',
        servingSizeUnit: servingSizeUnit || 'serving',
        barcode,
        imageUrl,
        isCustom,
        foodCategoryId,
        createdBy: req.body.userId // This would come from authenticated user
      },
      include: {
        foodCategory: true
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Food created successfully',
      data: food
    });
  } catch (error: any) {
    console.error('Error creating food:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating food',
      error: error.message
    });
  }
};

/**
 * Update food
 */
export const updateFood = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      calories, 
      carbs, 
      protein, 
      fat, 
      glycemicIndex, 
      glycemicLoad,
      servingSize,
      servingSizeUnit,
      barcode,
      imageUrl,
      foodCategoryId,
      isVerified
    } = req.body;
    
    // Check if food exists
    const existingFood = await prisma.food.findUnique({
      where: { id }
    });
    
    if (!existingFood) {
      res.status(404).json({
        success: false,
        message: 'Food not found'
      });
      return;
    }
    
    // Check if barcode already exists on another food
    if (barcode && barcode !== existingFood.barcode) {
      const foodWithBarcode = await prisma.food.findUnique({
        where: { barcode }
      });
      
      if (foodWithBarcode && foodWithBarcode.id !== id) {
        res.status(400).json({
          success: false,
          message: 'Another food with this barcode already exists'
        });
        return;
      }
    }
    
    // Update food
    const updatedFood = await prisma.food.update({
      where: { id },
      data: {
        name,
        description,
        calories: calories !== undefined ? Number(calories) : undefined,
        carbs: carbs !== undefined ? Number(carbs) : undefined,
        protein: protein !== undefined ? Number(protein) : undefined,
        fat: fat !== undefined ? Number(fat) : undefined,
        glycemicIndex: glycemicIndex !== undefined ? Number(glycemicIndex) : undefined,
        glycemicLoad: glycemicLoad !== undefined ? Number(glycemicLoad) : undefined,
        servingSize,
        servingSizeUnit,
        barcode,
        imageUrl,
        foodCategoryId,
        isVerified
      },
      include: {
        foodCategory: true
      }
    });
    
    res.status(200).json({
      success: true,
      message: 'Food updated successfully',
      data: updatedFood
    });
  } catch (error: any) {
    console.error('Error updating food:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating food',
      error: error.message
    });
  }
};

/**
 * Delete food
 */
export const deleteFood = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // Check if food exists
    const existingFood = await prisma.food.findUnique({
      where: { id }
    });
    
    if (!existingFood) {
      res.status(404).json({
        success: false,
        message: 'Food not found'
      });
      return;
    }
    
    // Check if food is used in any meals
    const mealItemCount = await prisma.mealItem.count({
      where: { foodId: id }
    });
    
    if (mealItemCount > 0) {
      res.status(400).json({
        success: false,
        message: `Cannot delete food as it is used in ${mealItemCount} meal(s)`
      });
      return;
    }
    
    // Delete food
    await prisma.food.delete({
      where: { id }
    });
    
    res.status(200).json({
      success: true,
      message: 'Food deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting food:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting food',
      error: error.message
    });
  }
};

/**
 * Get food categories
 */
export const getFoodCategories = async (req: Request, res: Response): Promise<void> => {
  try {
    const categories = await prisma.foodCategory.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (error: any) {
    console.error('Error getting food categories:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving food categories',
      error: error.message
    });
  }
};

/**
 * Create food category
 */
export const createFoodCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
      return;
    }
    
    // Check if category already exists
    const existingCategory = await prisma.foodCategory.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      }
    });
    
    if (existingCategory) {
      res.status(400).json({
        success: false,
        message: 'Category with this name already exists'
      });
      return;
    }
    
    const category = await prisma.foodCategory.create({
      data: {
        name,
        description
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Food category created successfully',
      data: category
    });
  } catch (error: any) {
    console.error('Error creating food category:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating food category',
      error: error.message
    });
  }
};
