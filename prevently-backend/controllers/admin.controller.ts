import { Request, Response } from 'express';
import { PrismaClient, RiskGroup } from '@prisma/client';
import nodemailer from 'nodemailer';

const prisma = new PrismaClient();

// Finnish Diabetes Risk Score calculation
const calculateRiskScore = (data: any): number => {
  let score = 0;

  // Age scoring
  if (data.age === '45-54') score += 2;
  else if (data.age === '55-64') score += 3;
  else if (data.age === '65+') score += 4;

  // BMI scoring
  if (data.bmi === '25-30') score += 1;
  else if (data.bmi === '30+') score += 3;

  // Waist circumference scoring
  if (data.waistCircumference === 'male-94-102' || data.waistCircumference === 'female-80-88') score += 3;
  else if (data.waistCircumference === 'male-102+' || data.waistCircumference === 'female-88+') score += 4;

  // Physical activity
  if (data.physicalActivity === 'no') score += 2;

  // Vegetable consumption
  if (data.vegetableConsumption === 'no') score += 1;

  // Blood pressure medication
  if (data.bloodPressureMedication === 'yes') score += 2;

  // High glucose history
  if (data.highGlucoseHistory === 'yes') score += 5;

  // Family diabetes history
  if (data.familyDiabetesHistory === 'yes-grandparent-aunt-uncle') score += 3;
  else if (data.familyDiabetesHistory === 'yes-parent-sibling-child') score += 5;

  return score;
};

// Risk group assignment
const getRiskGroup = (score: number): RiskGroup => {
  if (score < 7) return RiskGroup.A;
  if (score < 12) return RiskGroup.B;
  if (score < 15) return RiskGroup.C;
  return RiskGroup.D;
};

/**
 * @swagger
 * /api/admin/assessments:
 *   post:
 *     summary: Create a new patient assessment
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *               - age
 *               - bmi
 *               - waistCircumference
 *               - physicalActivity
 *               - vegetableConsumption
 *               - bloodPressureMedication
 *               - highGlucoseHistory
 *               - familyDiabetesHistory
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               age:
 *                 type: string
 *               bmi:
 *                 type: string
 *               waistCircumference:
 *                 type: string
 *               physicalActivity:
 *                 type: string
 *               vegetableConsumption:
 *                 type: string
 *               bloodPressureMedication:
 *                 type: string
 *               highGlucoseHistory:
 *                 type: string
 *               familyDiabetesHistory:
 *                 type: string
 *               notes:
 *                 type: string
 *               assessedBy:
 *                 type: string
 *     responses:
 *       201:
 *         description: Assessment created successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error
 */
export const createAssessment = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      age,
      bmi,
      waistCircumference,
      physicalActivity,
      vegetableConsumption,
      bloodPressureMedication,
      highGlucoseHistory,
      familyDiabetesHistory,
      notes,
      assessedBy
    } = req.body;

    // Calculate risk score
    const riskScore = calculateRiskScore({
      age,
      bmi,
      waistCircumference,
      physicalActivity,
      vegetableConsumption,
      bloodPressureMedication,
      highGlucoseHistory,
      familyDiabetesHistory
    });

    const riskGroup = getRiskGroup(riskScore);

    // Create or update patient
    const patient = await prisma.patient.upsert({
      where: { email },
      update: {
        firstName,
        lastName,
        phone,
      },
      create: {
        firstName,
        lastName,
        email,
        phone,
      },
    });

    // Create assessment
    const assessment = await prisma.assessment.create({
      data: {
        patientId: patient.id,
        age,
        bmi,
        waistCircumference,
        physicalActivity,
        vegetableConsumption,
        bloodPressureMedication,
        highGlucoseHistory,
        familyDiabetesHistory,
        riskScore,
        riskGroup,
        notes,
        assessedBy,
      },
      include: {
        patient: true,
      },
    });

    res.status(201).json({
      success: true,
      data: {
        assessment,
        riskScore,
        riskGroup,
      },
    });
  } catch (error) {
    console.error('Error creating assessment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create assessment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/assessments:
 *   get:
 *     summary: Get all assessments with pagination
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: riskGroup
 *         schema:
 *           type: string
 *           enum: [A, B, C, D]
 *     responses:
 *       200:
 *         description: List of assessments
 *       500:
 *         description: Server error
 */
export const getAssessments = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const riskGroup = req.query.riskGroup as RiskGroup;
    const skip = (page - 1) * limit;

    const where = riskGroup ? { riskGroup } : {};

    const [assessments, total] = await Promise.all([
      prisma.assessment.findMany({
        where,
        include: {
          patient: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.assessment.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        assessments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching assessments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assessments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/patients:
 *   get:
 *     summary: Get all patients with their latest assessment
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of patients
 *       500:
 *         description: Server error
 */
export const getPatients = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;
    const skip = (page - 1) * limit;

    const where = search
      ? {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        include: {
          assessments: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          invitations: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.patient.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        patients,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching patients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patients',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/send-invitation:
 *   post:
 *     summary: Send app invitation email to patient
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - email
 *               - riskGroup
 *               - riskScore
 *             properties:
 *               patientId:
 *                 type: string
 *               email:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               riskGroup:
 *                 type: string
 *                 enum: [A, B, C, D]
 *               riskScore:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Invitation sent successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error
 */
export const sendInvitation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { patientId, email, firstName, lastName, riskGroup, riskScore } = req.body;

    // Get admin settings for email configuration
    const settings = await prisma.adminSettings.findFirst();

    if (!settings || !settings.smtpHost || !settings.smtpUser) {
      res.status(400).json({
        success: false,
        message: 'Email settings not configured. Please configure SMTP settings first.',
      });
      return;
    }

    // Create email transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail', // or use host/port configuration
      auth: {
        user: settings.smtpUser,
        pass: settings.smtpPassword,
      },
    });

    // Generate email content based on risk group
    const getRiskMessage = (group: string) => {
      switch (group) {
        case 'A':
          return {
            title: 'Low Risk Assessment Result',
            message: 'Your assessment shows a low risk for developing type 2 diabetes. The Preventely app will help you maintain your healthy lifestyle.',
            color: '#10B981'
          };
        case 'B':
          return {
            title: 'Slightly Elevated Risk Assessment Result',
            message: 'Your assessment shows a slightly elevated risk. The Preventely app will provide personalized recommendations to help reduce your risk.',
            color: '#F59E0B'
          };
        case 'C':
          return {
            title: 'Moderate Risk Assessment Result',
            message: 'Your assessment shows a moderate risk for developing type 2 diabetes. The Preventely app will provide comprehensive lifestyle interventions.',
            color: '#EF4444'
          };
        case 'D':
          return {
            title: 'High Risk Assessment Result',
            message: 'Your assessment shows a high risk for developing type 2 diabetes. The Preventely app will provide intensive support and monitoring.',
            color: '#DC2626'
          };
        default:
          return {
            title: 'Assessment Result',
            message: 'Your diabetes risk assessment has been completed.',
            color: '#6B7280'
          };
      }
    };

    const riskInfo = getRiskMessage(riskGroup);
    const registrationLink = `${settings.webAppUrl}/register?email=${encodeURIComponent(email)}`;

    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Preventely Assessment Results</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .risk-badge { display: inline-block; padding: 10px 20px; border-radius: 25px; color: white; font-weight: bold; margin: 20px 0; }
            .download-section { background: white; padding: 25px; border-radius: 10px; margin: 20px 0; text-align: center; }
            .download-button { display: inline-block; padding: 12px 25px; margin: 10px; background: #007AFF; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏥 Preventely Health Assessment</h1>
                <p>Your Diabetes Risk Evaluation Results</p>
            </div>

            <div class="content">
                <h2>Hello ${firstName} ${lastName},</h2>

                <p>Thank you for completing your diabetes risk assessment at our clinic. Based on the Finnish Diabetes Risk Score, here are your results:</p>

                <div style="text-align: center;">
                    <h3>Your Risk Score: ${riskScore} points</h3>
                    <div class="risk-badge" style="background-color: ${riskInfo.color};">
                        Risk Group ${riskGroup} - ${riskInfo.title.replace(' Assessment Result', '')}
                    </div>
                </div>

                <p>${riskInfo.message}</p>

                <div class="download-section">
                    <h3>📱 Download the Preventely App</h3>
                    <p>Get personalized health recommendations, track your progress, and connect with healthcare professionals.</p>

                    <a href="${settings.iosAppUrl}" class="download-button">📱 Download for iOS</a>
                    <a href="${settings.androidAppUrl}" class="download-button">🤖 Download for Android</a>

                    <p style="margin-top: 20px;">
                        <a href="${registrationLink}" style="color: #007AFF; text-decoration: none; font-weight: bold;">
                            🔗 Create Your Account Here
                        </a>
                    </p>
                </div>

                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h4>🎯 What's Next?</h4>
                    <ul>
                        <li>Download the Preventely app using the links above</li>
                        <li>Create your account with this email address</li>
                        <li>Complete your health profile</li>
                        <li>Start receiving personalized recommendations</li>
                        <li>Track your progress and connect with our healthcare team</li>
                    </ul>
                </div>

                <p><strong>Questions?</strong> Contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>

            <div class="footer">
                <p>This email was sent by ${settings.clinicName || 'your healthcare provider'} using Preventely's clinical assessment system.</p>
                <p>© 2024 Preventely Health Solutions. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;

    // Send email
    await transporter.sendMail({
      from: `${settings.fromName} <${settings.fromEmail}>`,
      to: email,
      subject: `Your Preventely Health Assessment Results - Risk Group ${riskGroup}`,
      html: htmlContent,
    });

    // Create or update patient invitation record
    await prisma.patientInvitation.upsert({
      where: {
        patientId_email: {
          patientId,
          email,
        },
      },
      update: {
        riskGroup: riskGroup as RiskGroup,
        riskScore,
        invitationSent: true,
        sentAt: new Date(),
      },
      create: {
        patientId,
        email,
        riskGroup: riskGroup as RiskGroup,
        riskScore,
        invitationSent: true,
        sentAt: new Date(),
      },
    });

    res.json({
      success: true,
      message: 'Invitation sent successfully',
    });
  } catch (error) {
    console.error('Error sending invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send invitation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/risk-groups/stats:
 *   get:
 *     summary: Get risk group statistics
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Risk group statistics
 *       500:
 *         description: Server error
 */
export const getRiskGroupStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const [
      totalAssessments,
      riskGroupCounts,
      monthlyTrends,
      averageScore
    ] = await Promise.all([
      prisma.assessment.count(),
      prisma.assessment.groupBy({
        by: ['riskGroup'],
        _count: {
          riskGroup: true,
        },
      }),
      prisma.assessment.groupBy({
        by: ['assessmentDate'],
        _count: {
          id: true,
        },
        _avg: {
          riskScore: true,
        },
        orderBy: {
          assessmentDate: 'desc',
        },
        take: 12,
      }),
      prisma.assessment.aggregate({
        _avg: {
          riskScore: true,
        },
      }),
    ]);

    const riskDistribution = riskGroupCounts.map(group => ({
      group: group.riskGroup,
      count: group._count.riskGroup,
      percentage: totalAssessments > 0 ? (group._count.riskGroup / totalAssessments) * 100 : 0,
    }));

    res.json({
      success: true,
      data: {
        totalAssessments,
        averageScore: averageScore._avg.riskScore || 0,
        riskDistribution,
        monthlyTrends: monthlyTrends.map(trend => ({
          date: trend.assessmentDate,
          assessments: trend._count.id,
          averageScore: trend._avg.riskScore || 0,
        })),
      },
    });
  } catch (error) {
    console.error('Error fetching risk group stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risk group statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/reports/summary:
 *   get:
 *     summary: Get summary report data
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Summary report data
 *       500:
 *         description: Server error
 */
export const getSummaryReport = async (req: Request, res: Response): Promise<void> => {
  try {
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();

    const dateFilter = {
      assessmentDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    const [
      totalAssessments,
      highRiskPatients,
      invitationsSent,
      accountsCreated,
      riskGroupBreakdown,
      recentAssessments
    ] = await Promise.all([
      prisma.assessment.count({ where: dateFilter }),
      prisma.assessment.count({
        where: {
          ...dateFilter,
          riskGroup: { in: [RiskGroup.C, RiskGroup.D] },
        },
      }),
      prisma.patientInvitation.count({
        where: {
          invitationSent: true,
          sentAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      prisma.patientInvitation.count({
        where: {
          accountCreated: true,
          accountCreatedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      prisma.assessment.groupBy({
        by: ['riskGroup'],
        where: dateFilter,
        _count: {
          riskGroup: true,
        },
        _avg: {
          riskScore: true,
        },
      }),
      prisma.assessment.findMany({
        where: dateFilter,
        include: {
          patient: true,
        },
        orderBy: {
          assessmentDate: 'desc',
        },
        take: 10,
      }),
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          totalAssessments,
          highRiskPatients,
          invitationsSent,
          accountsCreated,
        },
        riskGroupBreakdown: riskGroupBreakdown.map(group => ({
          group: group.riskGroup,
          count: group._count.riskGroup,
          averageScore: group._avg.riskScore || 0,
        })),
        recentAssessments,
        dateRange: {
          startDate,
          endDate,
        },
      },
    });
  } catch (error) {
    console.error('Error generating summary report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate summary report',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/settings:
 *   get:
 *     summary: Get admin settings
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Admin settings
 *       500:
 *         description: Server error
 */
export const getSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    let settings = await prisma.adminSettings.findFirst();

    if (!settings) {
      // Create default settings if none exist
      settings = await prisma.adminSettings.create({
        data: {
          appName: 'Preventely',
          clinicName: 'Healthcare Clinic',
          emailNotifications: true,
          assessmentAlerts: true,
          highRiskAlerts: true,
          weeklyReports: true,
          monthlyReports: true,
        },
      });
    }

    // Don't send sensitive data like passwords
    const { smtpPassword, ...safeSettings } = settings;

    res.json({
      success: true,
      data: safeSettings,
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/settings:
 *   put:
 *     summary: Update admin settings
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               smtpHost:
 *                 type: string
 *               smtpPort:
 *                 type: string
 *               smtpUser:
 *                 type: string
 *               smtpPassword:
 *                 type: string
 *               fromEmail:
 *                 type: string
 *               fromName:
 *                 type: string
 *               appName:
 *                 type: string
 *               clinicName:
 *                 type: string
 *               clinicAddress:
 *                 type: string
 *               clinicPhone:
 *                 type: string
 *               iosAppUrl:
 *                 type: string
 *               androidAppUrl:
 *                 type: string
 *               webAppUrl:
 *                 type: string
 *               emailNotifications:
 *                 type: boolean
 *               assessmentAlerts:
 *                 type: boolean
 *               highRiskAlerts:
 *                 type: boolean
 *               dailyReports:
 *                 type: boolean
 *               weeklyReports:
 *                 type: boolean
 *               monthlyReports:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *       500:
 *         description: Server error
 */
export const updateSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    const settingsData = req.body;

    let settings = await prisma.adminSettings.findFirst();

    if (settings) {
      settings = await prisma.adminSettings.update({
        where: { id: settings.id },
        data: settingsData,
      });
    } else {
      settings = await prisma.adminSettings.create({
        data: settingsData,
      });
    }

    // Don't send sensitive data like passwords
    const { smtpPassword, ...safeSettings } = settings;

    res.json({
      success: true,
      data: safeSettings,
      message: 'Settings updated successfully',
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
