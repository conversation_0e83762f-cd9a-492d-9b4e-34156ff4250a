import { Request, Response } from 'express';
import { PrismaClient, RiskGroup } from '@prisma/client';

const prisma = new PrismaClient();

// Finnish Diabetes Risk Score calculation
const calculateRiskScore = (data: any): number => {
  let score = 0;

  // Age scoring
  if (data.age === '45-54') score += 2;
  else if (data.age === '55-64') score += 3;
  else if (data.age === '65+') score += 4;

  // BMI scoring
  if (data.bmi === '25-30') score += 1;
  else if (data.bmi === '30+') score += 3;

  // Waist circumference scoring
  if (data.waistCircumference === 'male-94-102' || data.waistCircumference === 'female-80-88') score += 3;
  else if (data.waistCircumference === 'male-102+' || data.waistCircumference === 'female-88+') score += 4;

  // Physical activity
  if (data.physicalActivity === 'no') score += 2;

  // Vegetable consumption
  if (data.vegetableConsumption === 'no') score += 1;

  // Blood pressure medication
  if (data.bloodPressureMedication === 'yes') score += 2;

  // High glucose history
  if (data.highGlucoseHistory === 'yes') score += 5;

  // Family diabetes history
  if (data.familyDiabetesHistory === 'yes-grandparent-aunt-uncle') score += 3;
  else if (data.familyDiabetesHistory === 'yes-parent-sibling-child') score += 5;

  return score;
};

// Risk group assignment
const getRiskGroup = (score: number): RiskGroup => {
  if (score < 7) return RiskGroup.A;
  if (score < 12) return RiskGroup.B;
  if (score < 15) return RiskGroup.C;
  return RiskGroup.D;
};

/**
 * @swagger
 * /api/admin/assessments:
 *   post:
 *     summary: Create a new patient assessment
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *               - age
 *               - bmi
 *               - waistCircumference
 *               - physicalActivity
 *               - vegetableConsumption
 *               - bloodPressureMedication
 *               - highGlucoseHistory
 *               - familyDiabetesHistory
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               age:
 *                 type: string
 *               bmi:
 *                 type: string
 *               waistCircumference:
 *                 type: string
 *               physicalActivity:
 *                 type: string
 *               vegetableConsumption:
 *                 type: string
 *               bloodPressureMedication:
 *                 type: string
 *               highGlucoseHistory:
 *                 type: string
 *               familyDiabetesHistory:
 *                 type: string
 *               notes:
 *                 type: string
 *               assessedBy:
 *                 type: string
 *     responses:
 *       201:
 *         description: Assessment created successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error
 */
export const createAssessment = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      age,
      bmi,
      waistCircumference,
      physicalActivity,
      vegetableConsumption,
      bloodPressureMedication,
      highGlucoseHistory,
      familyDiabetesHistory,
      notes,
      assessedBy
    } = req.body;

    // Calculate risk score
    const riskScore = calculateRiskScore({
      age,
      bmi,
      waistCircumference,
      physicalActivity,
      vegetableConsumption,
      bloodPressureMedication,
      highGlucoseHistory,
      familyDiabetesHistory
    });

    const riskGroup = getRiskGroup(riskScore);

    // Create or update patient
    const patient = await prisma.patient.upsert({
      where: { email },
      update: {
        firstName,
        lastName,
        phone,
      },
      create: {
        firstName,
        lastName,
        email,
        phone,
      },
    });

    // Create assessment
    const assessment = await prisma.assessment.create({
      data: {
        patientId: patient.id,
        age,
        bmi,
        waistCircumference,
        physicalActivity,
        vegetableConsumption,
        bloodPressureMedication,
        highGlucoseHistory,
        familyDiabetesHistory,
        riskScore,
        riskGroup,
        notes,
        assessedBy,
      },
      include: {
        patient: true,
      },
    });

    res.status(201).json({
      success: true,
      data: {
        assessment,
        riskScore,
        riskGroup,
      },
    });
  } catch (error) {
    console.error('Error creating assessment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create assessment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/assessments:
 *   get:
 *     summary: Get all assessments with pagination
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: riskGroup
 *         schema:
 *           type: string
 *           enum: [A, B, C, D]
 *     responses:
 *       200:
 *         description: List of assessments
 *       500:
 *         description: Server error
 */
export const getAssessments = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const riskGroup = req.query.riskGroup as RiskGroup;
    const skip = (page - 1) * limit;

    const where = riskGroup ? { riskGroup } : {};

    const [assessments, total] = await Promise.all([
      prisma.assessment.findMany({
        where,
        include: {
          patient: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.assessment.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        assessments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching assessments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assessments',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/patients:
 *   get:
 *     summary: Get all patients with their latest assessment
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of patients
 *       500:
 *         description: Server error
 */
export const getPatients = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;
    const skip = (page - 1) * limit;

    const where = search
      ? {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' as const } },
            { lastName: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [patients, total] = await Promise.all([
      prisma.patient.findMany({
        where,
        include: {
          assessments: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          invitations: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.patient.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        patients,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching patients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch patients',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/send-invitation:
 *   post:
 *     summary: Send app invitation email to patient
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - email
 *               - riskGroup
 *               - riskScore
 *             properties:
 *               patientId:
 *                 type: string
 *               email:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               riskGroup:
 *                 type: string
 *                 enum: [A, B, C, D]
 *               riskScore:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Invitation sent successfully
 *       400:
 *         description: Invalid input data
 *       500:
 *         description: Server error
 */
export const sendInvitation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { patientId, email, firstName, lastName, riskGroup, riskScore } = req.body;

    // For now, just create the invitation record without sending email
    // Email functionality can be added later when SMTP is properly configured

    // Create or update patient invitation record
    await prisma.patientInvitation.upsert({
      where: {
        patientId_email: {
          patientId,
          email,
        },
      },
      update: {
        riskGroup: riskGroup as RiskGroup,
        riskScore,
        invitationSent: true,
        sentAt: new Date(),
      },
      create: {
        patientId,
        email,
        riskGroup: riskGroup as RiskGroup,
        riskScore,
        invitationSent: true,
        sentAt: new Date(),
      },
    });

    res.json({
      success: true,
      message: 'Invitation record created successfully. Email functionality will be available when SMTP is configured.',
    });
  } catch (error) {
    console.error('Error creating invitation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create invitation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};



/**
 * @swagger
 * /api/admin/risk-groups/stats:
 *   get:
 *     summary: Get risk group statistics
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Risk group statistics
 *       500:
 *         description: Server error
 */
export const getRiskGroupStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const [
      totalAssessments,
      riskGroupCounts,
      monthlyTrends,
      averageScore
    ] = await Promise.all([
      prisma.assessment.count(),
      prisma.assessment.groupBy({
        by: ['riskGroup'],
        _count: {
          riskGroup: true,
        },
      }),
      prisma.assessment.groupBy({
        by: ['assessmentDate'],
        _count: {
          id: true,
        },
        _avg: {
          riskScore: true,
        },
        orderBy: {
          assessmentDate: 'desc',
        },
        take: 12,
      }),
      prisma.assessment.aggregate({
        _avg: {
          riskScore: true,
        },
      }),
    ]);

    const riskDistribution = riskGroupCounts.map(group => ({
      group: group.riskGroup,
      count: group._count.riskGroup,
      percentage: totalAssessments > 0 ? (group._count.riskGroup / totalAssessments) * 100 : 0,
    }));

    res.json({
      success: true,
      data: {
        totalAssessments,
        averageScore: averageScore._avg.riskScore || 0,
        riskDistribution,
        monthlyTrends: monthlyTrends.map(trend => ({
          date: trend.assessmentDate,
          assessments: trend._count.id,
          averageScore: trend._avg.riskScore || 0,
        })),
      },
    });
  } catch (error) {
    console.error('Error fetching risk group stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risk group statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/reports/summary:
 *   get:
 *     summary: Get summary report data
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Summary report data
 *       500:
 *         description: Server error
 */
export const getSummaryReport = async (req: Request, res: Response): Promise<void> => {
  try {
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();

    const dateFilter = {
      assessmentDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    const [
      totalAssessments,
      highRiskPatients,
      invitationsSent,
      accountsCreated,
      riskGroupBreakdown,
      recentAssessments
    ] = await Promise.all([
      prisma.assessment.count({ where: dateFilter }),
      prisma.assessment.count({
        where: {
          ...dateFilter,
          riskGroup: { in: [RiskGroup.C, RiskGroup.D] },
        },
      }),
      prisma.patientInvitation.count({
        where: {
          invitationSent: true,
          sentAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      prisma.patientInvitation.count({
        where: {
          accountCreated: true,
          accountCreatedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      prisma.assessment.groupBy({
        by: ['riskGroup'],
        where: dateFilter,
        _count: {
          riskGroup: true,
        },
        _avg: {
          riskScore: true,
        },
      }),
      prisma.assessment.findMany({
        where: dateFilter,
        include: {
          patient: true,
        },
        orderBy: {
          assessmentDate: 'desc',
        },
        take: 10,
      }),
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          totalAssessments,
          highRiskPatients,
          invitationsSent,
          accountsCreated,
        },
        riskGroupBreakdown: riskGroupBreakdown.map(group => ({
          group: group.riskGroup,
          count: group._count.riskGroup,
          averageScore: group._avg.riskScore || 0,
        })),
        recentAssessments,
        dateRange: {
          startDate,
          endDate,
        },
      },
    });
  } catch (error) {
    console.error('Error generating summary report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate summary report',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/settings:
 *   get:
 *     summary: Get admin settings
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Admin settings
 *       500:
 *         description: Server error
 */
export const getSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    let settings = await prisma.adminSettings.findFirst();

    if (!settings) {
      // Create default settings if none exist
      settings = await prisma.adminSettings.create({
        data: {
          appName: 'Preventely',
          clinicName: 'Healthcare Clinic',
          emailNotifications: true,
          assessmentAlerts: true,
          highRiskAlerts: true,
          weeklyReports: true,
          monthlyReports: true,
        },
      });
    }

    // Don't send sensitive data like passwords
    const { smtpPassword, ...safeSettings } = settings;

    res.json({
      success: true,
      data: safeSettings,
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/settings:
 *   put:
 *     summary: Update admin settings
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               smtpHost:
 *                 type: string
 *               smtpPort:
 *                 type: string
 *               smtpUser:
 *                 type: string
 *               smtpPassword:
 *                 type: string
 *               fromEmail:
 *                 type: string
 *               fromName:
 *                 type: string
 *               appName:
 *                 type: string
 *               clinicName:
 *                 type: string
 *               clinicAddress:
 *                 type: string
 *               clinicPhone:
 *                 type: string
 *               iosAppUrl:
 *                 type: string
 *               androidAppUrl:
 *                 type: string
 *               webAppUrl:
 *                 type: string
 *               emailNotifications:
 *                 type: boolean
 *               assessmentAlerts:
 *                 type: boolean
 *               highRiskAlerts:
 *                 type: boolean
 *               dailyReports:
 *                 type: boolean
 *               weeklyReports:
 *                 type: boolean
 *               monthlyReports:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *       500:
 *         description: Server error
 */
export const updateSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    const settingsData = req.body;

    let settings = await prisma.adminSettings.findFirst();

    if (settings) {
      settings = await prisma.adminSettings.update({
        where: { id: settings.id },
        data: settingsData,
      });
    } else {
      settings = await prisma.adminSettings.create({
        data: settingsData,
      });
    }

    // Don't send sensitive data like passwords
    const { smtpPassword, ...safeSettings } = settings;

    res.json({
      success: true,
      data: safeSettings,
      message: 'Settings updated successfully',
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/patients/{patientId}/cgm-data:
 *   get:
 *     summary: Get CGM data for a specific patient
 *     tags: [Admin]
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *     responses:
 *       200:
 *         description: CGM data for the patient
 *       404:
 *         description: Patient not found
 *       500:
 *         description: Server error
 */
export const getPatientCGMData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { patientId } = req.params;
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();
    const limit = parseInt(req.query.limit as string) || 100;

    // First, find the patient
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
    });

    if (!patient) {
      res.status(404).json({
        success: false,
        message: 'Patient not found',
      });
      return;
    }

    // Find the corresponding user by email
    const user = await prisma.user.findUnique({
      where: { email: patient.email },
      include: {
        profile: {
          include: {
            glucoseReadings: {
              where: {
                timestamp: {
                  gte: startDate,
                  lte: endDate,
                },
              },
              orderBy: {
                timestamp: 'desc',
              },
              take: limit,
            },
          },
        },
      },
    });

    if (!user || !user.profile) {
      res.json({
        success: true,
        data: {
          patient,
          cgmData: {
            readings: [],
            statistics: {
              totalReadings: 0,
              averageGlucose: 0,
              timeInRange: 0,
              timeAboveRange: 0,
              timeBelowRange: 0,
            },
          },
        },
      });
      return;
    }

    const readings = user.profile.glucoseReadings;

    // Calculate statistics
    const totalReadings = readings.length;
    const averageGlucose = totalReadings > 0
      ? readings.reduce((sum, reading) => sum + reading.value, 0) / totalReadings
      : 0;

    // Target range: 70-180 mg/dL (standard diabetes range)
    const targetMin = user.profile.glucoseTargetMin || 70;
    const targetMax = user.profile.glucoseTargetMax || 180;

    const inRange = readings.filter(r => r.value >= targetMin && r.value <= targetMax).length;
    const aboveRange = readings.filter(r => r.value > targetMax).length;
    const belowRange = readings.filter(r => r.value < targetMin).length;

    const timeInRange = totalReadings > 0 ? (inRange / totalReadings) * 100 : 0;
    const timeAboveRange = totalReadings > 0 ? (aboveRange / totalReadings) * 100 : 0;
    const timeBelowRange = totalReadings > 0 ? (belowRange / totalReadings) * 100 : 0;

    res.json({
      success: true,
      data: {
        patient,
        cgmData: {
          readings: readings.map(reading => ({
            id: reading.id,
            value: reading.value,
            timestamp: reading.timestamp,
            trend: reading.trend,
            trendRate: reading.trendRate,
            source: reading.source,
            notes: reading.notes,
          })),
          statistics: {
            totalReadings,
            averageGlucose: Math.round(averageGlucose * 10) / 10,
            timeInRange: Math.round(timeInRange * 10) / 10,
            timeAboveRange: Math.round(timeAboveRange * 10) / 10,
            timeBelowRange: Math.round(timeBelowRange * 10) / 10,
            targetRange: {
              min: targetMin,
              max: targetMax,
            },
          },
          dateRange: {
            startDate,
            endDate,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error fetching patient CGM data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch CGM data',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/admin/cgm-overview:
 *   get:
 *     summary: Get CGM overview for all patients
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *     responses:
 *       200:
 *         description: CGM overview data
 *       500:
 *         description: Server error
 */
export const getCGMOverview = async (req: Request, res: Response): Promise<void> => {
  try {
    const days = parseInt(req.query.days as string) || 7;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const endDate = new Date();

    // Get all patients with their corresponding users and CGM data
    const patients = await prisma.patient.findMany({
      include: {
        assessments: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });

    const cgmOverview = [];

    for (const patient of patients) {
      const user = await prisma.user.findUnique({
        where: { email: patient.email },
        include: {
          profile: {
            include: {
              glucoseReadings: {
                where: {
                  timestamp: {
                    gte: startDate,
                    lte: endDate,
                  },
                },
                orderBy: {
                  timestamp: 'desc',
                },
              },
            },
          },
        },
      });

      const readings = user?.profile?.glucoseReadings || [];
      const latestAssessment = patient.assessments[0];

      if (readings.length > 0) {
        const averageGlucose = readings.reduce((sum, reading) => sum + reading.value, 0) / readings.length;
        const latestReading = readings[0];

        // Calculate time in range
        const targetMin = user?.profile?.glucoseTargetMin || 70;
        const targetMax = user?.profile?.glucoseTargetMax || 180;
        const inRange = readings.filter(r => r.value >= targetMin && r.value <= targetMax).length;
        const timeInRange = (inRange / readings.length) * 100;

        cgmOverview.push({
          patient: {
            id: patient.id,
            firstName: patient.firstName,
            lastName: patient.lastName,
            email: patient.email,
            riskGroup: latestAssessment?.riskGroup || null,
            riskScore: latestAssessment?.riskScore || null,
          },
          cgmSummary: {
            totalReadings: readings.length,
            averageGlucose: Math.round(averageGlucose * 10) / 10,
            timeInRange: Math.round(timeInRange * 10) / 10,
            latestReading: {
              value: latestReading.value,
              timestamp: latestReading.timestamp,
              trend: latestReading.trend,
            },
            targetRange: {
              min: targetMin,
              max: targetMax,
            },
          },
        });
      }
    }

    // Sort by latest reading timestamp
    cgmOverview.sort((a, b) =>
      new Date(b.cgmSummary.latestReading.timestamp).getTime() -
      new Date(a.cgmSummary.latestReading.timestamp).getTime()
    );

    res.json({
      success: true,
      data: {
        overview: cgmOverview,
        summary: {
          totalPatientsWithCGM: cgmOverview.length,
          totalPatients: patients.length,
          dateRange: {
            startDate,
            endDate,
            days,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error fetching CGM overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch CGM overview',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
