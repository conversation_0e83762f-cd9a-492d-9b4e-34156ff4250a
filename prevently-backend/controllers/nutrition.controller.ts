import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import { Prisma, GlycemicLoadStatus } from '@prisma/client';

/**
 * Get daily nutrition log for the authenticated user
 * This endpoint is used by the mobile app's home screen
 */
export const getCurrentUserDailyLog = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }
    
    // Get date from query or use current date
    const dateParam = req.query.date as string;
    const date = dateParam ? new Date(dateParam) : new Date();
    
    // Set to start of day
    date.setHours(0, 0, 0, 0);
    
    // Find nutrition log for this date
    const dailyLog = await prisma.dailyNutritionLog.findFirst({
      where: {
        userId,
        date
      }
    });
    
    if (!dailyLog) {
      // If no log exists, calculate averages from recent meals
      const recentMeals = await prisma.meal.findMany({
        where: {
          userId,
          date: {
            gte: new Date(date.getTime() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            lte: date
          }
        },
        include: {
          mealItems: true
        }
      });
      
      // Calculate average GI and GL from recent meals
      let totalGI = 0;
      let totalGL = 0;
      let mealCount = 0;
      
      recentMeals.forEach(meal => {
        if (meal.averageGI) {
          totalGI += meal.averageGI;
          mealCount++;
        }
        if (meal.totalGL) {
          totalGL += meal.totalGL;
        }
      });
      
      const averageGI = mealCount > 0 ? totalGI / mealCount : 55; // Default to moderate if no data
      const averageGL = recentMeals.length > 0 ? totalGL / recentMeals.length : 15; // Default to moderate if no data
      
      // Determine glycemic load status
      let glStatus: GlycemicLoadStatus = GlycemicLoadStatus.MODERATE;
      if (averageGL < 10) {
        glStatus = GlycemicLoadStatus.GOOD;
      } else if (averageGL > 20) {
        glStatus = GlycemicLoadStatus.HIGH;
      }
      
      res.status(200).json({
        success: true,
        data: {
          date,
          userId,
          totalCalories: 0,
          totalCarbs: 0,
          totalProtein: 0,
          totalFat: 0,
          averageGI,
          totalGlycemicLoad: averageGL,
          glycemicLoadStatus: glStatus,
          isEstimated: true
        }
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: {
        ...dailyLog,
        averageGI: dailyLog.totalGlycemicLoad ? dailyLog.totalGlycemicLoad / 2 : 55, // Estimate from GL if not available
        isEstimated: false
      }
    });
  } catch (error: any) {
    console.error('Error getting current user daily nutrition log:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving daily nutrition log',
      error: error.message
    });
  }
};

/**
 * Get daily nutrition log for a user on a specific date
 */
export const getDailyNutrition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, date } = req.params;
    
    const dailyLog = await prisma.dailyNutritionLog.findFirst({
      where: {
        userId,
        date: new Date(date)
      }
    });
    
    if (!dailyLog) {
      res.status(404).json({
        success: false,
        message: 'Daily nutrition log not found for this date'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: dailyLog
    });
  } catch (error: any) {
    console.error('Error getting daily nutrition log:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving daily nutrition log',
      error: error.message
    });
  }
};

/**
 * Get daily nutrition logs for a user within a date range
 */
export const getDailyNutritionRange = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { startDate, endDate, limit = 30, page = 1 } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    // Build filter conditions
    const where: Prisma.DailyNutritionLogWhereInput = {
      userId
    };
    
    // Filter by date range
    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string)
      };
    }
    
    // Get total count for pagination
    const totalCount = await prisma.dailyNutritionLog.count({ where });
    
    // Get logs with pagination
    const logs = await prisma.dailyNutritionLog.findMany({
      where,
      orderBy: {
        date: 'desc'
      },
      skip,
      take: Number(limit)
    });
    
    res.status(200).json({
      success: true,
      count: logs.length,
      totalCount,
      totalPages: Math.ceil(totalCount / Number(limit)),
      currentPage: Number(page),
      data: logs
    });
  } catch (error: any) {
    console.error('Error getting daily nutrition logs:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving daily nutrition logs',
      error: error.message
    });
  }
};

/**
 * Create or update daily nutrition log
 * This can be called manually or automatically when meals are added/updated
 */
export const updateDailyNutrition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, date } = req.params;
    const { 
      caloriesGoal, 
      carbsGoal, 
      proteinGoal, 
      fatGoal 
    } = req.body;
    
    // Parse date
    const targetDate = new Date(date);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);
    
    // Calculate nutrition totals from all meals on this date
    const meals = await prisma.meal.findMany({
      where: {
        userId,
        date: {
          gte: targetDate,
          lt: nextDay
        }
      }
    });
    
    let totalCalories = 0;
    let totalCarbs = 0;
    let totalProtein = 0;
    let totalFat = 0;
    let totalGlycemicLoad = 0;
    
    meals.forEach(meal => {
      if (meal.totalCalories) totalCalories += meal.totalCalories;
      if (meal.totalCarbs) totalCarbs += meal.totalCarbs;
      if (meal.totalProtein) totalProtein += meal.totalProtein;
      if (meal.totalFat) totalFat += meal.totalFat;
      if (meal.totalGL) totalGlycemicLoad += meal.totalGL;
    });
    
    // Determine glycemic load status
    let glycemicLoadStatus: GlycemicLoadStatus = GlycemicLoadStatus.GOOD;
    
    if (totalGlycemicLoad > 120) {
      glycemicLoadStatus = GlycemicLoadStatus.HIGH;
    } else if (totalGlycemicLoad > 80) {
      glycemicLoadStatus = GlycemicLoadStatus.MODERATE;
    }
    
    // Create or update daily log
    const dailyLog = await prisma.dailyNutritionLog.upsert({
      where: {
        date_userId: {
          date: targetDate,
          userId
        }
      },
      update: {
        totalCalories,
        totalCarbs,
        totalProtein,
        totalFat,
        totalGlycemicLoad,
        glycemicLoadStatus,
        caloriesGoal: caloriesGoal !== undefined ? Number(caloriesGoal) : undefined,
        carbsGoal: carbsGoal !== undefined ? Number(carbsGoal) : undefined,
        proteinGoal: proteinGoal !== undefined ? Number(proteinGoal) : undefined,
        fatGoal: fatGoal !== undefined ? Number(fatGoal) : undefined
      },
      create: {
        date: targetDate,
        userId,
        totalCalories,
        totalCarbs,
        totalProtein,
        totalFat,
        totalGlycemicLoad,
        glycemicLoadStatus,
        caloriesGoal: caloriesGoal ? Number(caloriesGoal) : null,
        carbsGoal: carbsGoal ? Number(carbsGoal) : null,
        proteinGoal: proteinGoal ? Number(proteinGoal) : null,
        fatGoal: fatGoal ? Number(fatGoal) : null
      }
    });
    
    res.status(200).json({
      success: true,
      message: 'Daily nutrition log updated successfully',
      data: dailyLog
    });
  } catch (error: any) {
    console.error('Error updating daily nutrition log:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating daily nutrition log',
      error: error.message
    });
  }
};

/**
 * Get nutrition summary for a date range
 */
export const getNutritionSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }
    
    // Get logs within date range
    const logs = await prisma.dailyNutritionLog.findMany({
      where: {
        userId,
        date: {
          gte: new Date(startDate as string),
          lte: new Date(endDate as string)
        }
      },
      orderBy: {
        date: 'asc'
      }
    });
    
    // Calculate averages
    const totalDays = logs.length;
    
    if (totalDays === 0) {
      res.status(404).json({
        success: false,
        message: 'No nutrition logs found for this date range'
      });
      return;
    }
    
    const summary = {
      totalDays,
      averageCalories: 0,
      averageCarbs: 0,
      averageProtein: 0,
      averageFat: 0,
      averageGlycemicLoad: 0,
      caloriesGoalAchievement: 0,
      carbsGoalAchievement: 0,
      proteinGoalAchievement: 0,
      fatGoalAchievement: 0,
      glycemicLoadDistribution: {
        good: 0,
        moderate: 0,
        high: 0
      },
      dailyData: logs.map(log => ({
        date: log.date,
        calories: log.totalCalories,
        carbs: log.totalCarbs,
        protein: log.totalProtein,
        fat: log.totalFat,
        glycemicLoad: log.totalGlycemicLoad,
        glycemicLoadStatus: log.glycemicLoadStatus
      }))
    };
    
    // Calculate totals
    let totalCalories = 0;
    let totalCarbs = 0;
    let totalProtein = 0;
    let totalFat = 0;
    let totalGlycemicLoad = 0;
    
    // Goal achievement counters
    let caloriesGoalDays = 0;
    let caloriesGoalAchieved = 0;
    let carbsGoalDays = 0;
    let carbsGoalAchieved = 0;
    let proteinGoalDays = 0;
    let proteinGoalAchieved = 0;
    let fatGoalDays = 0;
    let fatGoalAchieved = 0;
    
    // Glycemic load distribution
    let goodGLDays = 0;
    let moderateGLDays = 0;
    let highGLDays = 0;
    
    logs.forEach(log => {
      // Add to totals
      totalCalories += log.totalCalories;
      totalCarbs += log.totalCarbs;
      totalProtein += log.totalProtein;
      totalFat += log.totalFat;
      if (log.totalGlycemicLoad) totalGlycemicLoad += log.totalGlycemicLoad;
      
      // Check goal achievements
      if (log.caloriesGoal) {
        caloriesGoalDays++;
        if (log.totalCalories <= log.caloriesGoal) caloriesGoalAchieved++;
      }
      
      if (log.carbsGoal) {
        carbsGoalDays++;
        if (log.totalCarbs <= log.carbsGoal) carbsGoalAchieved++;
      }
      
      if (log.proteinGoal) {
        proteinGoalDays++;
        if (log.totalProtein >= log.proteinGoal) proteinGoalAchieved++;
      }
      
      if (log.fatGoal) {
        fatGoalDays++;
        if (log.totalFat <= log.fatGoal) fatGoalAchieved++;
      }
      
      // Count glycemic load status
      if (log.glycemicLoadStatus === GlycemicLoadStatus.GOOD) goodGLDays++;
      else if (log.glycemicLoadStatus === GlycemicLoadStatus.MODERATE) moderateGLDays++;
      else if (log.glycemicLoadStatus === GlycemicLoadStatus.HIGH) highGLDays++;
    });
    
    // Calculate averages
    summary.averageCalories = Math.round(totalCalories / totalDays);
    summary.averageCarbs = parseFloat((totalCarbs / totalDays).toFixed(1));
    summary.averageProtein = parseFloat((totalProtein / totalDays).toFixed(1));
    summary.averageFat = parseFloat((totalFat / totalDays).toFixed(1));
    summary.averageGlycemicLoad = parseFloat((totalGlycemicLoad / totalDays).toFixed(1));
    
    // Calculate goal achievements
    summary.caloriesGoalAchievement = caloriesGoalDays > 0 ? parseFloat(((caloriesGoalAchieved / caloriesGoalDays) * 100).toFixed(1)) : 0;
    summary.carbsGoalAchievement = carbsGoalDays > 0 ? parseFloat(((carbsGoalAchieved / carbsGoalDays) * 100).toFixed(1)) : 0;
    summary.proteinGoalAchievement = proteinGoalDays > 0 ? parseFloat(((proteinGoalAchieved / proteinGoalDays) * 100).toFixed(1)) : 0;
    summary.fatGoalAchievement = fatGoalDays > 0 ? parseFloat(((fatGoalAchieved / fatGoalDays) * 100).toFixed(1)) : 0;
    
    // Calculate glycemic load distribution
    summary.glycemicLoadDistribution.good = parseFloat(((goodGLDays / totalDays) * 100).toFixed(1));
    summary.glycemicLoadDistribution.moderate = parseFloat(((moderateGLDays / totalDays) * 100).toFixed(1));
    summary.glycemicLoadDistribution.high = parseFloat(((highGLDays / totalDays) * 100).toFixed(1));
    
    res.status(200).json({
      success: true,
      data: summary
    });
  } catch (error: any) {
    console.error('Error getting nutrition summary:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving nutrition summary',
      error: error.message
    });
  }
};
