import { Request, Response } from 'express';
import axios from 'axios';
import prisma from '../services/prisma.service';

/**
 * Get personalized AI response using OpenAI's GPT model
 */
export const getAIResponse = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, message, conversationHistory = [] } = req.body;
    
    if (!userId || !message) {
      res.status(400).json({
        success: false,
        message: 'User ID and message are required'
      });
      return;
    }
    
    // Get user data to personalize the AI response
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            medications: true,
            allergies: true,
            healthcareProvider: true,
            preferences: true,
            connectedDevices: true,
            glucoseReadings: {
              orderBy: {
                timestamp: 'desc'
              },
              take: 50
            }
          }
        }
      }
    });
    
    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }
    
    // Get user's recent meals for context
    const recentMeals = await prisma.meal.findMany({
      where: { 
        userId,
        date: {
          gte: new Date(new Date().setDate(new Date().getDate() - 7)) // Last 7 days
        }
      },
      include: {
        mealItems: {
          include: {
            food: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      take: 5
    });
    
    // Get user's nutrition logs for context
    const nutritionLogs = await prisma.dailyNutritionLog.findMany({
      where: {
        userId,
        date: {
          gte: new Date(new Date().setDate(new Date().getDate() - 7)) // Last 7 days
        }
      },
      orderBy: {
        date: 'desc'
      },
      take: 7
    });
    
    // Calculate glucose statistics if CGM data is available
    let glucoseStats = null;
    if (user?.profile?.glucoseReadings && user.profile.glucoseReadings.length > 0) {
      const readings = user.profile.glucoseReadings;
      
      // Calculate average glucose, time in range, etc.
      const totalGlucose = readings.reduce((sum, reading) => sum + reading.value, 0);
      const avgGlucose = totalGlucose / readings.length;
      
      // Calculate time in range based on user's target range or default values
      const minTarget = user.profile.glucoseTargetMin || 70;
      const maxTarget = user.profile.glucoseTargetMax || 140;
      
      const readingsInRange = readings.filter(r => r.value >= minTarget && r.value <= maxTarget).length;
      const readingsAboveRange = readings.filter(r => r.value > maxTarget).length;
      const readingsBelowRange = readings.filter(r => r.value < minTarget).length;
      
      const percentInRange = (readingsInRange / readings.length) * 100;
      const percentAboveRange = (readingsAboveRange / readings.length) * 100;
      const percentBelowRange = (readingsBelowRange / readings.length) * 100;
      
      // Get latest reading
      const latestReading = readings[0];
      
      glucoseStats = {
        averageGlucose: Math.round(avgGlucose),
        percentInRange: Math.round(percentInRange),
        percentAboveRange: Math.round(percentAboveRange),
        percentBelowRange: Math.round(percentBelowRange),
        latestReading: {
          value: latestReading.value,
          timestamp: latestReading.timestamp,
          trend: latestReading.trend
        },
        targetRange: {
          min: minTarget,
          max: maxTarget
        }
      };
    }
    
    // Create a user context object for the AI
    const userContext = {
      name: user.name || 'User',
      email: user.email,
      profile: {
        age: user.profile?.age,
        gender: user.profile?.gender,
        condition: user.profile?.condition,
        weight: user.profile?.weight,
        height: user.profile?.height,
        bmi: user.profile?.bmi,
        glucoseTargets: {
          min: user.profile?.glucoseTargetMin || 70,
          max: user.profile?.glucoseTargetMax || 140
        }
      },
      medications: user.profile?.medications?.map(med => ({
        name: med.name,
        dosage: med.dosage,
        frequency: med.frequency
      })) || [],
      allergies: user.profile?.allergies?.map(allergy => ({
        name: allergy.name,
        severity: allergy.severity
      })) || [],
      recentMeals: recentMeals.map(meal => ({
        id: meal.id,
        name: meal.name,
        date: meal.date,
        totalCalories: meal.totalCalories,
        totalCarbs: meal.totalCarbs,
        totalProtein: meal.totalProtein,
        totalFat: meal.totalFat,
        averageGI: meal.averageGI,
        totalGL: meal.totalGL,
        foods: meal.mealItems.map(item => ({
          name: item.food.name,
          quantity: item.quantity,
          calories: item.calories,
          carbs: item.carbs,
          protein: item.protein,
          fat: item.fat
        }))
      })),
      nutritionSummary: {
        averageCalories: Math.round(nutritionLogs.reduce((sum, log) => sum + log.totalCalories, 0) / (nutritionLogs.length || 1)),
        averageCarbs: Math.round(nutritionLogs.reduce((sum, log) => sum + log.totalCarbs, 0) / (nutritionLogs.length || 1)),
        averageProtein: Math.round(nutritionLogs.reduce((sum, log) => sum + log.totalProtein, 0) / (nutritionLogs.length || 1)),
        averageFat: Math.round(nutritionLogs.reduce((sum, log) => sum + log.totalFat, 0) / (nutritionLogs.length || 1)),
        averageGlycemicLoad: Math.round(nutritionLogs.reduce((sum, log) => sum + (log.totalGlycemicLoad || 0), 0) / (nutritionLogs.length || 1))
      },
      glucoseData: glucoseStats
    };
    
    // Build the messages array for the API request
    const messages = [
      {
        role: 'system',
        content: `You are a health and nutrition assistant for Prevently, an app focused on helping users manage prediabetes and diabetes through proper nutrition and lifestyle changes. Your advice is based on scientific research and particularly the principles from the book "Sucre l'ennemi public n°1" (Sugar: Public Enemy #1).
        
You have access to the following information about the user:
- Name: ${userContext.name}
- Profile: ${JSON.stringify(userContext.profile)}
- Medications: ${JSON.stringify(userContext.medications)}
- Allergies: ${JSON.stringify(userContext.allergies)}
- Recent meals: ${JSON.stringify(userContext.recentMeals)}
- Nutrition summary: ${JSON.stringify(userContext.nutritionSummary)}
${userContext.glucoseData ? `- Glucose data: ${JSON.stringify(userContext.glucoseData)}` : ''}

KEY PRINCIPLES FROM "SUCRE L'ENNEMI PUBLIC N°1":
1. Sugar is highly addictive and has harmful effects similar to tobacco and alcohol
2. Excessive sugar consumption leads to insulin resistance, obesity, and metabolic disorders
3. Hidden sugars in processed foods are particularly dangerous
4. Reducing sugar intake can reverse many health conditions including prediabetes
5. Low glycemic index foods are preferable to high GI foods
6. Natural whole foods are superior to processed alternatives
7. Combining protein, healthy fats, and fiber with carbohydrates helps reduce glycemic impact

GUIDELINES FOR YOUR RESPONSES:
1. Provide personalized advice based on the user's data, especially their CGM readings if available
2. Recommend low glycemic index foods and balanced meals
3. Suggest specific meal plans that align with the user's preferences and health goals
4. Interpret glucose data and suggest adjustments to diet and lifestyle
5. Be mindful of the user's medications and allergies when making recommendations
6. Keep responses concise (under 200 words) and actionable
7. When discussing nutrition, reference the user's actual meal data when relevant
8. Explain the impact of specific foods on blood sugar levels based on the book's principles

Always be supportive and positive, acknowledging the user's efforts and progress.`
      },
      ...conversationHistory,
      {
        role: 'user',
        content: message
      }
    ];
    
    // Call OpenAI API
    const openaiResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-4o',
        messages,
        temperature: 0.7,
        max_tokens: 300
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
        }
      }
    );
    
    // Save the conversation to the database
    const conversation = await prisma.aIConversation.upsert({
      where: {
        userId
      },
      update: {
        messages: {
          create: [
            {
              content: message,
              role: 'user',
              timestamp: new Date()
            },
            {
              content: openaiResponse.data.choices[0].message.content,
              role: 'assistant',
              timestamp: new Date()
            }
          ]
        }
      },
      create: {
        userId,
        messages: {
          create: [
            {
              content: message,
              role: 'user',
              timestamp: new Date()
            },
            {
              content: openaiResponse.data.choices[0].message.content,
              role: 'assistant',
              timestamp: new Date()
            }
          ]
        }
      }
    });
    
    // Generate contextual suggestions based on the message and response
    const suggestionsResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'Generate 3 follow-up question suggestions based on the conversation. Each suggestion should be concise (under 10 words) and relevant to the user\'s health and nutrition goals. Format as a JSON array of strings.'
          },
          {
            role: 'user',
            content: `User message: "${message}"\nAssistant response: "${openaiResponse.data.choices[0].message.content}"`
          }
        ],
        temperature: 0.7,
        max_tokens: 150,
        response_format: { type: 'json_object' }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
        }
      }
    );
    
    // Parse suggestions from the response
    let suggestions = [];
    try {
      const suggestionsData = JSON.parse(suggestionsResponse.data.choices[0].message.content);
      suggestions = suggestionsData.suggestions || [];
    } catch (error) {
      console.error('Error parsing suggestions:', error);
      suggestions = [
        'How can I improve my diet?',
        'What exercises are recommended?',
        'Tell me more about glycemic index'
      ];
    }
    
    res.status(200).json({
      success: true,
      data: {
        message: openaiResponse.data.choices[0].message.content,
        suggestions
      }
    });
  } catch (error: any) {
    console.error('Error getting AI response:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting AI response',
      error: error.message
    });
  }
};

/**
 * Get conversation history for a user
 */
export const getConversationHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    // Get conversation history
    const conversation = await prisma.aIConversation.findUnique({
      where: { userId },
      include: {
        messages: {
          orderBy: {
            timestamp: 'asc'
          }
        }
      }
    });
    
    if (!conversation) {
      res.status(200).json({
        success: true,
        data: {
          messages: []
        }
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: {
        messages: conversation.messages
      }
    });
  } catch (error: any) {
    console.error('Error getting conversation history:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting conversation history',
      error: error.message
    });
  }
};

/**
 * Clear conversation history for a user
 */
export const clearConversationHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    // Delete conversation and messages
    await prisma.aIConversationMessage.deleteMany({
      where: {
        conversation: {
          userId
        }
      }
    });
    
    await prisma.aIConversation.delete({
      where: { userId }
    });
    
    res.status(200).json({
      success: true,
      message: 'Conversation history cleared successfully'
    });
  } catch (error: any) {
    console.error('Error clearing conversation history:', error);
    res.status(500).json({
      success: false,
      message: 'Error clearing conversation history',
      error: error.message
    });
  }
};
