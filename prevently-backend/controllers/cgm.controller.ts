import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import dexcomService, { DexcomGlucoseReading, DexcomStatistics } from '../services/dexcom.service';

/**
 * Authenticate with Dexcom API
 */
export const authenticateDexcom = async (req: Request, res: Response): Promise<void> => {
  try {
    const { code, redirectUri } = req.body;
    
    if (!code || !redirectUri) {
      res.status(400).json({
        success: false,
        message: 'Authorization code and redirect URI are required'
      });
      return;
    }
    
    const authResponse = await dexcomService.authenticate(code, redirectUri);
    
    // Store the user's Dexcom connection in the database
    // We'll use the userId from the authenticated user
    const userId = req.user?.id;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }
    
    // Get the user's profile
    const profile = await prisma.profile.findUnique({
      where: { userId },
      include: { connectedDevices: true }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'User profile not found'
      });
      return;
    }
    
    // Add or update Dexcom as a connected device
    const existingDevice = profile.connectedDevices.find(device => device.deviceType === 'Dexcom CGM');
    
    if (existingDevice) {
      await prisma.connectedDevice.update({
        where: { id: existingDevice.id },
        data: {
          connected: true,
          lastSyncDate: new Date()
        }
      });
    } else {
      await prisma.connectedDevice.create({
        data: {
          name: 'Dexcom CGM',
          deviceType: 'Dexcom CGM',
          connected: true,
          lastSyncDate: new Date(),
          profileId: profile.id
        }
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Successfully authenticated with Dexcom',
      data: {
        accessToken: authResponse.access_token,
        expiresIn: authResponse.expires_in,
        tokenType: authResponse.token_type
      }
    });
  } catch (error: any) {
    console.error('Error authenticating with Dexcom:', error);
    res.status(500).json({
      success: false,
      message: 'Error authenticating with Dexcom',
      error: error.message
    });
  }
};

/**
 * Get Dexcom authorization URL
 */
export const getDexcomAuthUrl = async (req: Request, res: Response): Promise<void> => {
  try {
    const { redirectUri } = req.query;
    
    if (!redirectUri || typeof redirectUri !== 'string') {
      res.status(400).json({
        success: false,
        message: 'Redirect URI is required'
      });
      return;
    }
    
    // Generate a state parameter for security (can be stored in session)
    const state = Math.random().toString(36).substring(2, 15);
    
    const authUrl = dexcomService.getAuthorizationUrl(redirectUri, state);
    
    res.status(200).json({
      success: true,
      data: {
        authUrl,
        state
      }
    });
  } catch (error: any) {
    console.error('Error generating Dexcom auth URL:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating Dexcom auth URL',
      error: error.message
    });
  }
};

/**
 * Get glucose readings for a specific date range
 */
export const getGlucoseReadings = async (req: Request, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate || typeof startDate !== 'string' || typeof endDate !== 'string') {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }
    
    const readings = await dexcomService.getGlucoseReadings(startDate, endDate);
    
    res.status(200).json({
      success: true,
      data: readings
    });
  } catch (error: any) {
    console.error('Error fetching glucose readings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching glucose readings',
      error: error.message
    });
  }
};

/**
 * Get the latest glucose reading
 */
export const getLatestGlucoseReading = async (req: Request, res: Response): Promise<void> => {
  try {
    const reading = await dexcomService.getLatestGlucoseReading();
    
    res.status(200).json({
      success: true,
      data: reading
    });
  } catch (error: any) {
    console.error('Error fetching latest glucose reading:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching latest glucose reading',
      error: error.message
    });
  }
};

/**
 * Get statistics for glucose readings in a specific date range
 */
export const getGlucoseStatistics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;
    const targetRangeMin = req.query.targetRangeMin ? parseInt(req.query.targetRangeMin as string) : undefined;
    const targetRangeMax = req.query.targetRangeMax ? parseInt(req.query.targetRangeMax as string) : undefined;
    
    if (!startDate || !endDate || typeof startDate !== 'string' || typeof endDate !== 'string') {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }
    
    const statistics = await dexcomService.getStatistics(
      startDate, 
      endDate, 
      targetRangeMin, 
      targetRangeMax
    );
    
    res.status(200).json({
      success: true,
      data: statistics
    });
  } catch (error: any) {
    console.error('Error fetching glucose statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching glucose statistics',
      error: error.message
    });
  }
};

/**
 * Get Dexcom devices
 */
export const getDexcomDevices = async (req: Request, res: Response): Promise<void> => {
  try {
    const devices = await dexcomService.getDevices();
    
    res.status(200).json({
      success: true,
      data: devices
    });
  } catch (error: any) {
    console.error('Error fetching Dexcom devices:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching Dexcom devices',
      error: error.message
    });
  }
};

/**
 * Store glucose reading in the database
 */
export const storeGlucoseReading = async (req: Request, res: Response): Promise<void> => {
  try {
    const { value, timestamp, userId } = req.body;
    
    if (!value || !timestamp || !userId) {
      res.status(400).json({
        success: false,
        message: 'Glucose value, timestamp, and user ID are required'
      });
      return;
    }
    
    // Get the user's profile
    const profile = await prisma.profile.findUnique({
      where: { userId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'User profile not found'
      });
      return;
    }
    
    // Create a new glucose reading model in the database
    const glucoseReading = await prisma.glucoseReading.create({
      data: {
        value: parseFloat(value),
        timestamp: new Date(timestamp),
        profileId: profile.id
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Glucose reading stored successfully',
      data: glucoseReading
    });
  } catch (error: any) {
    console.error('Error storing glucose reading:', error);
    res.status(500).json({
      success: false,
      message: 'Error storing glucose reading',
      error: error.message
    });
  }
};

/**
 * Sync glucose readings from Dexcom to the database
 */
export const syncGlucoseReadings = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, days = 1 } = req.body;
    
    if (!userId) {
      res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
      return;
    }
    
    // Get the user's profile
    const profile = await prisma.profile.findUnique({
      where: { userId }
    });
    
    if (!profile) {
      res.status(404).json({
        success: false,
        message: 'User profile not found'
      });
      return;
    }
    
    // Calculate date range for sync
    const endDate = new Date().toISOString();
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();
    
    // Get readings from Dexcom API
    const readings = await dexcomService.getGlucoseReadings(startDate, endDate);
    
    // Store readings in the database
    const storedReadings = await Promise.all(
      readings.map(async (reading: DexcomGlucoseReading) => {
        // Check if reading already exists to avoid duplicates
        const existingReading = await prisma.glucoseReading.findFirst({
          where: {
            profileId: profile.id,
            timestamp: new Date(reading.displayTime)
          }
        });
        
        if (!existingReading) {
          return prisma.glucoseReading.create({
            data: {
              value: reading.value,
              timestamp: new Date(reading.displayTime),
              profileId: profile.id,
              trend: reading.trend,
              trendRate: reading.trendRate
            }
          });
        }
        
        return existingReading;
      })
    );
    
    // Update the connected device's last sync date
    const dexcomDevice = await prisma.connectedDevice.findFirst({
      where: {
        profileId: profile.id,
        deviceType: 'Dexcom CGM'
      }
    });
    
    if (dexcomDevice) {
      await prisma.connectedDevice.update({
        where: { id: dexcomDevice.id },
        data: {
          lastSyncDate: new Date()
        }
      });
    }
    
    res.status(200).json({
      success: true,
      message: `Successfully synced ${storedReadings.length} glucose readings`,
      data: {
        syncedReadings: storedReadings.length,
        timeRange: {
          startDate,
          endDate
        }
      }
    });
  } catch (error: any) {
    console.error('Error syncing glucose readings:', error);
    res.status(500).json({
      success: false,
      message: 'Error syncing glucose readings',
      error: error.message
    });
  }
};
