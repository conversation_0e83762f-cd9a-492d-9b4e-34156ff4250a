import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Get all reminders for the authenticated user
 * @route GET /api/reminders
 * @access Private
 */
export const getReminders = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { date } = req.query;

    // Build filter conditions
    const where: any = { userId };
    
    // Add date filter if provided
    if (date) {
      const filterDate = new Date(date as string);
      where.date = {
        gte: new Date(filterDate.setHours(0, 0, 0, 0)),
        lt: new Date(filterDate.setHours(23, 59, 59, 999))
      };
    }

    const reminders = await prisma.reminder.findMany({
      where,
      orderBy: { time: 'asc' }
    });

    res.status(200).json(reminders);
  } catch (error) {
    console.error('Error fetching reminders:', error);
    res.status(500).json({ message: 'Failed to fetch reminders' });
  }
};

/**
 * Get a reminder by ID
 * @route GET /api/reminders/:id
 * @access Private
 */
export const getReminderById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    const reminder = await prisma.reminder.findFirst({
      where: { id, userId }
    });

    if (!reminder) {
      res.status(404).json({ message: 'Reminder not found' });
      return;
    }

    res.status(200).json(reminder);
  } catch (error) {
    console.error('Error fetching reminder:', error);
    res.status(500).json({ message: 'Failed to fetch reminder' });
  }
};

/**
 * Create a new reminder
 * @route POST /api/reminders
 * @access Private
 */
export const createReminder = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      res.status(401).json({ message: 'User not authenticated' });
      return;
    }
    
    const { title, time, date, type } = req.body;

    // Validate required fields
    if (!title || !time || !type) {
      res.status(400).json({ message: 'Title, time, and type are required' });
      return;
    }

    // Validate reminder type
    const validTypes = ['medication', 'measurement', 'activity', 'appointment'];
    if (!validTypes.includes(type)) {
      res.status(400).json({ message: 'Invalid reminder type' });
      return;
    }

    // Create reminder
    const reminder = await prisma.reminder.create({
      data: {
        id: uuidv4(),
        title,
        time,
        date: date ? new Date(date) : new Date(),
        type,
        completed: false,
        userId
      }
    });

    res.status(201).json(reminder);
  } catch (error) {
    console.error('Error creating reminder:', error);
    res.status(500).json({ message: 'Failed to create reminder' });
  }
};

/**
 * Update a reminder
 * @route PATCH /api/reminders/:id
 * @access Private
 */
export const updateReminder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const { title, time, date, type, completed } = req.body;

    // Check if reminder exists and belongs to user
    const existingReminder = await prisma.reminder.findFirst({
      where: { id, userId }
    });

    if (!existingReminder) {
      res.status(404).json({ message: 'Reminder not found' });
      return;
    }

    // Validate reminder type if provided
    if (type) {
      const validTypes = ['medication', 'measurement', 'activity', 'appointment'];
      if (!validTypes.includes(type)) {
        res.status(400).json({ message: 'Invalid reminder type' });
        return;
      }
    }

    // Update reminder
    const updatedReminder = await prisma.reminder.update({
      where: { id },
      data: {
        ...(title && { title }),
        ...(time && { time }),
        ...(date && { date: new Date(date) }),
        ...(type && { type }),
        ...(completed !== undefined && { completed })
      }
    });

    res.status(200).json(updatedReminder);
  } catch (error) {
    console.error('Error updating reminder:', error);
    res.status(500).json({ message: 'Failed to update reminder' });
  }
};

/**
 * Delete a reminder
 * @route DELETE /api/reminders/:id
 * @access Private
 */
export const deleteReminder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Check if reminder exists and belongs to user
    const existingReminder = await prisma.reminder.findFirst({
      where: { id, userId }
    });

    if (!existingReminder) {
      res.status(404).json({ message: 'Reminder not found' });
      return;
    }

    // Delete reminder
    await prisma.reminder.delete({
      where: { id }
    });

    res.status(200).json({ message: 'Reminder deleted successfully' });
  } catch (error) {
    console.error('Error deleting reminder:', error);
    res.status(500).json({ message: 'Failed to delete reminder' });
  }
};
