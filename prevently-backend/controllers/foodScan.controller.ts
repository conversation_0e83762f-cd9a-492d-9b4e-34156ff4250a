import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import openFoodFactsService from '../services/openFoodFacts.service';
import { Prisma } from '@prisma/client';

/**
 * Scan food by barcode
 * This endpoint will check our database first, then query Open Food Facts if not found
 */
export const scanFoodByBarcode = async (req: Request, res: Response): Promise<void> => {
  try {
    const { barcode } = req.params;
    
    if (!barcode) {
      res.status(400).json({
        success: false,
        message: 'Barcode is required'
      });
      return;
    }
    
    // First, check if we already have this food in our database
    let food = await prisma.food.findUnique({
      where: { barcode },
      include: {
        foodCategory: true
      }
    });
    
    // If found in our database, return it
    if (food) {
      res.status(200).json({
        success: true,
        source: 'database',
        data: food
      });
      return;
    }
    
    // If not found, query Open Food Facts API
    const product = await openFoodFactsService.getProductByBarcode(barcode);
    
    if (!product) {
      res.status(404).json({
        success: false,
        message: 'Food not found with this barcode'
      });
      return;
    }
    
    // Map the product data to our food model format
    const mappedFood = openFoodFactsService.mapProductToFood(product);
    
    if (!mappedFood) {
      res.status(500).json({
        success: false,
        message: 'Error mapping product data'
      });
      return;
    }
    
    // Check if we need to create a food category
    let foodCategoryId = null;
    if (mappedFood.foodCategory) {
      // Try to find existing category with similar name
      const existingCategory = await prisma.foodCategory.findFirst({
        where: {
          name: {
            contains: mappedFood.foodCategory,
            mode: 'insensitive'
          }
        }
      });
      
      if (existingCategory) {
        foodCategoryId = existingCategory.id;
      } else {
        // Create new category
        const newCategory = await prisma.foodCategory.create({
          data: {
            name: mappedFood.foodCategory,
            description: `Category for ${mappedFood.foodCategory} foods`
          }
        });
        foodCategoryId = newCategory.id;
      }
    }
    
    // Save the food to our database for future use
    const savedFood = await prisma.food.create({
      data: {
        name: mappedFood.name,
        description: mappedFood.description,
        calories: mappedFood.calories,
        carbs: mappedFood.carbs,
        protein: mappedFood.protein,
        fat: mappedFood.fat,
        glycemicIndex: mappedFood.glycemicIndex,
        servingSize: mappedFood.servingSize,
        servingSizeUnit: mappedFood.servingSizeUnit,
        barcode: mappedFood.barcode,
        imageUrl: mappedFood.imageUrl,
        isVerified: true,
        isCustom: false,
        foodCategoryId
      },
      include: {
        foodCategory: true
      }
    });
    
    res.status(200).json({
      success: true,
      source: 'open_food_facts',
      data: savedFood
    });
  } catch (error: any) {
    console.error('Error scanning food:', error);
    res.status(500).json({
      success: false,
      message: 'Error scanning food',
      error: error.message
    });
  }
};

/**
 * Search foods from Open Food Facts
 */
export const searchOpenFoodFacts = async (req: Request, res: Response): Promise<void> => {
  try {
    const { query, page = 1, pageSize = 20 } = req.query;
    
    if (!query) {
      res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
      return;
    }
    
    const results = await openFoodFactsService.searchProducts(
      String(query),
      Number(page),
      Number(pageSize)
    );
    
    // Map results to our food model format
    const mappedResults = results.products
      ? results.products
          .map(product => openFoodFactsService.mapProductToFood(product))
          .filter(food => food !== null)
      : [];
    
    res.status(200).json({
      success: true,
      count: mappedResults.length,
      totalCount: results.count || 0,
      currentPage: Number(page),
      totalPages: Math.ceil((results.count || 0) / Number(pageSize)),
      data: mappedResults
    });
  } catch (error: any) {
    console.error('Error searching Open Food Facts:', error);
    res.status(500).json({
      success: false,
      message: 'Error searching Open Food Facts',
      error: error.message
    });
  }
};

/**
 * Import food from Open Food Facts to our database
 */
export const importFoodFromOpenFoodFacts = async (req: Request, res: Response): Promise<void> => {
  try {
    const { barcode } = req.body;
    
    if (!barcode) {
      res.status(400).json({
        success: false,
        message: 'Barcode is required'
      });
      return;
    }
    
    // Check if already in database
    const existingFood = await prisma.food.findUnique({
      where: { barcode }
    });
    
    if (existingFood) {
      res.status(400).json({
        success: false,
        message: 'Food with this barcode already exists in the database'
      });
      return;
    }
    
    // Fetch from Open Food Facts
    const product = await openFoodFactsService.getProductByBarcode(barcode);
    
    if (!product) {
      res.status(404).json({
        success: false,
        message: 'Food not found with this barcode in Open Food Facts'
      });
      return;
    }
    
    // Map the product data
    const mappedFood = openFoodFactsService.mapProductToFood(product);
    
    if (!mappedFood) {
      res.status(500).json({
        success: false,
        message: 'Error mapping product data'
      });
      return;
    }
    
    // Handle food category
    let foodCategoryId = null;
    if (mappedFood.foodCategory) {
      const existingCategory = await prisma.foodCategory.findFirst({
        where: {
          name: {
            contains: mappedFood.foodCategory,
            mode: 'insensitive'
          }
        }
      });
      
      if (existingCategory) {
        foodCategoryId = existingCategory.id;
      } else {
        const newCategory = await prisma.foodCategory.create({
          data: {
            name: mappedFood.foodCategory,
            description: `Category for ${mappedFood.foodCategory} foods`
          }
        });
        foodCategoryId = newCategory.id;
      }
    }
    
    // Save to database
    const savedFood = await prisma.food.create({
      data: {
        name: mappedFood.name,
        description: mappedFood.description,
        calories: mappedFood.calories,
        carbs: mappedFood.carbs,
        protein: mappedFood.protein,
        fat: mappedFood.fat,
        glycemicIndex: mappedFood.glycemicIndex,
        servingSize: mappedFood.servingSize,
        servingSizeUnit: mappedFood.servingSizeUnit,
        barcode: mappedFood.barcode,
        imageUrl: mappedFood.imageUrl,
        isVerified: true,
        isCustom: false,
        foodCategoryId
      },
      include: {
        foodCategory: true
      }
    });
    
    res.status(201).json({
      success: true,
      message: 'Food imported successfully from Open Food Facts',
      data: savedFood
    });
  } catch (error: any) {
    console.error('Error importing food:', error);
    res.status(500).json({
      success: false,
      message: 'Error importing food',
      error: error.message
    });
  }
};
