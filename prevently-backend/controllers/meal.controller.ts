import { Request, Response } from 'express';
import prisma from '../services/prisma.service';
import { Prisma } from '@prisma/client';

/**
 * Get all meals for a user
 */
export const getUserMeals = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { date, startDate, endDate, limit = 20, page = 1 } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    // Build filter conditions
    const where: Prisma.MealWhereInput = {
      userId
    };
    
    // Filter by specific date
    if (date) {
      const targetDate = new Date(date as string);
      const nextDay = new Date(targetDate);
      nextDay.setDate(nextDay.getDate() + 1);
      
      where.date = {
        gte: targetDate,
        lt: nextDay
      };
    } 
    // Filter by date range
    else if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string)
      };
    }
    
    // Get total count for pagination
    const totalCount = await prisma.meal.count({ where });
    
    // Get meals with pagination
    const meals = await prisma.meal.findMany({
      where,
      include: {
        mealItems: {
          include: {
            food: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      skip,
      take: Number(limit)
    });
    
    res.status(200).json({
      success: true,
      count: meals.length,
      totalCount,
      totalPages: Math.ceil(totalCount / Number(limit)),
      currentPage: Number(page),
      data: meals
    });
  } catch (error: any) {
    console.error('Error getting meals:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving meals',
      error: error.message
    });
  }
};

/**
 * Get meal by ID
 */
export const getMealById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const meal = await prisma.meal.findUnique({
      where: { id },
      include: {
        mealItems: {
          include: {
            food: true
          }
        }
      }
    });
    
    if (!meal) {
      res.status(404).json({
        success: false,
        message: 'Meal not found'
      });
      return;
    }
    
    res.status(200).json({
      success: true,
      data: meal
    });
  } catch (error: any) {
    console.error('Error getting meal:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving meal',
      error: error.message
    });
  }
};

/**
 * Create new meal
 */
export const createMeal = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      name, 
      date, 
      time, 
      userId, 
      mealItems, 
      notes 
    } = req.body;
    
    // Validate required fields
    if (!name || !date || !time || !userId || !mealItems || !Array.isArray(mealItems)) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: name, date, time, userId, mealItems (array)'
      });
      return;
    }
    
    // Create meal with items in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the meal first
      const newMeal = await tx.meal.create({
        data: {
          name,
          date: new Date(date),
          time: new Date(time),
          userId,
          notes
        }
      });
      
      let totalCalories = 0;
      let totalCarbs = 0;
      let totalProtein = 0;
      let totalFat = 0;
      let giValues = [];
      let totalGL = 0;
      
      // Create meal items and calculate totals
      for (const item of mealItems) {
        const { foodId, quantity, servingSize } = item;
        
        if (!foodId || !quantity) {
          throw new Error('Each meal item must have foodId and quantity');
        }
        
        // Get food details
        const food = await tx.food.findUnique({
          where: { id: foodId }
        });
        
        if (!food) {
          throw new Error(`Food with ID ${foodId} not found`);
        }
        
        // Calculate nutrition for this item based on quantity
        const itemCalories = Math.round(food.calories * quantity);
        const itemCarbs = parseFloat((food.carbs * quantity).toFixed(1));
        const itemProtein = parseFloat((food.protein * quantity).toFixed(1));
        const itemFat = parseFloat((food.fat * quantity).toFixed(1));
        
        // Calculate glycemic load if glycemic index is available
        let itemGL = null;
        if (food.glycemicIndex) {
          itemGL = parseFloat(((food.glycemicIndex * itemCarbs) / 100).toFixed(1));
          giValues.push(food.glycemicIndex);
          totalGL += itemGL;
        }
        
        // Add to totals
        totalCalories += itemCalories;
        totalCarbs += itemCarbs;
        totalProtein += itemProtein;
        totalFat += itemFat;
        
        // Create meal item
        await tx.mealItem.create({
          data: {
            mealId: newMeal.id,
            foodId,
            quantity,
            servingSize: servingSize || 1,
            calories: itemCalories,
            carbs: itemCarbs,
            protein: itemProtein,
            fat: itemFat,
            glycemicLoad: itemGL
          }
        });
      }
      
      // Calculate average GI
      const averageGI = giValues.length > 0 
        ? parseFloat((giValues.reduce((sum, gi) => sum + gi, 0) / giValues.length).toFixed(1))
        : null;
      
      // Update meal with calculated totals
      const updatedMeal = await tx.meal.update({
        where: { id: newMeal.id },
        data: {
          totalCalories,
          totalCarbs,
          totalProtein,
          totalFat,
          averageGI,
          totalGL: totalGL > 0 ? totalGL : null
        },
        include: {
          mealItems: {
            include: {
              food: true
            }
          }
        }
      });
      
      return updatedMeal;
    });
    
    res.status(201).json({
      success: true,
      message: 'Meal created successfully',
      data: result
    });
  } catch (error: any) {
    console.error('Error creating meal:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating meal',
      error: error.message
    });
  }
};

/**
 * Update meal
 */
export const updateMeal = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { 
      name, 
      date, 
      time, 
      mealItems, 
      notes 
    } = req.body;
    
    // Check if meal exists
    const existingMeal = await prisma.meal.findUnique({
      where: { id },
      include: {
        mealItems: true
      }
    });
    
    if (!existingMeal) {
      res.status(404).json({
        success: false,
        message: 'Meal not found'
      });
      return;
    }
    
    // Update meal with items in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update basic meal info
      let updateData: Prisma.MealUpdateInput = {};
      
      if (name) updateData.name = name;
      if (date) updateData.date = new Date(date);
      if (time) updateData.time = new Date(time);
      if (notes !== undefined) updateData.notes = notes;
      
      // If meal items are provided, update them
      if (mealItems && Array.isArray(mealItems)) {
        // Delete existing meal items
        await tx.mealItem.deleteMany({
          where: { mealId: id }
        });
        
        let totalCalories = 0;
        let totalCarbs = 0;
        let totalProtein = 0;
        let totalFat = 0;
        let giValues = [];
        let totalGL = 0;
        
        // Create new meal items and calculate totals
        for (const item of mealItems) {
          const { foodId, quantity, servingSize } = item;
          
          if (!foodId || !quantity) {
            throw new Error('Each meal item must have foodId and quantity');
          }
          
          // Get food details
          const food = await tx.food.findUnique({
            where: { id: foodId }
          });
          
          if (!food) {
            throw new Error(`Food with ID ${foodId} not found`);
          }
          
          // Calculate nutrition for this item based on quantity
          const itemCalories = Math.round(food.calories * quantity);
          const itemCarbs = parseFloat((food.carbs * quantity).toFixed(1));
          const itemProtein = parseFloat((food.protein * quantity).toFixed(1));
          const itemFat = parseFloat((food.fat * quantity).toFixed(1));
          
          // Calculate glycemic load if glycemic index is available
          let itemGL = null;
          if (food.glycemicIndex) {
            itemGL = parseFloat(((food.glycemicIndex * itemCarbs) / 100).toFixed(1));
            giValues.push(food.glycemicIndex);
            totalGL += itemGL;
          }
          
          // Add to totals
          totalCalories += itemCalories;
          totalCarbs += itemCarbs;
          totalProtein += itemProtein;
          totalFat += itemFat;
          
          // Create meal item
          await tx.mealItem.create({
            data: {
              mealId: id,
              foodId,
              quantity,
              servingSize: servingSize || 1,
              calories: itemCalories,
              carbs: itemCarbs,
              protein: itemProtein,
              fat: itemFat,
              glycemicLoad: itemGL
            }
          });
        }
        
        // Calculate average GI
        const averageGI = giValues.length > 0 
          ? parseFloat((giValues.reduce((sum, gi) => sum + gi, 0) / giValues.length).toFixed(1))
          : null;
        
        // Update meal totals
        updateData.totalCalories = totalCalories;
        updateData.totalCarbs = totalCarbs;
        updateData.totalProtein = totalProtein;
        updateData.totalFat = totalFat;
        updateData.averageGI = averageGI;
        updateData.totalGL = totalGL > 0 ? totalGL : null;
      }
      
      // Update the meal
      const updatedMeal = await tx.meal.update({
        where: { id },
        data: updateData,
        include: {
          mealItems: {
            include: {
              food: true
            }
          }
        }
      });
      
      return updatedMeal;
    });
    
    res.status(200).json({
      success: true,
      message: 'Meal updated successfully',
      data: result
    });
  } catch (error: any) {
    console.error('Error updating meal:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating meal',
      error: error.message
    });
  }
};

/**
 * Delete meal
 */
export const deleteMeal = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    // Check if meal exists
    const existingMeal = await prisma.meal.findUnique({
      where: { id }
    });
    
    if (!existingMeal) {
      res.status(404).json({
        success: false,
        message: 'Meal not found'
      });
      return;
    }
    
    // Delete meal and its items in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete meal items first (cascade delete should handle this, but being explicit)
      await tx.mealItem.deleteMany({
        where: { mealId: id }
      });
      
      // Delete the meal
      await tx.meal.delete({
        where: { id }
      });
    });
    
    res.status(200).json({
      success: true,
      message: 'Meal deleted successfully'
    });
  } catch (error: any) {
    console.error('Error deleting meal:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting meal',
      error: error.message
    });
  }
};
