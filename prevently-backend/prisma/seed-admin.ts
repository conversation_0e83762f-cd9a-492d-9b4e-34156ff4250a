import { PrismaClient, RiskGroup } from '@prisma/client';

const prisma = new PrismaClient();

async function seedAdminData() {
  console.log('🌱 Seeding admin dashboard data...');

  try {
    // Create admin settings
    const adminSettings = await prisma.adminSettings.upsert({
      where: { id: '00000000-0000-0000-0000-000000000001' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000001',
        appName: 'Preventely',
        clinicName: 'HealthCare Plus Clinic',
        clinicAddress: '123 Medical Center Drive, Health City, HC 12345',
        clinicPhone: '+****************',
        iosAppUrl: 'https://apps.apple.com/app/preventely',
        androidAppUrl: 'https://play.google.com/store/apps/details?id=com.preventely',
        webAppUrl: 'https://app.preventely.com',
        smtpHost: 'smtp.gmail.com',
        smtpPort: '587',
        smtpUser: '<EMAIL>',
        fromEmail: '<EMAIL>',
        fromName: 'Preventely Health Team',
        emailNotifications: true,
        assessmentAlerts: true,
        highRiskAlerts: true,
        dailyReports: false,
        weeklyReports: true,
        monthlyReports: true,
      },
    });

    // Create sample patients
    const patients = [
      {
        id: '********-1111-1111-1111-********1111',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1980-05-15'),
        gender: 'Male',
        address: '456 Oak Street, Wellness Town, WT 67890',
      },
      {
        id: '********-2222-2222-2222-************',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1975-08-22'),
        gender: 'Female',
        address: '789 Pine Avenue, Health Village, HV 54321',
      },
      {
        id: '********-3333-3333-3333-********3333',
        firstName: 'Mike',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1990-12-03'),
        gender: 'Male',
        address: '321 Maple Road, Fitness City, FC 98765',
      },
      {
        id: '********-4444-4444-4444-********4444',
        firstName: 'Sarah',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1965-03-18'),
        gender: 'Female',
        address: '654 Elm Street, Care Town, CT 13579',
      },
      {
        id: '********-5555-5555-5555-************',
        firstName: 'David',
        lastName: 'Brown',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1985-07-09'),
        gender: 'Male',
        address: '987 Cedar Lane, Wellness Park, WP 24680',
      },
      {
        id: '66666666-6666-6666-6666-666666666666',
        firstName: 'Lisa',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1992-11-27'),
        gender: 'Female',
        address: '147 Birch Boulevard, Health Heights, HH 97531',
      },
      {
        id: '77777777-7777-7777-7777-777777777777',
        firstName: 'Robert',
        lastName: 'Davis',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1978-01-14'),
        gender: 'Male',
        address: '258 Spruce Street, Medical Mile, MM 86420',
      },
      {
        id: '88888888-8888-8888-8888-888888888888',
        firstName: 'Emma',
        lastName: 'Martinez',
        email: '<EMAIL>',
        phone: '+****************',
        dateOfBirth: new Date('1988-09-06'),
        gender: 'Female',
        address: '369 Willow Way, Care Center, CC 75319',
      },
    ];

    for (const patient of patients) {
      await prisma.patient.upsert({
        where: { id: patient.id },
        update: {},
        create: patient,
      });
    }

    // Create sample assessments with varying risk scores
    const assessments = [
      {
        patientId: '********-1111-1111-1111-********1111',
        age: '45-54',
        bmi: '30+',
        waistCircumference: 'male-102+',
        physicalActivity: 'no',
        vegetableConsumption: 'no',
        bloodPressureMedication: 'yes',
        highGlucoseHistory: 'yes',
        familyDiabetesHistory: 'yes-parent-sibling-child',
        riskScore: 18,
        riskGroup: RiskGroup.D,
        notes: 'High risk patient requiring immediate intervention',
        assessedBy: 'Dr. Smith',
        assessmentDate: new Date('2024-01-15'),
      },
      {
        patientId: '********-2222-2222-2222-************',
        age: '35-44',
        bmi: '25-30',
        waistCircumference: 'female-80-88',
        physicalActivity: 'yes',
        vegetableConsumption: 'no',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'yes-grandparent-aunt-uncle',
        riskScore: 8,
        riskGroup: RiskGroup.B,
        notes: 'Slightly elevated risk, lifestyle modifications recommended',
        assessedBy: 'Dr. Johnson',
        assessmentDate: new Date('2024-01-14'),
      },
      {
        patientId: '********-3333-3333-3333-********3333',
        age: 'under-45',
        bmi: 'under-25',
        waistCircumference: 'male-under-94',
        physicalActivity: 'yes',
        vegetableConsumption: 'yes',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'no',
        riskScore: 4,
        riskGroup: RiskGroup.A,
        notes: 'Low risk, maintain healthy lifestyle',
        assessedBy: 'Dr. Wilson',
        assessmentDate: new Date('2024-01-13'),
      },
      {
        patientId: '********-4444-4444-4444-********4444',
        age: '55-64',
        bmi: '30+',
        waistCircumference: 'female-88+',
        physicalActivity: 'no',
        vegetableConsumption: 'no',
        bloodPressureMedication: 'yes',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'yes-parent-sibling-child',
        riskScore: 16,
        riskGroup: RiskGroup.D,
        notes: 'High risk, comprehensive intervention needed',
        assessedBy: 'Dr. Brown',
        assessmentDate: new Date('2024-01-12'),
      },
      {
        patientId: '********-5555-5555-5555-************',
        age: '45-54',
        bmi: '25-30',
        waistCircumference: 'male-94-102',
        physicalActivity: 'yes',
        vegetableConsumption: 'yes',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'yes-grandparent-aunt-uncle',
        riskScore: 11,
        riskGroup: RiskGroup.B,
        notes: 'Slightly elevated risk, monitor closely',
        assessedBy: 'Dr. Davis',
        assessmentDate: new Date('2024-01-11'),
      },
      {
        patientId: '66666666-6666-6666-6666-666666666666',
        age: 'under-45',
        bmi: 'under-25',
        waistCircumference: 'female-under-80',
        physicalActivity: 'yes',
        vegetableConsumption: 'yes',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'no',
        riskScore: 2,
        riskGroup: RiskGroup.A,
        notes: 'Excellent health profile, continue current lifestyle',
        assessedBy: 'Dr. Martinez',
        assessmentDate: new Date('2024-01-10'),
      },
      {
        patientId: '77777777-7777-7777-7777-777777777777',
        age: '45-54',
        bmi: '25-30',
        waistCircumference: 'male-94-102',
        physicalActivity: 'no',
        vegetableConsumption: 'no',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'yes-parent-sibling-child',
        riskScore: 13,
        riskGroup: RiskGroup.C,
        notes: 'Moderate risk, lifestyle counseling recommended',
        assessedBy: 'Dr. Taylor',
        assessmentDate: new Date('2024-01-09'),
      },
      {
        patientId: '88888888-8888-8888-8888-888888888888',
        age: '35-44',
        bmi: '25-30',
        waistCircumference: 'female-80-88',
        physicalActivity: 'yes',
        vegetableConsumption: 'no',
        bloodPressureMedication: 'no',
        highGlucoseHistory: 'no',
        familyDiabetesHistory: 'yes-grandparent-aunt-uncle',
        riskScore: 9,
        riskGroup: RiskGroup.B,
        notes: 'Slightly elevated risk, dietary improvements needed',
        assessedBy: 'Dr. Anderson',
        assessmentDate: new Date('2024-01-08'),
      },
    ];

    for (const assessment of assessments) {
      await prisma.assessment.create({
        data: assessment,
      });
    }

    // Create sample patient invitations
    const invitations = [
      {
        patientId: '********-1111-1111-1111-********1111',
        email: '<EMAIL>',
        riskGroup: RiskGroup.D,
        riskScore: 18,
        invitationSent: true,
        sentAt: new Date('2024-01-15T10:30:00Z'),
        accountCreated: false,
      },
      {
        patientId: '********-2222-2222-2222-************',
        email: '<EMAIL>',
        riskGroup: RiskGroup.B,
        riskScore: 8,
        invitationSent: true,
        sentAt: new Date('2024-01-14T14:15:00Z'),
        accountCreated: true,
        accountCreatedAt: new Date('2024-01-14T16:45:00Z'),
      },
      {
        patientId: '********-3333-3333-3333-********3333',
        email: '<EMAIL>',
        riskGroup: RiskGroup.A,
        riskScore: 4,
        invitationSent: true,
        sentAt: new Date('2024-01-13T09:20:00Z'),
        accountCreated: true,
        accountCreatedAt: new Date('2024-01-13T11:30:00Z'),
      },
      {
        patientId: '********-4444-4444-4444-********4444',
        email: '<EMAIL>',
        riskGroup: RiskGroup.D,
        riskScore: 16,
        invitationSent: false,
      },
      {
        patientId: '********-5555-5555-5555-************',
        email: '<EMAIL>',
        riskGroup: RiskGroup.B,
        riskScore: 11,
        invitationSent: true,
        sentAt: new Date('2024-01-11T13:45:00Z'),
        accountCreated: false,
      },
    ];

    for (const invitation of invitations) {
      await prisma.patientInvitation.create({
        data: invitation,
      });
    }

    console.log('✅ Admin dashboard data seeded successfully!');
    console.log(`📊 Created:`);
    console.log(`   - 1 Admin Settings record`);
    console.log(`   - ${patients.length} Patients`);
    console.log(`   - ${assessments.length} Assessments`);
    console.log(`   - ${invitations.length} Patient Invitations`);
    console.log(`📈 Risk Group Distribution:`);
    console.log(`   - Group A (Low Risk): 2 patients`);
    console.log(`   - Group B (Slightly Elevated): 3 patients`);
    console.log(`   - Group C (Moderate Risk): 1 patient`);
    console.log(`   - Group D (High Risk): 2 patients`);

  } catch (error) {
    console.error('❌ Error seeding admin data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedAdminData()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
