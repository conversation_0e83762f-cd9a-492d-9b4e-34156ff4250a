// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid()) @db.Uuid
  email     String   @unique
  password  String
  name      String?
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  profile   Profile?
  posts     Post[]
  meals     Meal[]
  dailyNutritionLogs DailyNutritionLog[]
  aiConversation AIConversation?
  reminders Reminder[]
}

model Profile {
  id                String   @id @default(uuid()) @db.Uuid
  bio               String?
  avatar            String?
  age               Int?
  gender            String?
  condition         String?
  weight            Float?
  height            Float?
  bmi               Float?
  glucoseTargetMin  Int?    @default(70)
  glucoseTargetMax  Int?    @default(140)
  userId            String   @unique @db.Uuid
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  medications       Medication[]
  allergies         Allergy[]
  healthcareProvider HealthcareProvider?
  preferences       UserPreference?
  connectedDevices  ConnectedDevice[]
  glucoseReadings   GlucoseReading[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model Post {
  id        String    @id @default(uuid()) @db.Uuid
  title     String
  content   String
  published Boolean   @default(false)
  authorId  String    @db.Uuid
  author    User      @relation(fields: [authorId], references: [id])
  tags      Tag[]     @relation("PostToTag")
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Tag {
  id        String   @id @default(uuid()) @db.Uuid
  name      String   @unique
  posts     Post[]   @relation("PostToTag")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Food {
  id          String    @id @default(uuid()) @db.Uuid
  name        String
  description String?
  calories    Int
  carbs       Float
  protein     Float
  fat         Float
  glycemicIndex Int?    // GI value
  glycemicLoad Float?   // GL value
  servingSize String
  servingSizeUnit String
  barcode     String?   @unique
  imageUrl    String?
  isVerified  Boolean   @default(false)
  isCustom    Boolean   @default(false)
  createdBy   String?   @db.Uuid
  mealItems   MealItem[]
  foodCategory FoodCategory? @relation(fields: [foodCategoryId], references: [id])
  foodCategoryId String? @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model FoodCategory {
  id          String    @id @default(uuid()) @db.Uuid
  name        String    @unique
  description String?
  foods       Food[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Meal {
  id          String    @id @default(uuid()) @db.Uuid
  name        String    // Breakfast, Lunch, Dinner, Snack
  date        DateTime
  time        DateTime
  userId      String    @db.Uuid
  user        User      @relation(fields: [userId], references: [id])
  mealItems   MealItem[]
  totalCalories Int?
  totalCarbs  Float?
  totalProtein Float?
  totalFat    Float?
  averageGI   Float?
  totalGL     Float?
  notes       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model MealItem {
  id          String    @id @default(uuid()) @db.Uuid
  mealId      String    @db.Uuid
  meal        Meal      @relation(fields: [mealId], references: [id], onDelete: Cascade)
  foodId      String    @db.Uuid
  food        Food      @relation(fields: [foodId], references: [id])
  quantity    Float
  servingSize Float
  calories    Int
  carbs       Float
  protein     Float
  fat         Float
  glycemicLoad Float?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model DailyNutritionLog {
  id          String    @id @default(uuid()) @db.Uuid
  date        DateTime
  userId      String    @db.Uuid
  user        User      @relation(fields: [userId], references: [id])

  @@unique([date, userId], name: "date_userId")
  totalCalories Int
  totalCarbs  Float
  totalProtein Float
  totalFat    Float
  caloriesGoal Int?
  carbsGoal   Float?
  proteinGoal Float?
  fatGoal     Float?
  totalGlycemicLoad Float?
  glycemicLoadStatus GlycemicLoadStatus? @default(GOOD)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model FoodSuggestion {
  id          String    @id @default(uuid()) @db.Uuid
  category    String    // e.g., "Low GI Breakfast", "Balanced Snacks"
  foodId      String    @db.Uuid
  benefits    String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

enum GlycemicLoadStatus {
  GOOD
  MODERATE
  HIGH
}

enum Role {
  USER
  ADMIN
}

model Medication {
  id          String   @id @default(uuid()) @db.Uuid
  name        String
  dosage      String?
  frequency   String?
  notes       String?
  profileId   String   @db.Uuid
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Allergy {
  id          String   @id @default(uuid()) @db.Uuid
  name        String
  severity    String?
  notes       String?
  profileId   String   @db.Uuid
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model HealthcareProvider {
  id          String   @id @default(uuid()) @db.Uuid
  name        String
  specialty   String?
  phone       String?
  email       String?
  address     String?
  notes       String?
  profileId   String   @unique @db.Uuid
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model UserPreference {
  id              String   @id @default(uuid()) @db.Uuid
  notifications   Boolean  @default(true)
  language        String   @default("EN")
  darkMode        Boolean  @default(false)
  units           String   @default("metric")
  profileId       String   @unique @db.Uuid
  profile         Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model ConnectedDevice {
  id           String    @id @default(uuid()) @db.Uuid
  name         String
  deviceType   String?
  connected    Boolean   @default(false)
  lastSyncDate DateTime?
  deviceId     String?
  profileId    String    @db.Uuid
  profile      Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model Reminder {
  id          String   @id @default(uuid()) @db.Uuid
  title       String
  time        String   // Time of day for the reminder (e.g., "08:00")
  date        DateTime // Date for the reminder
  type        String   // medication, measurement, activity, appointment
  completed   Boolean  @default(false)
  userId      String   @db.Uuid
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GlucoseReading {
  id          String    @id @default(uuid()) @db.Uuid
  value       Float
  timestamp   DateTime
  trend       String?
  trendRate   Float?
  source      String?   @default("Dexcom")
  notes       String?
  profileId   String    @db.Uuid
  profile     Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model AIConversation {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @unique @db.Uuid
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages  AIConversationMessage[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AIConversationMessage {
  id            String   @id @default(uuid()) @db.Uuid
  conversationId String   @db.Uuid
  conversation  AIConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  content       String
  role          String   // 'user' or 'assistant'
  timestamp     DateTime @default(now())
}

// Admin Dashboard Models
model Patient {
  id                String   @id @default(uuid()) @db.Uuid
  firstName         String
  lastName          String
  email             String   @unique
  phone             String?
  dateOfBirth       DateTime?
  gender            String?
  address           String?
  emergencyContact  String?
  emergencyPhone    String?
  assessments       Assessment[]
  invitations       PatientInvitation[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model Assessment {
  id                    String   @id @default(uuid()) @db.Uuid
  patientId             String   @db.Uuid
  patient               Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  // Finnish Diabetes Risk Score Questions
  age                   String
  bmi                   String
  waistCircumference    String
  physicalActivity      String
  vegetableConsumption  String
  bloodPressureMedication String
  highGlucoseHistory    String
  familyDiabetesHistory String

  // Calculated Results
  riskScore             Int
  riskGroup             RiskGroup

  // Additional Information
  notes                 String?
  assessedBy            String?   // Healthcare professional name
  assessmentDate        DateTime  @default(now())

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}

model PatientInvitation {
  id                String   @id @default(uuid()) @db.Uuid
  patientId         String   @db.Uuid
  patient           Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  email             String
  riskGroup         RiskGroup
  riskScore         Int
  invitationSent    Boolean  @default(false)
  sentAt            DateTime?
  accountCreated    Boolean  @default(false)
  accountCreatedAt  DateTime?

  // Email tracking
  emailOpened       Boolean  @default(false)
  emailOpenedAt     DateTime?
  appDownloaded     Boolean  @default(false)
  appDownloadedAt   DateTime?

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([patientId, email])
}

model AdminSettings {
  id                String   @id @default(uuid()) @db.Uuid

  // Email Settings
  smtpHost          String?
  smtpPort          String?
  smtpUser          String?
  smtpPassword      String?
  fromEmail         String?
  fromName          String?

  // App Settings
  appName           String?  @default("Preventely")
  clinicName        String?
  clinicAddress     String?
  clinicPhone       String?
  iosAppUrl         String?
  androidAppUrl     String?
  webAppUrl         String?

  // Notification Settings
  emailNotifications    Boolean @default(true)
  assessmentAlerts      Boolean @default(true)
  highRiskAlerts        Boolean @default(true)
  dailyReports          Boolean @default(false)
  weeklyReports         Boolean @default(true)
  monthlyReports        Boolean @default(true)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

enum RiskGroup {
  A  // Low Risk (0-6 points)
  B  // Slightly Elevated Risk (7-11 points)
  C  // Moderate Risk (12-14 points)
  D  // High Risk (15+ points)
}
