/*
  Warnings:

  - A unique constraint covering the columns `[date,userId]` on the table `DailyNutritionLog` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "RiskGroup" AS ENUM ('A', 'B', 'C', 'D');

-- DropIndex
DROP INDEX "DailyNutritionLog_date_key";

-- CreateTable
CREATE TABLE "Reminder" (
    "id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "time" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "type" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "userId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Reminder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Patient" (
    "id" UUID NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "gender" TEXT,
    "address" TEXT,
    "emergencyContact" TEXT,
    "emergencyPhone" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Patient_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Assessment" (
    "id" UUID NOT NULL,
    "patientId" UUID NOT NULL,
    "age" TEXT NOT NULL,
    "bmi" TEXT NOT NULL,
    "waistCircumference" TEXT NOT NULL,
    "physicalActivity" TEXT NOT NULL,
    "vegetableConsumption" TEXT NOT NULL,
    "bloodPressureMedication" TEXT NOT NULL,
    "highGlucoseHistory" TEXT NOT NULL,
    "familyDiabetesHistory" TEXT NOT NULL,
    "riskScore" INTEGER NOT NULL,
    "riskGroup" "RiskGroup" NOT NULL,
    "notes" TEXT,
    "assessedBy" TEXT,
    "assessmentDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Assessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PatientInvitation" (
    "id" UUID NOT NULL,
    "patientId" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "riskGroup" "RiskGroup" NOT NULL,
    "riskScore" INTEGER NOT NULL,
    "invitationSent" BOOLEAN NOT NULL DEFAULT false,
    "sentAt" TIMESTAMP(3),
    "accountCreated" BOOLEAN NOT NULL DEFAULT false,
    "accountCreatedAt" TIMESTAMP(3),
    "emailOpened" BOOLEAN NOT NULL DEFAULT false,
    "emailOpenedAt" TIMESTAMP(3),
    "appDownloaded" BOOLEAN NOT NULL DEFAULT false,
    "appDownloadedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PatientInvitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminSettings" (
    "id" UUID NOT NULL,
    "smtpHost" TEXT,
    "smtpPort" TEXT,
    "smtpUser" TEXT,
    "smtpPassword" TEXT,
    "fromEmail" TEXT,
    "fromName" TEXT,
    "appName" TEXT DEFAULT 'Preventely',
    "clinicName" TEXT,
    "clinicAddress" TEXT,
    "clinicPhone" TEXT,
    "iosAppUrl" TEXT,
    "androidAppUrl" TEXT,
    "webAppUrl" TEXT,
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "assessmentAlerts" BOOLEAN NOT NULL DEFAULT true,
    "highRiskAlerts" BOOLEAN NOT NULL DEFAULT true,
    "dailyReports" BOOLEAN NOT NULL DEFAULT false,
    "weeklyReports" BOOLEAN NOT NULL DEFAULT true,
    "monthlyReports" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AdminSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Patient_email_key" ON "Patient"("email");

-- CreateIndex
CREATE UNIQUE INDEX "PatientInvitation_patientId_email_key" ON "PatientInvitation"("patientId", "email");

-- CreateIndex
CREATE UNIQUE INDEX "DailyNutritionLog_date_userId_key" ON "DailyNutritionLog"("date", "userId");

-- AddForeignKey
ALTER TABLE "Reminder" ADD CONSTRAINT "Reminder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Assessment" ADD CONSTRAINT "Assessment_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PatientInvitation" ADD CONSTRAINT "PatientInvitation_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
