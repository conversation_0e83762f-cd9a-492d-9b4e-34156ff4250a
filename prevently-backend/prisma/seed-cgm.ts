import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedCGMData() {
  console.log('🩺 Seeding CGM data...');

  try {
    // Create sample users and profiles for patients who have CGM data
    const cgmPatients = [
      {
        email: '<EMAIL>',
        name: '<PERSON>',
        password: '$2b$10$hashedpassword', // This would be properly hashed in real app
        targetMin: 70,
        targetMax: 180,
      },
      {
        email: '<EMAIL>',
        name: '<PERSON>',
        password: '$2b$10$hashedpassword',
        targetMin: 80,
        targetMax: 160,
      },
      {
        email: '<EMAIL>',
        name: '<PERSON>',
        password: '$2b$10$hashedpassword',
        targetMin: 70,
        targetMax: 180,
      },
    ];

    for (const patientData of cgmPatients) {
      // Create or update user
      const user = await prisma.user.upsert({
        where: { email: patientData.email },
        update: {},
        create: {
          email: patientData.email,
          name: patientData.name,
          password: patientData.password,
        },
      });

      // Create or update profile
      const profile = await prisma.profile.upsert({
        where: { userId: user.id },
        update: {
          glucoseTargetMin: patientData.targetMin,
          glucoseTargetMax: patientData.targetMax,
        },
        create: {
          userId: user.id,
          glucoseTargetMin: patientData.targetMin,
          glucoseTargetMax: patientData.targetMax,
          age: 45,
          gender: 'Male',
          weight: 75.0,
          height: 175.0,
          bmi: 24.5,
        },
      });

      // Generate realistic CGM data for the last 7 days
      const readings = [];
      const now = new Date();
      
      for (let day = 6; day >= 0; day--) {
        const baseDate = new Date(now.getTime() - day * 24 * 60 * 60 * 1000);
        
        // Generate readings every 15 minutes (96 readings per day)
        for (let hour = 0; hour < 24; hour++) {
          for (let minute = 0; minute < 60; minute += 15) {
            const timestamp = new Date(baseDate);
            timestamp.setHours(hour, minute, 0, 0);

            // Generate realistic glucose values based on time of day and patient
            let baseGlucose = 100;
            
            // Meal spikes (breakfast, lunch, dinner)
            if ((hour >= 7 && hour <= 9) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) {
              baseGlucose += Math.random() * 60 + 20; // 120-180 mg/dL during meals
            } else if (hour >= 2 && hour <= 6) {
              baseGlucose -= Math.random() * 20; // Lower during early morning
            }

            // Add some patient-specific variation
            if (patientData.email === '<EMAIL>') {
              // John has higher glucose (high risk patient)
              baseGlucose += 30 + Math.random() * 40;
            } else if (patientData.email === '<EMAIL>') {
              // Sarah has very high glucose (high risk patient)
              baseGlucose += 50 + Math.random() * 60;
            } else {
              // Jane has more controlled glucose
              baseGlucose += Math.random() * 20 - 10;
            }

            // Add random variation
            const glucose = Math.max(60, Math.min(400, baseGlucose + (Math.random() - 0.5) * 20));

            // Determine trend
            let trend = 'stable';
            let trendRate = 0;
            
            if (Math.random() < 0.3) {
              if (Math.random() < 0.5) {
                trend = 'rising';
                trendRate = Math.random() * 3 + 1;
              } else {
                trend = 'falling';
                trendRate = -(Math.random() * 3 + 1);
              }
            }

            readings.push({
              profileId: profile.id,
              value: Math.round(glucose * 10) / 10,
              timestamp,
              trend,
              trendRate,
              source: 'Dexcom G6',
            });
          }
        }
      }

      // Insert all readings for this patient
      await prisma.glucoseReading.createMany({
        data: readings,
        skipDuplicates: true,
      });

      console.log(`✅ Created ${readings.length} CGM readings for ${patientData.name}`);
    }

    // Get total counts
    const totalUsers = await prisma.user.count();
    const totalProfiles = await prisma.profile.count();
    const totalReadings = await prisma.glucoseReading.count();

    console.log('✅ CGM data seeded successfully!');
    console.log(`📊 Created:`);
    console.log(`   - ${cgmPatients.length} Users with CGM data`);
    console.log(`   - ${cgmPatients.length} Profiles with glucose targets`);
    console.log(`   - ${totalReadings} Glucose readings`);
    console.log(`📈 CGM Data Summary:`);
    console.log(`   - 7 days of data per patient`);
    console.log(`   - 96 readings per day (every 15 minutes)`);
    console.log(`   - Realistic meal spikes and daily patterns`);
    console.log(`   - Patient-specific glucose patterns based on risk levels`);

  } catch (error) {
    console.error('❌ Error seeding CGM data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedCGMData()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
