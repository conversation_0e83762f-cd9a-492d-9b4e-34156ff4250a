{"openapi": "3.0.0", "info": {"title": "Prevently API", "description": "API documentation for Prevently Backend", "version": "1.0.0", "contact": {"name": "Prevently Support"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/": {"get": {"summary": "Welcome endpoint", "description": "Returns a welcome message", "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}}}}}}}, "/health": {"get": {"summary": "Health check endpoint", "description": "Returns the API health status", "responses": {"200": {"description": "API is healthy", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}}}