/**
 * @swagger
 * components:
 *   schemas:
 *     Food:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the food
 *         name:
 *           type: string
 *           description: The name of the food
 *         description:
 *           type: string
 *           description: Description of the food
 *         calories:
 *           type: integer
 *           description: Calories per serving
 *         carbs:
 *           type: number
 *           description: Carbohydrates per serving (g)
 *         protein:
 *           type: number
 *           description: Protein per serving (g)
 *         fat:
 *           type: number
 *           description: Fat per serving (g)
 *         glycemicIndex:
 *           type: integer
 *           description: Glycemic index value (0-100)
 *         glycemicLoad:
 *           type: number
 *           description: Glycemic load value
 *         servingSize:
 *           type: string
 *           description: Serving size value
 *         servingSizeUnit:
 *           type: string
 *           description: Unit of serving size (e.g., g, ml, oz)
 *         barcode:
 *           type: string
 *           description: Barcode for the food item
 *         imageUrl:
 *           type: string
 *           description: URL to an image of the food
 *         isVerified:
 *           type: boolean
 *           description: Whether the food data has been verified
 *         isCustom:
 *           type: boolean
 *           description: Whether the food is a custom user-added food
 *         foodCategory:
 *           $ref: '#/components/schemas/FoodCategory'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the food was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the food was last updated
 *       example:
 *         id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         name: Oatmeal
 *         description: Steel cut oats
 *         calories: 150
 *         carbs: 27
 *         protein: 5
 *         fat: 3
 *         glycemicIndex: 55
 *         glycemicLoad: 14.85
 *         servingSize: 1
 *         servingSizeUnit: cup
 *         barcode: 038000000000
 *         imageUrl: https://example.com/oatmeal.jpg
 *         isVerified: true
 *         isCustom: false
 *         foodCategory:
 *           id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *           name: Grains
 *         createdAt: 2025-05-18T08:00:00.000Z
 *         updatedAt: 2025-05-18T08:00:00.000Z
 *     
 *     FoodCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the category
 *         name:
 *           type: string
 *           description: The name of the category
 *         description:
 *           type: string
 *           description: Description of the category
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the category was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the category was last updated
 *       example:
 *         id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         name: Grains
 *         description: Grain-based foods
 *         createdAt: 2025-05-18T08:00:00.000Z
 *         updatedAt: 2025-05-18T08:00:00.000Z
 *     
 *     Meal:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the meal
 *         name:
 *           type: string
 *           description: The name of the meal (e.g., Breakfast, Lunch)
 *         date:
 *           type: string
 *           format: date
 *           description: The date of the meal
 *         time:
 *           type: string
 *           format: date-time
 *           description: The time of the meal
 *         userId:
 *           type: string
 *           format: uuid
 *           description: The id of the user who created the meal
 *         totalCalories:
 *           type: integer
 *           description: Total calories in the meal
 *         totalCarbs:
 *           type: number
 *           description: Total carbohydrates in the meal (g)
 *         totalProtein:
 *           type: number
 *           description: Total protein in the meal (g)
 *         totalFat:
 *           type: number
 *           description: Total fat in the meal (g)
 *         averageGI:
 *           type: number
 *           description: Average glycemic index of the meal
 *         totalGL:
 *           type: number
 *           description: Total glycemic load of the meal
 *         notes:
 *           type: string
 *           description: Additional notes about the meal
 *         mealItems:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/MealItem'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the meal was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the meal was last updated
 *       example:
 *         id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         name: Breakfast
 *         date: 2025-05-18
 *         time: 2025-05-18T07:30:00.000Z
 *         userId: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         totalCalories: 320
 *         totalCarbs: 45
 *         totalProtein: 15
 *         totalFat: 8
 *         averageGI: 45
 *         totalGL: 16
 *         notes: Morning breakfast
 *         mealItems: [
 *           {
 *             id: 3fa85f64-5717-4562-b3fc-2c963f66afa6,
 *             foodId: 3fa85f64-5717-4562-b3fc-2c963f66afa6,
 *             quantity: 1,
 *             calories: 150,
 *             carbs: 27,
 *             protein: 5,
 *             fat: 3,
 *             glycemicLoad: 14.85,
 *             food: {
 *               id: 3fa85f64-5717-4562-b3fc-2c963f66afa6,
 *               name: Oatmeal
 *             }
 *           }
 *         ]
 *         createdAt: 2025-05-18T08:00:00.000Z
 *         updatedAt: 2025-05-18T08:00:00.000Z
 *     
 *     MealItem:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the meal item
 *         mealId:
 *           type: string
 *           format: uuid
 *           description: The id of the meal this item belongs to
 *         foodId:
 *           type: string
 *           format: uuid
 *           description: The id of the food
 *         quantity:
 *           type: number
 *           description: The quantity of the food
 *         servingSize:
 *           type: number
 *           description: The serving size
 *         calories:
 *           type: integer
 *           description: Calories for this item
 *         carbs:
 *           type: number
 *           description: Carbohydrates for this item (g)
 *         protein:
 *           type: number
 *           description: Protein for this item (g)
 *         fat:
 *           type: number
 *           description: Fat for this item (g)
 *         glycemicLoad:
 *           type: number
 *           description: Glycemic load for this item
 *         food:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *               format: uuid
 *             name:
 *               type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the meal item was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the meal item was last updated
 *       example:
 *         id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         mealId: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         foodId: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         quantity: 1
 *         servingSize: 1
 *         calories: 150
 *         carbs: 27
 *         protein: 5
 *         fat: 3
 *         glycemicLoad: 14.85
 *         food:
 *           id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *           name: Oatmeal
 *         createdAt: 2025-05-18T08:00:00.000Z
 *         updatedAt: 2025-05-18T08:00:00.000Z
 *     
 *     DailyNutritionLog:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated id of the log
 *         date:
 *           type: string
 *           format: date
 *           description: The date of the log
 *         userId:
 *           type: string
 *           format: uuid
 *           description: The id of the user
 *         totalCalories:
 *           type: integer
 *           description: Total calories consumed
 *         totalCarbs:
 *           type: number
 *           description: Total carbohydrates consumed (g)
 *         totalProtein:
 *           type: number
 *           description: Total protein consumed (g)
 *         totalFat:
 *           type: number
 *           description: Total fat consumed (g)
 *         caloriesGoal:
 *           type: integer
 *           description: Daily calorie goal
 *         carbsGoal:
 *           type: number
 *           description: Daily carbohydrate goal (g)
 *         proteinGoal:
 *           type: number
 *           description: Daily protein goal (g)
 *         fatGoal:
 *           type: number
 *           description: Daily fat goal (g)
 *         totalGlycemicLoad:
 *           type: number
 *           description: Total glycemic load for the day
 *         glycemicLoadStatus:
 *           type: string
 *           enum: [GOOD, MODERATE, HIGH]
 *           description: Status of the glycemic load
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the log was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the log was last updated
 *       example:
 *         id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         date: 2025-05-18
 *         userId: 3fa85f64-5717-4562-b3fc-2c963f66afa6
 *         totalCalories: 1800
 *         totalCarbs: 200
 *         totalProtein: 90
 *         totalFat: 60
 *         caloriesGoal: 2000
 *         carbsGoal: 225
 *         proteinGoal: 100
 *         fatGoal: 65
 *         totalGlycemicLoad: 75
 *         glycemicLoadStatus: MODERATE
 *         createdAt: 2025-05-18T08:00:00.000Z
 *         updatedAt: 2025-05-18T08:00:00.000Z
 */

// This file is for Swagger documentation only
// No actual code is exported
