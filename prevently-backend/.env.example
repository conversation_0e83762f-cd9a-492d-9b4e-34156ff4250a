# Server Configuration
PORT=3000
NODE_ENV=development

# PostgreSQL Connection String
DATABASE_URL="postgresql://postgres:root@localhost:5432/prevently?schema=public"

# JWT Secret
JWT_SECRET=your_jwt_secret_key_here

# OpenAI API Key for AI Assistant
OPENAI_API_KEY=your_openai_api_key_here

# Dexcom API Credentials
DEXCOM_CLIENT_ID=your_dexcom_client_id
DEXCOM_CLIENT_SECRET=your_dexcom_client_secret
DEXCOM_API_URL=https://api.dexcom.com
DEXCOM_SANDBOX_API_URL=https://sandbox-api.dexcom.com

# Note: Make sure PostgreSQL is running on port 5432
