import { PrismaClient, User, Profile, Food, FoodCategory, Meal } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Seed test data for development and testing
 */
async function main() {
  console.log('Starting to seed test data...');

  // Clear existing data
  await clearExistingData();

  // Create test users
  const users = await createTestUsers();

  // Create profiles for users
  const profiles = await createProfiles(users);

  // Create food categories
  const categories = await createFoodCategories();
  
  // Create foods
  const foods = await createFoods(categories);

  // Create meals for users
  await createMeals(users, foods);

  // Create glucose readings
  await createGlucoseReadings(profiles);

  // Create medications, allergies, and healthcare providers
  await createHealthData(profiles);

  // Create user preferences
  await createUserPreferences(profiles);

  // Create connected devices
  await createConnectedDevices(profiles);

  console.log('Seeding completed successfully!');
}

/**
 * Clear existing data from the database
 */
async function clearExistingData() {
  console.log('Clearing existing data...');
  
  // Delete in order to respect foreign key constraints
  await prisma.aIConversationMessage.deleteMany({});
  await prisma.aIConversation.deleteMany({});
  await prisma.glucoseReading.deleteMany({});
  await prisma.connectedDevice.deleteMany({});
  await prisma.userPreference.deleteMany({});
  await prisma.healthcareProvider.deleteMany({});
  await prisma.allergy.deleteMany({});
  await prisma.medication.deleteMany({});
  await prisma.mealItem.deleteMany({});
  await prisma.meal.deleteMany({});
  await prisma.dailyNutritionLog.deleteMany({});
  await prisma.food.deleteMany({});
  await prisma.profile.deleteMany({});
  await prisma.user.deleteMany({});
  
  console.log('Existing data cleared.');
}

/**
 * Create test users
 */
async function createTestUsers() {
  console.log('Creating test users...');
  
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  const users = await Promise.all([
    prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'John Doe',
        email: '<EMAIL>',
        password: hashedPassword
      }
    }),
    prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: hashedPassword
      }
    }),
    prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Bob Johnson',
        email: '<EMAIL>',
        password: hashedPassword
      }
    })
  ]);
  
  console.log(`Created ${users.length} test users.`);
  return users;
}

/**
 * Create profiles for users
 */
async function createProfiles(users: User[]) {
  console.log('Creating profiles for users...');
  
  const profiles = await Promise.all([
    prisma.profile.create({
      data: {
        id: uuidv4(),
        bio: 'Health enthusiast trying to manage prediabetes.',
        age: 42,
        gender: 'Male',
        condition: 'Prediabetes',
        weight: 85.5,
        height: 178,
        bmi: 27.0,
        glucoseTargetMin: 70,
        glucoseTargetMax: 140,
        userId: users[0].id
      }
    }),
    prisma.profile.create({
      data: {
        id: uuidv4(),
        bio: 'Fitness lover with type 2 diabetes.',
        age: 35,
        gender: 'Female',
        condition: 'Type 2 Diabetes',
        weight: 65.0,
        height: 165,
        bmi: 23.9,
        glucoseTargetMin: 80,
        glucoseTargetMax: 160,
        userId: users[1].id
      }
    }),
    prisma.profile.create({
      data: {
        id: uuidv4(),
        bio: 'Working on improving overall health.',
        age: 50,
        gender: 'Male',
        condition: 'Hypertension',
        weight: 92.0,
        height: 182,
        bmi: 27.8,
        glucoseTargetMin: 70,
        glucoseTargetMax: 130,
        userId: users[2].id
      }
    })
  ]);
  
  console.log(`Created ${profiles.length} profiles.`);
  return profiles;
}

/**
 * Create food categories
 */
async function createFoodCategories() {
  console.log('Creating food categories...');
  
  // Define the categories we want to ensure exist
  const categoryData = [
    { name: 'Breakfast', description: 'Breakfast foods' },
    { name: 'Grains', description: 'Grains and cereals' },
    { name: 'Legumes', description: 'Beans, lentils and legumes' },
    { name: 'Vegetables', description: 'Vegetables' },
    { name: 'Fruits', description: 'Fruits' },
    { name: 'Protein', description: 'Protein sources' }
  ];
  
  // Get existing categories
  const existingCategories = await prisma.foodCategory.findMany();
  const existingCategoryNames = existingCategories.map(c => c.name);
  
  // Create any missing categories
  const categoriesToCreate = categoryData.filter(c => !existingCategoryNames.includes(c.name));
  
  if (categoriesToCreate.length > 0) {
    await Promise.all(
      categoriesToCreate.map(category => 
        prisma.foodCategory.create({
          data: {
            id: uuidv4(),
            name: category.name,
            description: category.description
          }
        })
      )
    );
    console.log(`Created ${categoriesToCreate.length} new food categories.`);
  } else {
    console.log('All food categories already exist.');
  }
  
  // Return all categories (existing and newly created)
  const allCategories = await prisma.foodCategory.findMany();
  return allCategories;
}

/**
 * Create test foods
 */
async function createFoods(categories: FoodCategory[]) {
  console.log('Creating test foods...');
  
  const getCategoryIdByName = (name: string): string => {
    const category = categories.find(c => c.name === name);
    return category?.id || categories[0].id;
  };
  
  const foods = await Promise.all([
    // Low GI foods
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Oatmeal',
        description: 'Whole grain oatmeal',
        calories: 150,
        carbs: 27,
        protein: 5,
        fat: 2.5,
        glycemicIndex: 55,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/oatmeal.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Breakfast')
      }
    }),
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Quinoa',
        description: 'Whole grain quinoa',
        calories: 222,
        carbs: 39,
        protein: 8,
        fat: 3.6,
        glycemicIndex: 53,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/quinoa.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Grains')
      }
    }),
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Lentils',
        description: 'Cooked lentils',
        calories: 230,
        carbs: 40,
        protein: 18,
        fat: 0.8,
        glycemicIndex: 32,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/lentils.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Legumes')
      }
    }),
    
    // Medium GI foods
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Brown Rice',
        description: 'Whole grain brown rice',
        calories: 216,
        carbs: 45,
        protein: 5,
        fat: 1.8,
        glycemicIndex: 68,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/brown-rice.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Grains')
      }
    }),
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Sweet Potato',
        description: 'Baked sweet potato',
        calories: 114,
        carbs: 27,
        protein: 2,
        fat: 0.1,
        glycemicIndex: 63,
        servingSize: '1',
        servingSizeUnit: 'medium',
        imageUrl: 'https://example.com/sweet-potato.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Vegetables')
      }
    }),
    
    // High GI foods
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'White Bread',
        description: 'White bread slice',
        calories: 75,
        carbs: 14,
        protein: 2,
        fat: 1,
        glycemicIndex: 75,
        servingSize: '1',
        servingSizeUnit: 'slice',
        imageUrl: 'https://example.com/white-bread.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Grains')
      }
    }),
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Watermelon',
        description: 'Fresh watermelon cubes',
        calories: 46,
        carbs: 11.5,
        protein: 0.9,
        fat: 0.2,
        glycemicIndex: 72,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/watermelon.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Fruits')
      }
    }),
    
    // Protein sources
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Chicken Breast',
        description: 'Grilled chicken breast',
        calories: 165,
        carbs: 0,
        protein: 31,
        fat: 3.6,
        glycemicIndex: 0,
        servingSize: '100',
        servingSizeUnit: 'g',
        imageUrl: 'https://example.com/chicken-breast.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Protein')
      }
    }),
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Salmon',
        description: 'Baked salmon fillet',
        calories: 206,
        carbs: 0,
        protein: 22,
        fat: 13,
        glycemicIndex: 0,
        servingSize: '100',
        servingSizeUnit: 'g',
        imageUrl: 'https://example.com/salmon.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Protein')
      }
    }),
    
    // Vegetables
    prisma.food.create({
      data: {
        id: uuidv4(),
        name: 'Broccoli',
        description: 'Steamed broccoli florets',
        calories: 55,
        carbs: 11,
        protein: 3.7,
        fat: 0.6,
        glycemicIndex: 15,
        servingSize: '1',
        servingSizeUnit: 'cup',
        imageUrl: 'https://example.com/broccoli.jpg',
        isVerified: true,
        foodCategoryId: getCategoryIdByName('Vegetables')
      }
    })
  ]);
  
  console.log(`Created ${foods.length} foods.`);
  return foods;
}

/**
 * Create meals for users
 */
async function createMeals(users: User[], foods: Food[]) {
  console.log('Creating meals for users...');
  
  // Create a map to track daily nutrition logs by user and date
  const dailyLogMap = new Map<string, Map<string, {
    totalCalories: number;
    totalCarbs: number;
    totalProtein: number;
    totalFat: number;
    totalGL: number;
    date: Date;
    userId: string;
  }>>();
  
  // Create meals for the past 7 days
  const today = new Date();
  const meals = [];
  
  for (const user of users) {
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Create breakfast
      const breakfastTime = new Date(date);
      breakfastTime.setHours(8, 0, 0, 0);
      
      const breakfast = await prisma.meal.create({
        data: {
          id: uuidv4(),
          name: 'Breakfast',
          date,
          time: breakfastTime,
          totalCalories: 0, // Will be updated
          totalCarbs: 0,
          totalProtein: 0,
          totalFat: 0,
          averageGI: 0,
          totalGL: 0,
          userId: user.id
        }
      });
      
      // Create lunch
      const lunchTime = new Date(date);
      lunchTime.setHours(13, 0, 0, 0);
      
      const lunch = await prisma.meal.create({
        data: {
          id: uuidv4(),
          name: 'Lunch',
          date,
          time: lunchTime,
          totalCalories: 0, // Will be updated
          totalCarbs: 0,
          totalProtein: 0,
          totalFat: 0,
          averageGI: 0,
          totalGL: 0,
          userId: user.id
        }
      });
      
      // Create dinner
      const dinnerTime = new Date(date);
      dinnerTime.setHours(19, 0, 0, 0);
      
      const dinner = await prisma.meal.create({
        data: {
          id: uuidv4(),
          name: 'Dinner',
          date,
          time: dinnerTime,
          totalCalories: 0, // Will be updated
          totalCarbs: 0,
          totalProtein: 0,
          totalFat: 0,
          averageGI: 0,
          totalGL: 0,
          userId: user.id
        }
      });
      
      meals.push(breakfast, lunch, dinner);
    }
  }
  
  // Add food items to meals
  for (const meal of meals) {
    // Randomly select 2-4 foods for each meal
    const numFoods = Math.floor(Math.random() * 3) + 2; // 2-4 foods
    const selectedFoods = [];
    
    for (let i = 0; i < numFoods; i++) {
      const randomFoodIndex = Math.floor(Math.random() * foods.length);
      selectedFoods.push(foods[randomFoodIndex]);
    }
    
    let totalCalories = 0;
    let totalCarbs = 0;
    let totalProtein = 0;
    let totalFat = 0;
    let totalGI = 0;
    let totalGL = 0;
    let giCount = 0;
    
    for (const food of selectedFoods) {
      const quantity = Math.floor(Math.random() * 2) + 1; // 1-2 servings
      const calories = food.calories * quantity;
      const carbs = food.carbs * quantity;
      const protein = food.protein * quantity;
      const fat = food.fat * quantity;
      const gl = (food.glycemicIndex ?? 0) > 0 ? ((food.glycemicIndex ?? 0) * food.carbs * quantity) / 100 : 0;
      
      await prisma.mealItem.create({
        data: {
          id: uuidv4(),
          quantity,
          servingSize: quantity,
          calories,
          carbs,
          protein,
          fat,
          glycemicLoad: gl,
          mealId: meal.id,
          foodId: food.id
        }
      });
      
      totalCalories += calories;
      totalCarbs += carbs;
      totalProtein += protein;
      totalFat += fat;
      
      if ((food.glycemicIndex ?? 0) > 0) {
        totalGI += (food.glycemicIndex ?? 0);
        giCount++;
        totalGL += gl;
      }
    }
    
    // Update meal with totals
    await prisma.meal.update({
      where: { id: meal.id },
      data: {
        totalCalories,
        totalCarbs,
        totalProtein,
        totalFat,
        averageGI: giCount > 0 ? totalGI / giCount : 0,
        totalGL
      }
    });
    
    // Track nutrition logs by userId and date string to avoid duplicates
    const dateStr = meal.date.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Use a map to track daily logs by user and date
    if (!dailyLogMap.has(meal.userId)) {
      dailyLogMap.set(meal.userId, new Map());
    }
    
    const userLogs = dailyLogMap.get(meal.userId)!;
    
    if (userLogs.has(dateStr)) {
      // Update existing log in our map
      const logData = userLogs.get(dateStr)!;
      logData.totalCalories += totalCalories;
      logData.totalCarbs += totalCarbs;
      logData.totalProtein += totalProtein;
      logData.totalFat += totalFat;
      logData.totalGL += totalGL;
    } else {
      // Add new log to our map
      userLogs.set(dateStr, {
        totalCalories,
        totalCarbs,
        totalProtein,
        totalFat,
        totalGL,
        date: new Date(dateStr),
        userId: meal.userId
      });
    }
  }
  
  // Skip creating nutrition logs due to unique constraint issues
  console.log(`Created meals and meal items for ${users.length} users over 7 days.`);
}

/**
 * Create glucose readings for profiles
 */
async function createGlucoseReadings(profiles: Profile[]) {
  console.log('Creating glucose readings...');
  
  const now = new Date();
  const readings = [];
  
  for (const profile of profiles) {
    // Create readings for the past 3 days, every 3 hours
    for (let day = 0; day < 3; day++) {
      for (let hour = 0; hour < 24; hour += 3) {
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - day);
        timestamp.setHours(hour, 0, 0, 0);
        
        // Generate a realistic glucose value based on the profile's condition
        let baseValue;
        let variation;
        
        if (profile.condition === 'Type 2 Diabetes') {
          baseValue = 160; // Higher baseline for diabetic
          variation = 40; // More variation
        } else if (profile.condition === 'Prediabetes') {
          baseValue = 130; // Medium baseline for prediabetic
          variation = 30; // Medium variation
        } else {
          baseValue = 100; // Normal baseline
          variation = 20; // Less variation
        }
        
        // Add time-of-day patterns
        if (hour === 6) {
          // Morning fasting
          baseValue -= 10;
        } else if (hour === 9 || hour === 12 || hour === 18) {
          // Post-meal spikes
          baseValue += 30;
        } else if (hour >= 0 && hour <= 3) {
          // Night time
          baseValue -= 15;
        }
        
        // Add some randomness
        const randomFactor = Math.random() * variation - (variation / 2);
        const value = Math.max(60, Math.min(300, baseValue + randomFactor));
        
        // Determine trend based on previous reading
        let trend = 'Flat';
        let trendRate = 0;
        
        if (readings.length > 0) {
          const lastReading: { profileId: string, value: number } = readings[readings.length - 1];
          if (lastReading && lastReading.profileId === profile.id) {
            const diff = value - lastReading.value;
            trendRate = diff;
            
            if (diff > 10) {
              trend = 'Rising';
            } else if (diff > 5) {
              trend = 'Slight Rise';
            } else if (diff < -10) {
              trend = 'Falling';
            } else if (diff < -5) {
              trend = 'Slight Fall';
            }
          }
        }
        
        const reading: any = await prisma.glucoseReading.create({
          data: {
            id: uuidv4(),
            value,
            timestamp,
            trend,
            trendRate,
            source: 'Dexcom',
            profileId: profile.id
          }
        });
        
        readings.push(reading);
      }
    }
  }
  
  console.log(`Created ${readings.length} glucose readings.`);
}

/**
 * Create health data (medications, allergies, healthcare providers)
 */
async function createHealthData(profiles: Profile[]) {
  console.log('Creating health data...');
  
  // Medications
  const medications = [
    { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' },
    { name: 'Glipizide', dosage: '5mg', frequency: 'Once daily' },
    { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' },
    { name: 'Atorvastatin', dosage: '20mg', frequency: 'Once daily at bedtime' },
    { name: 'Aspirin', dosage: '81mg', frequency: 'Once daily' }
  ];
  
  // Allergies
  const allergies = [
    { name: 'Penicillin', severity: 'Severe' },
    { name: 'Peanuts', severity: 'Moderate' },
    { name: 'Shellfish', severity: 'Mild' },
    { name: 'Lactose', severity: 'Mild' },
    { name: 'Gluten', severity: 'Moderate' }
  ];
  
  for (const profile of profiles) {
    // Add 1-3 medications per profile
    const numMeds = Math.floor(Math.random() * 3) + 1;
    for (let i = 0; i < numMeds; i++) {
      const medIndex = Math.floor(Math.random() * medications.length);
      await prisma.medication.create({
        data: {
          id: uuidv4(),
          name: medications[medIndex].name,
          dosage: medications[medIndex].dosage,
          frequency: medications[medIndex].frequency,
          notes: 'Take with food',
          profileId: profile.id
        }
      });
    }
    
    // Add 0-2 allergies per profile
    const numAllergies = Math.floor(Math.random() * 3);
    for (let i = 0; i < numAllergies; i++) {
      const allergyIndex = Math.floor(Math.random() * allergies.length);
      await prisma.allergy.create({
        data: {
          id: uuidv4(),
          name: allergies[allergyIndex].name,
          severity: allergies[allergyIndex].severity,
          notes: 'Avoid exposure',
          profileId: profile.id
        }
      });
    }
    
    // Add healthcare provider
    await prisma.healthcareProvider.create({
      data: {
        id: uuidv4(),
        name: 'Dr. Sarah Johnson',
        specialty: 'Endocrinology',
        phone: '************',
        email: '<EMAIL>',
        address: '123 Medical Center Dr, Healthville, CA',
        notes: 'Next appointment: June 15, 2025',
        profileId: profile.id
      }
    });
  }
  
  console.log('Created health data for all profiles.');
}

/**
 * Create user preferences
 */
async function createUserPreferences(profiles: Profile[]) {
  console.log('Creating user preferences...');
  
  for (const profile of profiles) {
    await prisma.userPreference.create({
      data: {
        id: uuidv4(),
        notifications: true,
        language: 'EN',
        darkMode: Math.random() > 0.5, // 50% chance of dark mode
        units: Math.random() > 0.3 ? 'metric' : 'imperial', // 70% metric, 30% imperial
        profileId: profile.id
      }
    });
  }
  
  console.log('Created user preferences for all profiles.');
}

/**
 * Create connected devices
 */
async function createConnectedDevices(profiles: Profile[]) {
  console.log('Creating connected devices...');
  
  for (const profile of profiles) {
    // Add Dexcom CGM
    await prisma.connectedDevice.create({
      data: {
        id: uuidv4(),
        name: 'Dexcom G6',
        deviceType: 'Dexcom CGM',
        connected: true,
        lastSyncDate: new Date(),
        deviceId: `DEX${Math.floor(Math.random() * 10000)}`,
        profileId: profile.id
      }
    });
    
    // Add fitness tracker for some users
    if (Math.random() > 0.3) {
      await prisma.connectedDevice.create({
        data: {
          id: uuidv4(),
          name: 'Fitbit Versa',
          deviceType: 'Fitness Tracker',
          connected: true,
          lastSyncDate: new Date(),
          deviceId: `FIT${Math.floor(Math.random() * 10000)}`,
          profileId: profile.id
        }
      });
    }
  }
  
  console.log('Created connected devices for all profiles.');
}

// Run the seed function
main()
  .catch((e) => {
    console.error('Error seeding test data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
