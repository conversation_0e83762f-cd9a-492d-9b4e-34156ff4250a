#!/bin/bash

# Database configuration
DB_NAME="prevently"
DB_USER="postgres"

echo "Creating database $DB_NAME..."
# This will prompt for the password
PGPASSWORD=root psql -U $DB_USER -c "CREATE DATABASE $DB_NAME;" || echo "Database already exists or couldn't be created. Continuing..."

echo "Database creation attempted. Now generating Prisma client..."
npx prisma generate

echo "Running initial migration..."
npx prisma migrate dev --name init

echo "Setup complete!"
