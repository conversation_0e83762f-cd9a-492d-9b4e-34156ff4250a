#!/bin/bash

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL is not installed. Please install it first."
    exit 1
fi

# Database configuration
DB_NAME="prevently"
DB_USER="postgres"  # PostgreSQL username
DB_PASSWORD="root"    # PostgreSQL password

# Create database if it doesn't exist
echo "Creating database $DB_NAME if it doesn't exist..."
psql -U $DB_USER -c "CREATE DATABASE $DB_NAME;" || echo "Database already exists or couldn't be created. Continuing..."

# Update .env file with correct database URL
echo "Updating .env file with database connection string..."
if [ -f .env ]; then
    # Check if DATABASE_URL already exists in .env
    if grep -q "DATABASE_URL" .env; then
        # Replace existing DATABASE_URL
        sed -i '' "s|DATABASE_URL=.*|DATABASE_URL=\"postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME?schema=public\"|g" .env
    else
        # Add DATABASE_URL to .env
        echo "DATABASE_URL=\"postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME?schema=public\"" >> .env
    fi
else
    # Create new .env file
    echo "DATABASE_URL=\"postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/$DB_NAME?schema=public\"" > .env
    echo "PORT=3000" >> .env
    echo "NODE_ENV=development" >> .env
    echo "JWT_SECRET=your_jwt_secret_key_here" >> .env
fi

echo "Environment file updated."
echo "Please edit the .env file with your actual PostgreSQL credentials if needed."

# Run Prisma migrations
echo "Running Prisma migrations..."
npx prisma migrate dev --name init

echo "Setup complete!"
