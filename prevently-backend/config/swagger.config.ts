import swaggerJsdoc from 'swagger-jsdoc';
import { version } from '../package.json';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Prevently API Documentation',
      version,
      description: 'API documentation for the Prevently application',
      contact: {
        name: 'Prevently Support',
      },
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server',
      },
    ],
    components: {
      schemas: {
        Error: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
            },
          },
        },
      },
    },
  },
  apis: ['./routes/*.ts', './routes/**/*.ts', './controllers/*.ts', './models/*.ts', './swagger/*.ts'],
};

export const specs = swaggerJsdoc(options);
