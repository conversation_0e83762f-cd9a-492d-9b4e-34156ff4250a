import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file
dotenv.config();

const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3000', 10),
  DATABASE_URL: process.env.DATABASE_URL || 'postgresql://postgres:root@localhost:5432/prevently?schema=public',
  JWT_SECRET: process.env.JWT_SECRET || 'your_jwt_secret_key_here',
};

export default env;
