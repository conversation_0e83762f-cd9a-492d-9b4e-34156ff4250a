import prisma from '../services/prisma.service';
import env from './env.config';

const connectDB = async (): Promise<void> => {
  try {
    await prisma.$connect();
    console.log('PostgreSQL connected successfully via Prisma');
  } catch (error) {
    console.error('PostgreSQL connection error:', error);
    process.exit(1);
  }
};

const disconnectDB = async (): Promise<void> => {
  try {
    await prisma.$disconnect();
    console.log('PostgreSQL disconnected successfully');
  } catch (error) {
    console.error('PostgreSQL disconnection error:', error);
  }
};

export { connectDB, disconnectDB };
