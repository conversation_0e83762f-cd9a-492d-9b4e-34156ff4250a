import express, { Express } from 'express';
import cors from 'cors';
import swaggerUi from 'swagger-ui-express';
import { specs } from './config/swagger.config';
import env from './config/env.config';
import { connectDB, disconnectDB } from './config/db.config';
import indexRoutes from './routes/index';
import foodRoutes from './routes/food.routes';
import mealRoutes from './routes/meal.routes';
import nutritionRoutes from './routes/nutrition.routes';
import foodScanRoutes from './routes/foodScan.routes';
import aiAssistantRoutes from './routes/aiAssistant.routes';
import profileRoutes from './routes/profile.routes';
import cgmRoutes from './routes/cgm.routes';
import reminderRoutes from './routes/reminder.routes';
import authRoutes from './routes/auth.routes';
import { errorHandler, notFound } from './middlewares/error.middleware';

// Initialize express app
const app: Express = express();

// Initialize database connection
connectDB();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await disconnectDB();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDB();
  process.exit(0);
});

// Middleware
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/', indexRoutes);
app.use('/api/foods', foodRoutes);
app.use('/api/meals', mealRoutes);
app.use('/api/nutrition', nutritionRoutes);
app.use('/api/food-scan', foodScanRoutes);
app.use('/api/ai-assistant', aiAssistantRoutes);
app.use('/api/profile', profileRoutes);
app.use('/api/cgm', cgmRoutes);
app.use('/api/reminders', reminderRoutes);
app.use('/api/auth', authRoutes);

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, { explorer: true }));

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
const PORT = env.PORT;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running in ${env.NODE_ENV} mode on http://0.0.0.0:${PORT}`);
  console.log(`Access from other devices on your network at http://<your-ip-address>:${PORT}`);
});

export default app;
