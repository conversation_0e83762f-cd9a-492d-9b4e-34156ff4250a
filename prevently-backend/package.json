{"name": "prevently-backend", "version": "1.0.0", "main": "server.ts", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node server.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Backend API for Prevently application", "dependencies": {"@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "prisma": "^6.8.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.18", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}